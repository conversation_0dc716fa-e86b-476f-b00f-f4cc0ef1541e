<template>
  <ElDrawer
    v-model="visible"
    :title="`附件详情 - ${attachmentInfo?.file_name || ''}`"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="attachment-detail-drawer">
      <!-- 基本信息 -->
      <ElCard shadow="never" class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="文件名">{{
            attachmentInfo?.file_name || '-'
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="文件大小">{{
            formatFileSize(attachmentInfo?.file_size)
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="MIME类型">{{
            attachmentInfo?.mime_type || '-'
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="存储类型">{{
            getStorageTypeName(attachmentInfo?.storage_type)
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="MD5哈希">{{
            attachmentInfo?.md5_hash || '-'
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="创建时间">{{
            attachmentInfo?.created_at
              ? formatDate(attachmentInfo.created_at, 'YYYY-MM-DD HH:mm:ss')
              : '-'
          }}</ElDescriptionsItem>
          <ElDescriptionsItem label="文件路径" :span="2">{{
            attachmentInfo?.file_path || '-'
          }}</ElDescriptionsItem>
        </ElDescriptions>
      </ElCard>

      <!-- 关联业务信息 -->
      <ElCard shadow="never" class="business-card">
        <template #header>
          <div class="card-header">
            <span>关联业务信息</span>
          </div>
        </template>
        <ElTable :data="businessData" border style="width: 100%">
          <ElTableColumn prop="attachable_type" label="业务类型" width="150">
            <template #default="{ row }">
              {{ getBusinessTypeName(row.attachable_type) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="attachable_id" label="业务ID" width="150" />
          <ElTableColumn prop="description" label="描述">
            <template #default="{ row }">
              <ElInput
                v-model="row.description"
                placeholder="请输入描述"
                @blur="handleDescriptionChange(row)"
              />
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <ElButton
                type="primary"
                size="small"
                :loading="row.saving"
                @click="handleSaveDescription(row)"
              >
                保存
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>
    </div>
  </ElDrawer>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AttachmentDetailDrawer' })

  import { ref, reactive, watch } from 'vue'
  import {
    ElDrawer,
    ElCard,
    ElDescriptions,
    ElDescriptionsItem,
    ElTable,
    ElTableColumn,
    ElInput,
    ElButton,
    ElMessage
  } from 'element-plus'
  import { getAttachmentDetail, updateAttachmentDescription } from '@/api/admin/attachment'
  import { formatDate } from '@/utils/dataprocess/format'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import type { AttachmentItem, DictionaryItem } from '@/types/api'

  interface Props {
    modelValue: boolean
    attachmentId?: string
  }

  interface BusinessDataItem extends AttachmentItem {
    relation_id?: string
    saving?: boolean
    originalDescription?: string
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    refresh: []
  }>()

  const visible = ref(false)
  const attachmentInfo = ref<AttachmentItem>()
  const businessData = ref<BusinessDataItem[]>([])
  const businessTypes = ref<DictionaryItem[]>([])
  const dictionaryStore = useDictionaryStore()

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val
      if (val && props.attachmentId) {
        fetchAttachmentDetail()
      }
    }
  )

  // 监听 visible 变化，同步到父组件
  watch(visible, (val) => {
    emit('update:modelValue', val)
  })

  // 获取附件详情
  const fetchAttachmentDetail = async () => {
    if (!props.attachmentId) return

    try {
      // 加载字典数据
      if (businessTypes.value.length === 0) {
        businessTypes.value = await dictionaryStore.fetchItemsByCode('attachment_business_type')
      }

      const data = await getAttachmentDetail(props.attachmentId)
      attachmentInfo.value = data

      // 目前只有一条业务关联数据
      businessData.value = [
        {
          ...data,
          relation_id: data.relation_id,
          saving: false,
          originalDescription: data.description
        }
      ]
    } catch (error) {
      ElMessage.error('获取附件详情失败')
    }
  }

  // 格式化文件大小
  const formatFileSize = (size: number | string | undefined) => {
    if (!size) return '-'
    const bytes = Number(size)
    if (bytes < 1024) return bytes + ' B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
    if (bytes < 1024 * 1024 * 1024) return (bytes / 1024 / 1024).toFixed(2) + ' MB'
    return (bytes / 1024 / 1024 / 1024).toFixed(2) + ' GB'
  }

  // 获取存储类型名称
  const getStorageTypeName = (type: string | undefined) => {
    if (!type) return '本地'
    const storageMap: Record<string, string> = {
      local: '本地',
      alioss: '阿里云',
      qiniu: '七牛云',
      aws: 'AWS'
    }
    return storageMap[type] || type
  }

  // 获取业务类型名称
  const getBusinessTypeName = (type: string | null | undefined): string => {
    if (!type) return '-'
    const businessType = businessTypes.value.find((item) => item.code === type)
    return businessType?.value || type
  }

  // 处理描述变化
  const handleDescriptionChange = (row: BusinessDataItem) => {
    // 记录原始值，用于取消时恢复
    if (row.description !== row.originalDescription) {
      // 描述有变化，可以在此处添加标记
    }
  }

  // 保存描述
  const handleSaveDescription = async (row: BusinessDataItem) => {
    // 检查是否有关联关系ID
    if (!row.relation_id) {
      ElMessage.warning('该附件尚未关联业务，无法更新描述')
      return
    }

    row.saving = true
    try {
      // 调用API保存描述，使用关联关系ID
      await updateAttachmentDescription(row.relation_id, { description: row.description || '' })

      row.originalDescription = row.description
      ElMessage.success('保存成功')
      emit('refresh')
    } catch (error) {
      ElMessage.error('保存失败')
      // 恢复原始值
      row.description = row.originalDescription
    } finally {
      row.saving = false
    }
  }

  // 关闭抽屉
  const handleClose = () => {
    visible.value = false
  }
</script>

<style lang="scss" scoped>
  .attachment-detail-drawer {
    .info-card {
      margin-bottom: 20px;
    }

    .business-card {
      .el-table {
        margin-top: 10px;
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
</style>
