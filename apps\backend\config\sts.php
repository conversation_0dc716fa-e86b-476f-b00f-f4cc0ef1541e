<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Alibaba Cloud STS Configuration
    |--------------------------------------------------------------------------
    |
    | 阿里云STS临时凭证配置
    | 用于生成临时访问凭证，让前端直接上传文件到OSS
    |
    */

    // RAM角色ARN (需要在阿里云控制台创建)
    // 格式: acs:ram::$accountID:role/$roleName
    'role_arn' => env('ALIOSS_STS_ROLE_ARN', ''),

    // 角色会话名称前缀
    'role_session_prefix' => env('ALIOSS_STS_SESSION_PREFIX', 'oss-upload-'),

    // STS凭证有效期（秒）- 固定为3500秒
    'duration_seconds' => 3500,

    // 权限策略配置
    'policy' => [
        // 允许的操作
        'actions' => [
            'oss:PutObject',
            'oss:PutObjectAcl',
            'oss:GetObject',
            'oss:DeleteObject',
        ],

        // 资源路径前缀（限制上传到指定目录）
        'resource_prefix' => 'attachments/',
    ],

    // 区域配置（与OSS保持一致）
    'region' => env('ALIOSS_REGION', 'cn-hangzhou'),

    // 是否启用STS（可用于快速切换）
    'enabled' => env('ALIOSS_STS_ENABLED', true),

    // 缓存配置
    'cache' => [
        // 缓存key前缀
        'prefix' => 'sts:credentials:',

        // 缓存时间（秒）- 建议比duration_seconds少300秒
        'ttl' => env('ALIOSS_STS_CACHE_TTL', 3300),
    ],
];
