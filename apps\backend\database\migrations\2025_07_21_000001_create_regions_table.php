<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pid')->default(0)->comment('父级ID');
            $table->unsignedTinyInteger('deep')->default(0)->comment('层级深度：0省1市2区县');
            $table->string('name', 50)->comment('地区名称');
            $table->string('pinyin_prefix', 10)->comment('拼音首字母');
            $table->string('pinyin', 50)->comment('拼音全拼');
            $table->string('ext_id', 12)->unique()->comment('行政区划代码');
            $table->string('ext_name', 50)->comment('完整名称');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引优化
            $table->index('pid');
            $table->index('deep');
            $table->index('name');
            $table->index('pinyin_prefix');
            $table->index(['pid', 'deep']);

            $table->comment('省市区地区数据表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('regions');
    }
};
