<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dictionary_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique()->comment('分类编码');
            $table->string('name', 100)->comment('分类名称');
            $table->string('description', 500)->nullable()->comment('分类描述');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->boolean('is_enabled')->default(true)->comment('是否启用');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
            // $table->softDeletes();

            // 索引
            $table->index('is_enabled');
            $table->index('sort');
        });

        // 添加表注释
        DB::statement("ALTER TABLE dictionary_categories COMMENT '字典分类表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dictionary_categories');
    }
};
