<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MenuPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $menuName  菜单名称
     * @param  string|null  $authMark  权限标识（可选）
     */
    public function handle(Request $request, Closure $next, string $menuName, ?string $authMark = null): Response
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['message' => '未登录'], 401);
        }

        // 超级管理员直接放行
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // 如果请求是GET请求，则直接放行
        if ($request->isMethod('GET')) {
            return $next($request);
        }

        // 跳过不需要记录的路径
        $skipMark = [
            'checkin-records.store'
        ];
        if (in_array($authMark, $skipMark)) {
            return $next($request);
        }

        // 如果只检查菜单访问权限
        if (! $authMark) {
            $menu = \App\Models\Menu::where('name', $menuName)->first();
            if (! $menu) {
                return response()->json(['message' => '菜单不存在'], 404);
            }

            if (! $user->hasMenuAccess($menu->id)) {
                return response()->json(['message' => '没有访问该菜单的权限'], 403);
            }
        } else {
            // 检查具体的菜单权限
            if (! $user->hasMenuPermissionByMark($menuName, $authMark)) {
                return response()->json(['message' => '没有执行该操作的权限'], 403);
            }
        }

        return $next($request);
    }
}
