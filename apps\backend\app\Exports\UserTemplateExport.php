<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UserTemplateExport implements FromArray, WithColumnWidths, WithHeadings, WithStyles, WithTitle
{
    /**
     * 模板数据（默认不含示例数据）
     */
    public function array(): array
    {
        return [];
    }

    /**
     * 标题行
     */
    public function headings(): array
    {
        return [
            '用户账号',
            '密码',
            '用户昵称',
            '邮箱',
            '用户角色',
        ];
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:E1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // 设置行高
        $sheet->getRowDimension(1)->setRowHeight(25);
        // 自动换行
        $sheet->getStyle('A:E')->getAlignment()->setWrapText(true);

        return $sheet;
    }

    /**
     * 列宽
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // 用户账号
            'B' => 18, // 密码
            'C' => 20, // 用户昵称
            'D' => 28, // 邮箱
            'E' => 24, // 用户角色
        ];
    }

    /**
     * 工作表标题
     */
    public function title(): string
    {
        return '用户导入模板';
    }
}
