<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('export_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('type', 50)->comment('导出类型 (asset, category, entity, user)');
            $table->string('filename')->comment('文件名');
            $table->string('status', 20)->default('pending')->comment('状态 (pending, processing, completed, failed)');
            $table->integer('total_rows')->default(0)->comment('总行数');
            $table->json('filters')->nullable()->comment('过滤条件');
            $table->json('error_details')->nullable()->comment('错误详情');
            $table->bigInteger('file_size')->nullable()->comment('文件大小(字节)');
            $table->string('file_path')->nullable()->comment('文件路径');
            $table->integer('started_at')->nullable()->comment('开始时间戳');
            $table->integer('completed_at')->nullable()->comment('完成时间戳');
            $table->unsignedBigInteger('created_by')->comment('创建人ID');
            $table->integer('created_at')->comment('创建时间戳');
            $table->integer('updated_at')->comment('更新时间戳');

            // 索引
            $table->index(['type', 'created_at'], 'idx_type_created');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index('created_by', 'idx_created_by');
            $table->index('created_at', 'idx_created_at');

            // 外键约束
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('export_tasks');
    }
};
