<?php

namespace App\Models;

use App\Models\BaseModel;

class CheckinConfigUser extends BaseModel
{
    protected $fillable = [
        'checkin_config_id',
        'user_id'
    ];

    protected $casts = [
        'checkin_config_id' => 'integer',
        'user_id' => 'integer'
    ];

    public function config()
    {
        return $this->belongsTo(CheckinConfig::class, 'checkin_config_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
