<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">最新资产</h4>
        <p class="subtitle">本月新增<span class="text-success">+15台</span></p>
      </div>
      <el-radio-group v-model="timeFilter">
        <el-radio-button value="本月" label="本月"></el-radio-button>
        <el-radio-button value="上月" label="上月"></el-radio-button>
        <el-radio-button value="今年" label="今年"></el-radio-button>
      </el-radio-group>
    </div>
    <ArtTable
      class="table"
      :data="assetData"
      :table-config="{
        size: 'large'
      }"
    >
      <template #default>
        <el-table-column label="资产信息" prop="asset_code" width="180px">
          <template #default="scope">
            <div class="asset-info">
              <div class="asset-code">{{ scope.row.asset_code }}</div>
              <div class="asset-name">{{ scope.row.asset_name }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="asset_type" width="100px" />
        <el-table-column label="所属相关方" prop="entity_name" width="120px" />
        <el-table-column label="地区" prop="region_name" width="130px" />
        <el-table-column label="添加时间" prop="created_at" width="140px">
          <template #default="scope">
            {{ formatDate(scope.row.created_at, 'MM-DD HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80px">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </template>
    </ArtTable>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { formatDate } from '@/utils/dataprocess/format'

  const timeFilter = ref('本月')

  // 资产状态类型映射
  const getStatusType = (status: string) => {
    const statusMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
      在线: 'success',
      离线: 'info',
      故障: 'danger',
      维护中: 'warning'
    }
    return statusMap[status] || 'info'
  }

  // 生成时间戳（最近7天的随机时间）
  const generateRecentTimestamp = (daysAgo: number) => {
    const now = Math.floor(Date.now() / 1000)
    const dayInSeconds = 24 * 60 * 60
    const randomHours = Math.floor(Math.random() * 24 * 60 * 60)
    return now - daysAgo * dayInSeconds + randomHours
  }

  const assetData = reactive([
    {
      asset_code: 'AS-2024-001',
      asset_name: '生产设备A1',
      asset_type: '生产设备',
      entity_name: '北京分公司',
      region_name: '北京市朝阳区',
      status: '在线',
      created_at: generateRecentTimestamp(0)
    },
    {
      asset_code: 'AS-2024-002',
      asset_name: '监控摄像头B2',
      asset_type: '监控设备',
      entity_name: '上海工厂',
      region_name: '上海市浦东新区',
      status: '在线',
      created_at: generateRecentTimestamp(1)
    },
    {
      asset_code: 'AS-2024-003',
      asset_name: '温度传感器C3',
      asset_type: '监测设备',
      entity_name: '深圳研发中心',
      region_name: '广东省深圳市',
      status: '离线',
      created_at: generateRecentTimestamp(1)
    },
    {
      asset_code: 'AS-2024-004',
      asset_name: '质检设备D4',
      asset_type: '测试设备',
      entity_name: '苏州制造部',
      region_name: '江苏省苏州市',
      status: '维护中',
      created_at: generateRecentTimestamp(2)
    },
    {
      asset_code: 'AS-2024-005',
      asset_name: '包装机器人E5',
      asset_type: '自动化设备',
      entity_name: '天津分厂',
      region_name: '天津市滨海新区',
      status: '在线',
      created_at: generateRecentTimestamp(3)
    },
    {
      asset_code: 'AS-2024-006',
      asset_name: '安全门禁F6',
      asset_type: '安防设备',
      entity_name: '武汉办事处',
      region_name: '湖北省武汉市',
      status: '故障',
      created_at: generateRecentTimestamp(4)
    },
    {
      asset_code: 'AS-2024-007',
      asset_name: '数据服务器G7',
      asset_type: 'IT设备',
      entity_name: '杭州技术部',
      region_name: '浙江省杭州市',
      status: '在线',
      created_at: generateRecentTimestamp(5)
    },
    {
      asset_code: 'AS-2024-008',
      asset_name: '环境监测仪H8',
      asset_type: '监测设备',
      entity_name: '成都分公司',
      region_name: '四川省成都市',
      status: '在线',
      created_at: generateRecentTimestamp(6)
    }
  ])
</script>

<style lang="scss">
  .card {
    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      color: var(--el-color-primary) !important;
      background: transparent !important;
    }
  }
</style>

<style lang="scss" scoped>
  .card {
    width: 100%;
    height: 510px;
    overflow: hidden;

    .card-header {
      padding-left: 25px !important;
    }

    :deep(.el-table__body tr:last-child td) {
      border-bottom: none !important;
    }

    .asset-info {
      .asset-code {
        font-size: 13px;
        font-weight: 600;
        line-height: 1.2;
        color: var(--el-text-color-primary);
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        line-height: 1.2;
        color: var(--el-text-color-regular);
      }
    }

    .el-tag {
      font-size: 11px;
      border-radius: 4px;
    }
  }
</style>
