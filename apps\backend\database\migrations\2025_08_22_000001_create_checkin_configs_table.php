<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkin_configs', function (Blueprint $table) {
            $table->id()->comment('配置ID');
            $table->unsignedBigInteger('attachable_id')->comment('业务表主键');
            $table->string('attachable_type', 50)->comment('业务表类型');
            $table->bigInteger('checkin_time')->nullable()->comment('打卡时间');
            $table->tinyInteger('status')->default(1)->comment('是否开启打卡：1-启用，0-关闭');
            $table->tinyInteger('is_photo')->default(0)->comment('是否拍照：0-否，1-是');
            $table->string('location', 255)->nullable()->comment('打卡地点');
            $table->decimal('longitude', 10, 6)->nullable()->comment('打卡位置，经度');
            $table->decimal('latitude', 10, 6)->nullable()->comment('打卡位置，纬度');
            $table->integer('location_range')->nullable()->comment('打卡位置范围，离打卡地点的距离，单位：米');
            $table->unsignedBigInteger('created_by')->comment('创建人ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkin_configs');
    }
};
