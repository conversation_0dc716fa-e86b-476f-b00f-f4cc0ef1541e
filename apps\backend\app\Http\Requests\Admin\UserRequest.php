<?php

namespace App\Http\Requests\Admin;

use App\Enums\AdminStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        $userId = $user?->id;

        $rules = [
            'nickname' => [
                'required',
                'string',
                'max:255',
            ],
            'email' => [
                'nullable',
                'email',
                'max:255',
                Rule::unique('users')->ignore($userId),
            ],
            'account' => [
                'required',
                'string',
                'max:255',
                Rule::unique('users')->ignore($userId),
            ],
            'status' => ['required', Rule::in(AdminStatus::values())],
            'avatar_id' => 'nullable|integer|exists:attachments,id',
        ];

        // 创建时密码必填，更新时可选
        if ($this->isMethod('POST')) {
            $rules['password'] = 'required|string|min:6|max:255';
        } else {
            $rules['password'] = 'nullable|string|min:6|max:255';
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'account.required' => '用户名不能为空',
            'account.unique' => '用户名已存在',
            'account.max' => '用户名不能超过255个字符',
            'password.required' => '密码不能为空',
            'password.min' => '密码不能少于6个字符',
            'password.max' => '密码不能超过255个字符',
            'email.email' => '邮箱格式不正确',
            'email.unique' => '邮箱已存在',
            'nickname.required' => '昵称不能为空',
            'nickname.max' => '昵称不能超过255个字符',
            'status.required' => '状态不能为空',
            'status.in' => '状态值无效',
        ];
    }

    /**
     * 准备验证数据
     */
    protected function prepareForValidation(): void
    {
        // 如果是更新且密码为空，则移除密码字段
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            if (empty($this->password)) {
                $this->request->remove('password');
            }
        }
    }
}
