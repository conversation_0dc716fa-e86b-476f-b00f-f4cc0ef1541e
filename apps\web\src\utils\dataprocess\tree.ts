/**
 * 树形数据处理工具
 */

/**
 * 通用树形节点接口
 */
export interface TreeNode {
  id?: number
  parent_id?: number | null
  children?: TreeNode[]
  [key: string]: any
}

/**
 * 构建树形结构的配置选项
 */
export interface BuildTreeOptions {
  /**
   * ID字段名，默认 'id'
   */
  idKey?: string
  /**
   * 父ID字段名，默认 'parent_id'
   */
  parentKey?: string
  /**
   * 子节点字段名，默认 'children'
   */
  childrenKey?: string
  /**
   * 根节点的父ID值，可以是 null、0 或其他值
   * 默认会自动检测 null 和 0
   */
  rootParentValue?: any
}

/**
 * 将扁平数组转换为树形结构
 * @param flatData 扁平数组数据
 * @param options 配置选项
 * @returns 树形结构数组
 */
export function buildTree<T extends Record<string, any>>(
  flatData: T[],
  options: BuildTreeOptions = {}
): T[] {
  const {
    idKey = 'id',
    parentKey = 'parent_id',
    childrenKey = 'children',
    rootParentValue
  } = options

  const map = new Map<any, T>()
  const tree: T[] = []

  // 初始化所有节点，每个节点都有children数组
  flatData.forEach((item) => {
    const id = item[idKey]
    map.set(id, { ...item, [childrenKey]: item[childrenKey] || [] })
  })

  // 构建树形结构
  flatData.forEach((item) => {
    const id = item[idKey]
    const parentId = item[parentKey]
    const node = map.get(id)!

    // 判断是否为根节点
    const isRoot =
      rootParentValue !== undefined
        ? parentId === rootParentValue
        : parentId === null || parentId === 0

    if (isRoot) {
      tree.push(node)
    } else {
      const parent = map.get(parentId)
      if (parent) {
        if (!parent[childrenKey]) {
          ;(parent as any)[childrenKey] = []
        }
        ;(parent[childrenKey] as T[]).push(node)
      }
    }
  })

  return tree
}

/**
 * 将树形结构转换为扁平数组
 * @param tree 树形结构数组
 * @param options 配置选项
 * @returns 扁平数组
 */
export function flattenTree<T extends Record<string, any>>(
  tree: T[],
  options: Pick<BuildTreeOptions, 'childrenKey'> = {}
): T[] {
  const { childrenKey = 'children' } = options
  const result: T[] = []

  function traverse(nodes: T[]) {
    nodes.forEach((node) => {
      const { [childrenKey]: children, ...rest } = node
      result.push(rest as T)
      if (children && Array.isArray(children)) {
        traverse(children)
      }
    })
  }

  traverse(tree)
  return result
}

/**
 * 在树形结构中查找节点
 * @param tree 树形结构数组
 * @param predicate 查找条件函数
 * @param options 配置选项
 * @returns 找到的节点或 undefined
 */
export function findInTree<T extends Record<string, any>>(
  tree: T[],
  predicate: (node: T) => boolean,
  options: Pick<BuildTreeOptions, 'childrenKey'> = {}
): T | undefined {
  const { childrenKey = 'children' } = options

  for (const node of tree) {
    if (predicate(node)) {
      return node
    }
    if (node[childrenKey] && Array.isArray(node[childrenKey])) {
      const found = findInTree(node[childrenKey], predicate, options)
      if (found) {
        return found
      }
    }
  }

  return undefined
}

/**
 * 过滤树形结构
 * @param tree 树形结构数组
 * @param predicate 过滤条件函数
 * @param options 配置选项
 * @returns 过滤后的树形结构
 */
export function filterTree<T extends Record<string, any>>(
  tree: T[],
  predicate: (node: T) => boolean,
  options: Pick<BuildTreeOptions, 'childrenKey'> = {}
): T[] {
  const { childrenKey = 'children' } = options

  return tree
    .map((node) => {
      const children = node[childrenKey]
      if (children && Array.isArray(children)) {
        const filteredChildren = filterTree(children, predicate, options)
        if (filteredChildren.length > 0) {
          return { ...node, [childrenKey]: filteredChildren }
        }
      }
      return predicate(node) ? { ...node, [childrenKey]: [] } : null
    })
    .filter((node): node is T => node !== null)
}

/**
 * 遍历树形结构
 * @param tree 树形结构数组
 * @param callback 遍历回调函数
 * @param options 配置选项
 */
export function traverseTree<T extends Record<string, any>>(
  tree: T[],
  callback: (node: T, parent?: T, level?: number) => void,
  options: Pick<BuildTreeOptions, 'childrenKey'> = {}
): void {
  const { childrenKey = 'children' } = options

  function traverse(nodes: T[], parent?: T, level = 0) {
    nodes.forEach((node) => {
      callback(node, parent, level)
      if (node[childrenKey] && Array.isArray(node[childrenKey])) {
        traverse(node[childrenKey], node, level + 1)
      }
    })
  }

  traverse(tree)
}
