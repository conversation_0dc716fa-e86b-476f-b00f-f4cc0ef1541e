<!-- 系统logo -->
<template>
  <div class="art-logo">
    <img :style="logoStyle" :src="logoSrc" alt="logo" />
  </div>
</template>

<script setup lang="ts">
  import { useConfigStore } from '@/store/modules/config'

  defineOptions({ name: 'ArtLogo' })

  interface Props {
    /** logo 大小 */
    size?: number | string
    /** 自定义logo地址 */
    src?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    size: 36
  })

  const configStore = useConfigStore()

  const logoStyle = computed(() => ({ width: `${props.size}px` }))

  // 动态获取logo地址，优先级：props.src > 配置中的logo > 默认logo
  const logoSrc = computed(() => {
    if (props.src) return props.src

    const systemLogo = configStore.config.system.system_logo
    if (systemLogo && typeof systemLogo === 'object' && systemLogo.file_url) {
      return systemLogo.file_url
    }

    // 默认logo
    return new URL('@/assets/img/common/logo.webp', import.meta.url).href
  })
</script>

<style lang="scss" scoped>
  .art-logo {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
    }
  }
</style>
