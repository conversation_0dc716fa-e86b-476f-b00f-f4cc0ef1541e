/**
 * 分页链接
 */
export interface PaginationLinks {
  first: string | null
  last: string | null
  prev: string | null
  next: string | null
}

/**
 * 分页元数据
 */
export interface PaginationMeta {
  total: number
  per_page: number
  current_page: number
  last_page: number
  from: number | null
  to: number | null
  path: string
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

/**
 * 分页响应基础接口（Laravel 标准格式）
 */
export interface PaginatedResponse<T> {
  data: T[]
  links: PaginationLinks
  meta: PaginationMeta
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page?: number
  per_page?: number
}

/**
 * 带分页的搜索参数基础接口
 */
export interface PaginatedSearchParams extends PaginationParams {
  [key: string]: any
}

/**
 * 将旧的分页参数转换为新的格式
 * @deprecated 后续应直接使用 per_page
 */
export function transformPaginationParams(params: {
  current?: number
  size?: number
  page?: number
  per_page?: number
}): PaginationParams {
  return {
    page: params.page || params.current || 1,
    per_page: params.per_page || params.size || 20
  }
}

/**
 * 从分页响应中提取用于 Element Plus Table 的分页配置
 */
export function extractTablePagination(response: PaginatedResponse<any>) {
  return {
    total: response.meta.total,
    pageSize: response.meta.per_page,
    currentPage: response.meta.current_page
  }
}
