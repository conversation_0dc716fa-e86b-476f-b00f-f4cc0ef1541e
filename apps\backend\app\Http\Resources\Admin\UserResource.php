<?php

namespace App\Http\Resources\Admin;

use App\Enums\AttachmentCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use function App\Support\string_to_timestamp;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // 获取用户头像附件 - 从已加载的关系中筛选
        $avatar = null;
        if ($this->relationLoaded('attachments')) {
            $avatar = $this->attachments->filter(function ($attachment) {
                return $attachment->pivot->category === AttachmentCategory::AVATAR->value;
            })->first();
        }

        return [
            'id' => $this->id,
            'account' => $this->account,
            'nickname' => $this->nickname,
            'email' => $this->email,
            'avatar' => $avatar ? $avatar->file_url : null,
            'avatar_id' => $avatar?->id,
            'status' => $this->status,
            'status_label' => $this->status === 'enable' ? '启用' : '禁用',
            'is_super_admin' => $this->is_super_admin,
            'roles' => $this->when($this->relationLoaded('roles'), function () {
                if ($this->is_super_admin == 1) {
                    return [
                        [
                            'id' => 0,
                            'name' => '平台管理员',
                        ],
                    ];
                }

                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->description,
                    ];
                });
            }),
            'created_at' => $this->created_at ? string_to_timestamp($this->created_at) : null,
            'updated_at' => $this->updated_at ? string_to_timestamp($this->updated_at) : null,
        ];
    }
}
