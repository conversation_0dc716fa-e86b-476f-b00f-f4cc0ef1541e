<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">系统动态</h4>
        <p class="subtitle"
          >今日<span class="text-success">+{{ todayCount }}</span></p
        >
      </div>
      <div class="action">
        <span class="more-link" @click="handleViewMore">查看更多</span>
      </div>
    </div>

    <div class="timeline-container">
      <div class="timeline">
        <div
          v-for="(item, index) in displayList"
          :key="index"
          class="timeline-item"
          :class="{ 'animate-in': item.animate }"
        >
          <div class="timeline-dot" :class="`dot-${item.type}`">
            <i class="iconfont" :class="item.icon"></i>
          </div>
          <div class="timeline-content">
            <div class="timeline-time">{{ item.time }}</div>
            <div class="timeline-user">{{ item.user }}</div>
            <div class="timeline-description">{{ item.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed, onMounted, nextTick } from 'vue'

  // 系统动态类型定义
  interface SystemActivity {
    id: number
    type: 'login' | 'asset' | 'device' | 'alarm' | 'lifecycle' | 'maintenance'
    icon: string
    time: string
    user: string
    description: string
    animate?: boolean
  }

  // 系统动态数据
  const activities = reactive<SystemActivity[]>([
    {
      id: 1,
      type: 'login',
      icon: '&#xe721;',
      time: '14:30',
      user: '张三',
      description: '登录系统，开始日常巡检工作'
    },
    {
      id: 2,
      type: 'asset',
      icon: '&#xe7c5;',
      time: '14:15',
      user: '李四',
      description: '新增资产【服务器-A001】，分类：IT设备'
    },
    {
      id: 3,
      type: 'device',
      icon: '&#xe724;',
      time: '14:08',
      user: '王五',
      description: '设备【传感器-S001】状态变更：正常 → 离线'
    },
    {
      id: 4,
      type: 'alarm',
      icon: '&#xe7aa;',
      time: '13:56',
      user: '赵六',
      description: '处理高温告警，设备恢复正常运行'
    },
    {
      id: 5,
      type: 'lifecycle',
      icon: '&#xe7b9;',
      time: '13:42',
      user: '孙七',
      description: '更新项目【智慧园区】跟进记录，当前阶段：实施中'
    },
    {
      id: 6,
      type: 'maintenance',
      icon: '&#xe7c6;',
      time: '13:30',
      user: '周八',
      description: '执行系统维护操作，清理日志文件'
    },
    {
      id: 7,
      type: 'asset',
      icon: '&#xe7c5;',
      time: '13:15',
      user: '吴九',
      description: '修改资产【监控摄像头-C002】位置信息'
    },
    {
      id: 8,
      type: 'login',
      icon: '&#xe721;',
      time: '13:00',
      user: '郑十',
      description: '用户登出系统，结束值班工作'
    },
    {
      id: 9,
      type: 'device',
      icon: '&#xe724;',
      time: '12:45',
      user: '钱十一',
      description: '设备【空调控制器-AC001】状态变更：故障 → 正常'
    },
    {
      id: 10,
      type: 'alarm',
      icon: '&#xe7aa;',
      time: '12:30',
      user: '陈十二',
      description: '处理网络连接告警，已恢复通信'
    }
  ])

  // 显示的动态列表（前8条）
  const displayList = computed(() => activities.slice(0, 8))

  // 今日新增数量
  const todayCount = computed(() => activities.length)

  // 查看更多处理
  const handleViewMore = () => {
    console.log('查看更多系统动态')
    // 这里可以跳转到详细的系统日志页面
  }

  // 组件挂载后添加动画效果
  onMounted(() => {
    nextTick(() => {
      displayList.value.forEach((item, index) => {
        setTimeout(() => {
          item.animate = true
        }, index * 100)
      })
    })
  })
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 510px;
    padding: 0 25px;

    .card-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 20px 0 15px;

      .title {
        .box-title {
          margin: 0 0 5px;
          font-size: 16px;
          font-weight: 600;
          color: var(--art-text-gray-900);
        }

        .subtitle {
          margin: 0;
          font-size: 12px;
          color: var(--art-text-gray-600);

          .text-success {
            font-weight: 500;
            color: #67c23a;
          }
        }
      }

      .action {
        .more-link {
          font-size: 12px;
          color: var(--main-color);
          cursor: pointer;
          transition: opacity 0.2s;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .timeline-container {
      height: calc(100% - 80px);
      padding-right: 5px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--art-border-color);
        border-radius: 2px;
      }

      .timeline {
        position: relative;
        padding-left: 25px;

        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 11px;
          width: 2px;
          content: '';
          background: linear-gradient(
            to bottom,
            var(--art-border-color) 0%,
            var(--art-border-color) 80%,
            transparent 100%
          );
        }

        .timeline-item {
          position: relative;
          margin-bottom: 25px;
          opacity: 0;
          transition: all 0.3s ease;
          transform: translateX(-10px);

          &.animate-in {
            opacity: 1;
            transform: translateX(0);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .timeline-dot {
            position: absolute;
            top: 2px;
            left: -19px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22px;
            height: 22px;
            border: 2px solid #fff;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgb(0 0 0 / 10%);

            .iconfont {
              font-size: 10px;
              color: #fff;
            }

            // 不同操作类型的颜色
            &.dot-login {
              background: #409eff;
            }

            &.dot-asset {
              background: #67c23a;
            }

            &.dot-device {
              background: #e6a23c;
            }

            &.dot-alarm {
              background: #f56c6c;
            }

            &.dot-lifecycle {
              background: #909399;
            }

            &.dot-maintenance {
              background: #303133;
            }
          }

          .timeline-content {
            padding: 12px 15px;
            background: #fff;
            border: 1px solid var(--art-border-color);
            border-radius: 6px;
            box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
            transition: box-shadow 0.2s;

            &:hover {
              box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            }

            .timeline-time {
              margin-bottom: 4px;
              font-size: 11px;
              font-weight: 500;
              color: var(--art-text-gray-500);
            }

            .timeline-user {
              margin-bottom: 4px;
              font-size: 13px;
              font-weight: 500;
              color: var(--main-color);
            }

            .timeline-description {
              font-size: 12px;
              line-height: 1.4;
              color: var(--art-text-gray-800);
            }
          }
        }
      }
    }
  }

  // 暗色主题适配
  :root[data-theme='dark'] .card {
    .timeline-container .timeline .timeline-item .timeline-content {
      background: var(--art-bg-gray-800);
      border-color: var(--art-border-color);
    }
  }
</style>
