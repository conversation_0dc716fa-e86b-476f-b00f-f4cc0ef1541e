<?php

namespace App\Services;

use App\Enums\AttachmentCategory;
use App\Models\Lifecycle;
use App\Models\LifecycleFollowUp;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

use function App\Support\is_timestamp;
use function App\Support\string_to_timestamp;

class LifecycleFollowUpService
{
    /**
     * 获取生命周期的跟进记录列表
     */
    public function getByLifecycle(int $lifecycleId): Collection
    {
        return LifecycleFollowUp::where('lifecycle_id', $lifecycleId)
            ->with(['person:id,nickname', 'attachments'])
            ->orderBy('date', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 获取跟进记录详情
     */
    public function find(int $lifecycleId, int $id): ?LifecycleFollowUp
    {
        return LifecycleFollowUp::where('lifecycle_id', $lifecycleId)
            ->with(['person:id,nickname', 'attachments'])
            ->find($id);
    }

    /**
     * 创建跟进记录
     */
    public function create(Lifecycle $lifecycle, array $data): LifecycleFollowUp
    {
        return DB::transaction(function () use ($lifecycle, $data) {
            // 验证跟进人是否是协助人员
            if (! $lifecycle->isAssistant($data['person_id'])) {
                throw new \InvalidArgumentException('跟进人必须是该生命周期的协助人员');
            }

            if (isset($data['date']) && ! is_timestamp($data['date'])) {
                $data['date'] = string_to_timestamp($data['date']);
            }

            // 创建跟进记录
            $followUp = LifecycleFollowUp::create([
                'lifecycle_id' => $lifecycle->id,
                'date' => $data['date'],
                'person_id' => $data['person_id'],
                'tag_ids' => $data['tag_ids'],
                'content' => $data['content'],
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // 处理附件
            if (! empty($data['attachments'])) {
                $this->syncAttachments($followUp, $data['attachments']);
            }

            return $followUp->load(['person:id,nickname', 'attachments']);
        });
    }

    /**
     * 更新跟进记录
     */
    public function update(LifecycleFollowUp $followUp, array $data): LifecycleFollowUp
    {
        return DB::transaction(function () use ($followUp, $data) {
            // 如果要更改跟进人，验证新的跟进人是否是协助人员
            if (isset($data['person_id']) && $data['person_id'] != $followUp->person_id) {
                $lifecycle = $followUp->lifecycle;
                if (! $lifecycle->isAssistant($data['person_id'])) {
                    throw new \InvalidArgumentException('跟进人必须是该生命周期的协助人员');
                }
            }

            // 更新跟进记录
            $followUp->update([
                'date' => $data['date'] ?? $followUp->date,
                'person_id' => $data['person_id'] ?? $followUp->person_id,
                'tag_ids' => $data['tag_ids'] ?? $followUp->tag_ids,
                'content' => $data['content'] ?? $followUp->content,
                'updated_by' => auth()->id(),
            ]);

            // 处理附件
            if (isset($data['attachments'])) {
                $this->syncAttachments($followUp, $data['attachments']);
            }

            return $followUp->load(['person:id,nickname', 'attachments']);
        });
    }

    /**
     * 删除跟进记录
     */
    public function delete(LifecycleFollowUp $followUp): bool
    {
        return $followUp->delete();
    }

    /**
     * 验证跟进记录是否属于指定的生命周期
     */
    public function validateBelongsToLifecycle(int $lifecycleId, int $followUpId): ?LifecycleFollowUp
    {
        $followUp = LifecycleFollowUp::find($followUpId);

        if (! $followUp || $followUp->lifecycle_id != $lifecycleId) {
            return null;
        }

        return $followUp;
    }

    /**
     * 同步附件
     */
    private function syncAttachments(LifecycleFollowUp $followUp, array $attachmentIds): void
    {
        $attachmentData = [];
        foreach ($attachmentIds as $index => $id) {
            $attachmentData[] = [
                'id' => $id,
                'category' => AttachmentCategory::GENERAL->value,
                'sort' => $index,
                'description' => null,
            ];
        }

        $followUp->syncAttachments($attachmentData);
    }
}
