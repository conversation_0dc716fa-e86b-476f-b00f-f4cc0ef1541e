<?php

namespace App\Exports;

use App\Models\Category;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AssetTemplateExport implements FromArray, ShouldAutoSize, WithColumnWidths, WithHeadings, WithStyles
{
    private $categories = [];

    private $categoryColumns = [];

    public function __construct()
    {
        $this->loadCategories();
    }

    /**
     * 加载分类数据
     */
    private function loadCategories()
    {
        // 获取所有启用的分类
        $this->categories = Category::active()
            ->where('parent_id', '=', 0)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        // 生成分类列
        $this->generateCategoryColumns();
    }

    /**
     * 生成分类列
     */
    private function generateCategoryColumns()
    {
        $this->categoryColumns = [];

        foreach ($this->categories as $category) {
            $level = $category->level;
            $prefix = str_repeat('  ', $level); // 用空格表示层级
            $this->categoryColumns[] = [
                'id' => $category->id,
                'name' => $prefix.$category->name,
                'code' => $category->code,
                'level' => $level,
            ];
        }
    }

    /**
     * 导出数据
     */
    public function array(): array
    {
        // 基础字段数据
        $baseData = [
            '办公台式电脑', // 资产名称
            '联想', // 品牌
            'ThinkCentre M720', // 规格型号
            'ABC123456789', // 序列号
            'purchase', // 资产来源（字典code）
            'in_use', // 资产状态（字典code）
            'brand_new', // 成色（字典code）
            '', // 主设备（附属设备时填写）
            '深圳', // 所在地区
            'XX街道XX号XX大厦', // 详细地址
            '2024-01-01', // 启用日期
            '36', // 合同质保期(月)
            '30', // 质保期预警(天)
            '90', // 维护周期(天)
            '5', // 预计使用年限(年)
            '北京凯吉特医药科技发展有限公司', // 生产厂商名称
            '顾芳', // 生产厂商联系人
            '17281619968', // 生产厂商联系方式
            '业务员', // 生产厂商职位
            '北京康达和美经贸有限公司', // 供应商名称
            '常芳', // 供应商联系人
            '13552224534', // 供应商联系方式
            '技术人员', // 供应商职位
            '北京科利达医疗设备发展有限公司', // 服务商名称
            '戴芳', // 服务商联系人
            '13204091385', // 服务商联系电话
            '业务员', // 服务商职位
            '北京科迪信生物技术开发中心', // 售后部名称
            '常芳', // 售后部联系人
            '13552224534', // 售后部联系电话
            '业务员', // 售后部职位
            '这是一台办公用台式电脑', // 备注
        ];

        // 添加分类列数据
        $categoryData = [];
        foreach ($this->categoryColumns as $index => $category) {
            // 第一个示例数据选中第一个分类，第二个示例数据选中第二个分类（如果存在）
            $categoryData[] = $category['name'];
        }

        // 合并基础数据和分类数据
        $row1 = array_merge($baseData, $categoryData);

        // 第二个示例数据
        $baseData2 = [
            '激光打印机', // 资产名称
            '惠普', // 品牌
            'HP LaserJet Pro M404n', // 规格型号
            'HP123456789', // 序列号
            'purchase', // 资产来源
            'in_use', // 资产状态
            'brand_new', // 成色
            '', // 主设备（关联到办公台式电脑）
            '广州', // 所在地区
            'XX街道XX号XX大厦', // 详细地址
            '2024-01-15', // 启用日期
            '24', // 合同质保期(月)
            '30', // 质保期预警(天)
            '180', // 维护周期(天)
            '3', // 预计使用年限(年)
            '惠普（中国）有限公司', // 生产厂商名称
            '王五', // 生产厂商联系人
            '13700137000', // 生产厂商联系方式
            '技术支持', // 生产厂商职位
            '广州惠普科技有限公司', // 供应商名称
            '李六', // 供应商联系人
            '13600136000', // 供应商联系方式
            '销售经理', // 供应商职位
            '广州惠普维修中心', // 服务商名称
            '赵七', // 服务商联系人
            '13500135000', // 服务商联系电话
            '维修工程师', // 服务商职位
            '惠普售后服务部', // 售后部名称
            '孙八', // 售后部联系人
            '13400134000', // 售后部联系电话
            '客服专员', // 售后部职位
            '办公用激光打印机', // 备注
        ];

        // 添加分类列数据（第二个示例选择不同的分类）
        $categoryData2 = [];
        foreach ($this->categoryColumns as $index => $category) {
            $categoryData2[] = $category['name'];
        }

        $row2 = array_merge($baseData2, $categoryData2);

        return [$row1, $row2];
    }

    /**
     * 表头
     */
    public function headings(): array
    {
        // 基础字段表头
        $baseHeadings = [
            '资产名称',
            '品牌',
            '规格型号',
            '序列号',
            '资产来源',
            '资产状态',
            '成色',
            '主设备',
            '所在地区',
            '详细地址',
            '启用日期',
            '合同质保期(月)',
            '质保期预警(天)',
            '维护周期(天)',
            '预计使用年限(年)',
            '生产厂商名称',
            '生产厂商联系人',
            '生产厂商联系电话',
            '生产厂商职位',
            '供应商名称',
            '供应商联系人',
            '供应商联系电话',
            '供应商职位',
            '服务商名称',
            '服务商联系人',
            '服务商联系电话',
            '服务商职位',
            '售后部名称',
            '售后部联系人',
            '售后部联系电话',
            '售后部职位',
            '备注',
        ];

        // 添加分类列表头
        $categoryHeadings = [];
        foreach ($this->categoryColumns as $category) {
            $categoryHeadings[] = $category['name'];
        }

        return array_merge($baseHeadings, $categoryHeadings);
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet)
    {
        $totalColumns = count($this->headings());
        $lastColumn = $this->getColumnLetter($totalColumns);

        // 添加说明行
        $sheet->insertNewRowBefore(1, 1);

        // 第一行：标题
        $sheet->mergeCells("A1:{$lastColumn}1");
        $sheet->setCellValue('A1', '资产导入模板');
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // 设置表头样式（第2行）
        $sheet->getStyle("A2:{$lastColumn}2")->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // 设置示例数据行样式
        $sheet->getStyle("A3:{$lastColumn}4")->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F2F2F2'],
            ],
        ]);

        // 设置所有单元格的边框
        $sheet->getStyle("A2:{$lastColumn}4")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // 调整行高
        $sheet->getRowDimension(1)->setRowHeight(30);
        $sheet->getRowDimension(2)->setRowHeight(25);
        $sheet->getRowDimension(3)->setRowHeight(20);
        $sheet->getRowDimension(4)->setRowHeight(20);

        // 设置分类列的样式（居中对齐）
        $baseColumns = 32; // 基础字段数量
        for ($i = $baseColumns + 1; $i <= $totalColumns; $i++) {
            $colLetter = $this->getColumnLetter($i);
            $sheet->getStyle("{$colLetter}2:{$colLetter}4")->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ]);
        }
    }

    /**
     * 设置列宽
     */
    public function columnWidths(): array
    {
        $widths = [
            'A' => 20, // 资产名称
            'B' => 15, // 品牌
            'C' => 20, // 规格型号
            'D' => 20, // 序列号
            'E' => 15, // 资产来源
            'F' => 15, // 资产状态
            'G' => 10, // 成色
            'H' => 15, // 主设备
            'I' => 15, // 地区代码
            'J' => 25, // 详细地址
            'K' => 15, // 启用日期
            'L' => 20, // 合同质保期
            'M' => 20, // 质保期预警
            'N' => 15, // 维护周期
            'O' => 20, // 预计使用年限
            'P' => 40, // 生产厂商名称
            'Q' => 15, // 生产厂商联系人
            'R' => 20, // 生产厂商联系电话
            'S' => 15, // 生产厂商职位
            'T' => 40, // 供应商名称
            'U' => 15, // 供应商联系人
            'V' => 20, // 供应商联系电话
            'W' => 15, // 供应商职位
            'X' => 40, // 服务商名称
            'Y' => 15, // 服务商联系人
            'Z' => 20, // 服务商联系电话
            'AA' => 15, // 服务商职位
            'AB' => 40, // 售后部名称
            'AC' => 15, // 售后部联系人
            'AD' => 20, // 售后部联系电话
            'AE' => 15, // 售后部职位
            'AF' => 30, // 备注
        ];

        // 添加分类列的宽度
        $baseColumns = 32; // 基础字段数量
        foreach ($this->categoryColumns as $index => $category) {
            $colLetter = $this->getColumnLetter($baseColumns + $index + 1);
            $widths[$colLetter] = 15; // 分类列统一宽度
        }

        return $widths;
    }

    /**
     * 获取列字母
     */
    private function getColumnLetter($columnNumber)
    {
        $letter = '';
        while ($columnNumber > 0) {
            $columnNumber--;
            $letter = chr(65 + ($columnNumber % 26)).$letter;
            $columnNumber = intval($columnNumber / 26);
        }

        return $letter;
    }
}
