<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CategoryTemplateExport implements FromArray, WithColumnWidths, WithHeadings, WithStyles, WithTitle
{
    protected bool $withSampleData;

    public function __construct(bool $withSampleData = false)
    {
        $this->withSampleData = $withSampleData;
    }

    /**
     * 返回数据数组
     */
    public function array(): array
    {
        if (! $this->withSampleData) {
            return [];
        }

        // 返回示例数据
        return [
            ['电子设备', '', 'electronic', '电子设备分类'],
            ['计算机设备', '电子设备', 'computer', '计算机相关设备'],
            ['台式机', '计算机设备', 'desktop', '台式计算机'],
            ['笔记本', '计算机设备', 'laptop', '笔记本电脑'],
            ['网络设备', '电子设备', 'network', '网络相关设备'],
            ['路由器', '网络设备', 'router', '网络路由器'],
            ['交换机', '网络设备', 'switch', '网络交换机'],
            ['医疗设备', '', 'medical', '医疗相关设备'],
            ['诊断设备', '医疗设备', 'diagnostic', '医疗诊断设备'],
            ['治疗设备', '医疗设备', 'treatment', '医疗治疗设备'],
        ];
    }

    /**
     * 返回标题行
     */
    public function headings(): array
    {
        return [
            '分类名',
            '上级分类',
            '分类编码',
            '备注',
        ];
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet)
    {
        // 设置标题行样式
        $sheet->getStyle('A1:D1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // 设置数据行样式
        if ($this->withSampleData) {
            $lastRow = count($this->array()) + 1;
            $sheet->getStyle("A2:D{$lastRow}")->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_LEFT,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC'],
                    ],
                ],
            ]);
        }

        // 设置行高
        $sheet->getRowDimension(1)->setRowHeight(25);

        // 自动换行
        $sheet->getStyle('A:D')->getAlignment()->setWrapText(true);

        return $sheet;
    }

    /**
     * 设置列宽
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // 分类名
            'B' => 20, // 上级分类
            'C' => 25, // 分类编码
            'D' => 30, // 备注
        ];
    }

    /**
     * 设置工作表标题
     */
    public function title(): string
    {
        return '分类导入模板';
    }
}
