<?php

namespace App\Http\Controllers\Admin;

use App\Exports\AssetTemplateExport;
use App\Exports\CategoryTemplateExport;
use App\Exports\EntityTemplateExport;
use App\Exports\UserTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Resources\ExportTaskResource;
use App\Models\ExportTask;
use App\Services\AssetExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * 批量导入管理
 * 处理所有类型的模板导出功能
 */
class ExportController extends Controller
{
    /**
     * 统一导出模板
     *
     * 路径参数:
     * - type: 导出类型，支持 asset/category/entity/user
     */
    public function template(string $type, Request $request): BinaryFileResponse
    {
        $type = strtolower($type);

        // 验证导出类型
        $supportedTypes = ['asset', 'category', 'entity', 'user'];
        if (!in_array($type, $supportedTypes)) {
            abort(422, '不支持的导出类型');
        }

        try {
            Log::info('开始导出模板', [
                'type' => $type,
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $export = $this->createExportInstance($type, $request);
            $filename = $this->generateFilename($type, $request);

            Log::info('模板导出成功', [
                'type' => $type,
                'filename' => $filename,
                'user_id' => auth()->id(),
            ]);

            return Excel::download($export, $filename);

        } catch (\Exception $e) {
            Log::error('模板导出失败', [
                'type' => $type,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, '导出模板失败：' . $e->getMessage());
        }
    }

    /**
     * 创建导出实例
     */
    protected function createExportInstance(string $type, Request $request)
    {
        return match ($type) {
            'asset' => new AssetTemplateExport(),
            'category' => new CategoryTemplateExport(
                $request->boolean('with_sample', false)
            ),
            'entity' => new EntityTemplateExport(),
            'user' => new UserTemplateExport(),
            default => throw new \InvalidArgumentException("不支持的导出类型: {$type}"),
        };
    }

    /**
     * 生成文件名
     */
    protected function generateFilename(string $type, Request $request): string
    {
        $typeNames = [
            'asset' => '资产',
            'category' => '分类',
            'entity' => '相关方',
            'user' => '用户',
        ];

        $typeName = $typeNames[$type] ?? $type;
        $timestamp = date('Y-m-d_H-i-s');

        // 分类导出支持示例数据选项
        if ($type === 'category' && $request->boolean('with_sample', false)) {
            return "{$typeName}导入模板_含示例数据_{$timestamp}.xlsx";
        }

        return "{$typeName}导入模板_{$timestamp}.xlsx";
    }

    /**
     * 获取支持的导出类型列表
     */
    public function types(): array
    {
        return [
            'data' => [
                [
                    'type' => 'asset',
                    'name' => '资产',
                    'description' => '资产导入模板，包含资产基本信息、分类、相关方等字段',
                    'supports_sample' => false,
                ],
                [
                    'type' => 'category',
                    'name' => '分类',
                    'description' => '分类导入模板，支持层级结构',
                    'supports_sample' => true,
                ],
                [
                    'type' => 'entity',
                    'name' => '相关方',
                    'description' => '相关方导入模板，包含相关方基本信息和联系人',
                    'supports_sample' => false,
                ],
                [
                    'type' => 'user',
                    'name' => '用户',
                    'description' => '用户导入模板，包含账号、密码、角色等信息',
                    'supports_sample' => false,
                ],
            ],
        ];
    }

    /**
     * 批量导出数据
     *
     * 路径参数:
     * - type: 导出类型
     */
    public function data(string $type, Request $request): BinaryFileResponse
    {
        $type = strtolower($type);

        // 验证导出类型
        $supportedTypes = ['asset', 'category', 'entity', 'user'];
        if (!in_array($type, $supportedTypes)) {
            abort(422, '不支持的导出类型');
        }

        try {
            Log::info('开始导出数据', [
                'type' => $type,
                'user_id' => auth()->id(),
                'filters' => $request->all(),
            ]);

            $exportService = $this->createDataExportService($type, $request);
            $filename = $exportService->generateFilename();

            Log::info('数据导出成功', [
                'type' => $type,
                'filename' => $filename,
                'user_id' => auth()->id(),
            ]);

            return Excel::download($exportService, $filename);

        } catch (\Exception $e) {
            Log::error('数据导出失败', [
                'type' => $type,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, '导出数据失败：' . $e->getMessage());
        }
    }

    /**
     * 创建数据导出服务实例
     */
    protected function createDataExportService(string $type, Request $request)
    {
        return match ($type) {
            'asset' => new AssetExportService($request),
            'category' => throw new \Exception('分类数据导出功能开发中'),
            'entity' => throw new \Exception('相关方数据导出功能开发中'),
            'user' => throw new \Exception('用户数据导出功能开发中'),
            default => throw new \InvalidArgumentException("不支持的导出类型: {$type}"),
        };
    }

    /**
     * 获取导出历史记录
     */
    public function history(Request $request)
    {
        try {
            $query = ExportTask::with('creator')
                ->where('created_by', auth()->id())
                ->orderBy('created_at', 'desc');

            // 按类型筛选
            if ($request->has('type')) {
                $query->where('type', $request->input('type'));
            }

            // 按状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            // 按时间范围筛选
            if ($request->has('created_at_start')) {
                $query->where('created_at', '>=', strtotime($request->input('created_at_start')));
            }

            if ($request->has('created_at_end')) {
                $query->where('created_at', '<=', strtotime($request->input('created_at_end')));
            }

            $tasks = $query->paginate(20);

            return ExportTaskResource::collection($tasks);

        } catch (\Exception $e) {
            Log::error('获取导出历史失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => '获取导出历史失败：' . $e->getMessage(),
                'error_code' => 'EXPORT_HISTORY_FAILED',
            ], 500);
        }
    }

    /**
     * 下载导出文件
     */
    public function download(ExportTask $exportTask): BinaryFileResponse
    {
        // 检查权限 - 只能下载自己的导出文件
        if ($exportTask->created_by !== auth()->id()) {
            abort(403, '无权限下载此文件');
        }

        // 检查文件是否存在
        if (!$exportTask->canDownload()) {
            abort(404, '文件不存在或已过期');
        }

        $filePath = storage_path('app/' . $exportTask->file_path);

        Log::info('下载导出文件', [
            'export_task_id' => $exportTask->id,
            'filename' => $exportTask->filename,
            'user_id' => auth()->id(),
        ]);

        return response()->download($filePath, $exportTask->filename);
    }
}
