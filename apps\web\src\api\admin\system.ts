import request from '@/utils/http'
import type { AppRouteRecord } from '@/types/router'
import type {
  RoleListData,
  RoleSearchParams,
  RoleForm,
  RoleListItem,
  UserRoleAssignParams,
  RolePermissionAssignParams
} from '@/types/api/role'
import type {
  AppConfig,
  UpdateConfigsRequest,
  OperationLog,
  OperationLogListData,
  OperationLogSearchParams
} from '@/types/api'
import { buildTree } from '@/utils/dataprocess'

// ========== 菜单管理 ==========

// 后端返回的菜单数据类型
export interface BackendMenuItem extends AppRouteRecord {
  parent_id: number | null
  sort: number
  is_hide: boolean
  is_hide_tab: boolean
  is_iframe: boolean
  keep_alive: boolean
  is_first_level: boolean
  fixed_tab: boolean
  active_path: string | null
  is_full_page: boolean
  show_badge: boolean
  show_text_badge: string | null
  status: boolean
  created_at: string
  updated_at: string
  permissions: MenuPermission[]
}

// 菜单权限类型
export interface MenuPermission {
  id: number
  menu_id: number
  title: string
  auth_mark: string
  route_name: string
  sort: number
  created_at: string
  updated_at: string
}

// 菜单表单类型
export interface MenuForm {
  parent_id: number | null
  name: string
  path: string
  component?: string
  title: string
  icon?: string
  label?: string
  sort?: number
  is_hide?: boolean
  is_hide_tab?: boolean
  link?: string
  is_iframe?: boolean
  keep_alive?: boolean
  is_first_level?: boolean
  fixed_tab?: boolean
  active_path?: string
  is_full_page?: boolean
  show_badge?: boolean
  show_text_badge?: string
  status?: boolean
  permissions?: MenuPermissionForm[]
}

// 菜单权限表单类型
export interface MenuPermissionForm {
  title: string
  auth_mark: string
  sort?: number
}

/**
 * 获取菜单列表（树形结构）
 */
export const getMenuList = async (): Promise<BackendMenuItem[]> => {
  const response = await request.get<{ menuList: BackendMenuItem[] }>({
    url: '/api/admin/menus'
  })
  // 构建树形结构
  const treeData = buildTree<BackendMenuItem>(response.menuList)

  // 导入menuDataToRouter函数
  const { menuDataToRouter } = await import('@/router/utils/menuToRouter')

  // 转换菜单数据为路由格式
  const menuList = treeData.map((route) => menuDataToRouter(route))

  return menuList
}

/**
 * 获取菜单树（用于选择父级菜单）
 */
export const getMenuTree = async (): Promise<BackendMenuItem[]> => {
  const response = await request.get<BackendMenuItem[]>({
    url: '/api/admin/menus/tree'
  })
  return buildTree<BackendMenuItem>(response)
}

/**
 * 创建菜单
 */
export const createMenu = (data: MenuForm): Promise<BackendMenuItem> => {
  return request.post<BackendMenuItem>({
    url: '/api/admin/menus',
    data
  })
}

/**
 * 更新菜单
 */
export const updateMenu = (id: number | string, data: MenuForm): Promise<BackendMenuItem> => {
  return request.put<BackendMenuItem>({
    url: `/api/admin/menus/${id}`,
    data
  })
}

/**
 * 删除菜单
 */
export const deleteMenu = (id: number | string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/menus/${id}`
  })
}

// ========== 角色管理 ==========

/**
 * 获取角色列表
 */
export const getRoleList = (params: RoleSearchParams): Promise<RoleListData> => {
  return request.get<RoleListData>({
    url: '/api/admin/roles',
    params
  })
}

/**
 * 创建角色
 */
export const createRole = (data: RoleForm): Promise<RoleListItem> => {
  return request.post<RoleListItem>({
    url: '/api/admin/roles',
    data
  })
}

/**
 * 获取角色详情
 */
export const getRoleDetail = (id: number): Promise<RoleListItem> => {
  return request.get<RoleListItem>({
    url: `/api/admin/roles/${id}`
  })
}

/**
 * 更新角色
 */
export const updateRole = (id: number, data: RoleForm): Promise<RoleListItem> => {
  return request.put<RoleListItem>({
    url: `/api/admin/roles/${id}`,
    data
  })
}

/**
 * 删除角色
 */
export const deleteRole = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/roles/${id}`
  })
}

/**
 * 为用户分配角色（追加式）
 */
export const assignUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.post<void>({
    url: `/api/admin/users/${userId}/roles/assign`,
    data
  })
}

/**
 * 同步用户角色（覆盖式）
 */
export const syncUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.put<void>({
    url: `/api/admin/users/${userId}/roles/sync`,
    data
  })
}

/**
 * 移除用户角色
 */
export const removeUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/users/${userId}/roles/remove`,
    data
  })
}

/**
 * 更新角色的菜单权限
 */
export const updateRolePermissions = (
  roleId: number,
  data: RolePermissionAssignParams
): Promise<void> => {
  return request.post<void>({
    url: `/api/admin/roles/${roleId}/menu-permissions/assign`,
    data: {
      permissions: data.menus // 转换为后端期望的格式
    }
  })
}

// ========== 配置管理 ==========

/**
 * 获取所有配置
 */
export const getConfigs = async (): Promise<AppConfig> => {
  return request.get<AppConfig>({ url: '/api/admin/configs' })
}

/**
 * 更新配置（系统配置和上传配置）
 */
export const updateConfigs = async (data: UpdateConfigsRequest): Promise<void> => {
  return request.put<void>({
    url: '/api/admin/configs/update',
    data
  })
}

// ========== 操作日志管理 ==========

/**
 * 获取操作日志列表
 */
export const getOperationLogs = (
  params: OperationLogSearchParams
): Promise<OperationLogListData> => {
  return request.get<OperationLogListData>({
    url: '/api/admin/operation-logs',
    params
  })
}

/**
 * 获取操作日志详情
 */
export const getOperationLogDetail = (id: number | string): Promise<OperationLog> => {
  return request.get<OperationLog>({
    url: `/api/admin/operation-logs/${id}`
  })
}
