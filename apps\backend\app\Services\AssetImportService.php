<?php

namespace App\Services;

use App\Enums\AssetCondition;
use App\Enums\AssetSource;
use App\Enums\AssetStatus;
use App\Models\Asset;

use App\Models\Category;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\Region;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class AssetImportService extends BaseImportService
{
    protected string $templateType = 'unknown_template';

    // 批量处理大小 - 覆盖基类的默认值
    protected int $batchSize = 1000;

    // 已处理的实体缓存（避免重复创建）
    protected array $processedEntities = [];

    public function __construct()
    {
        // 设置标题行格式化器为不格式化，保持原样
        HeadingRowFormatter::default('none');
    }



    /**
     * 验证Excel标题行
     */
    protected function validateHeaders(array $headers): void
    {
        // 必填字段（只检查关键字段）
        $requiredHeaders = [
            '资产名称', // 必填字段
        ];

        $missingHeaders = array_diff($requiredHeaders, $headers);
        if (! empty($missingHeaders)) {
            throw new \Exception('Excel文件缺少必要的列: ' . implode(', ', $missingHeaders));
        }

        // 自动检测模板类型
        $this->templateType = $this->detectTemplateType($headers);

        Log::info('检测到模板类型', [
            'template_type' => $this->templateType,
            'headers_count' => count($headers),
            'sample_headers' => array_slice($headers, 0, 10),
        ]);
    }

    /**
     * 检测模板类型
     */
    protected function detectTemplateType(array $headers): string
    {
        // 新模板特征字段
        $newTemplateHeaders = [
            '生产厂商名称',
            '供应商名称',
            '服务商名称',
            '售后部名称',
            '医疗分类',
            '科室',
            '行业分类',
        ];

        // 旧模板特征字段
        $oldTemplateHeaders = [
            '相关方名称',
            '税号',
            '相关方类型',
            '相关方地址',
            '相关方电话',
            '联系人姓名',
            '联系人电话',
            '职位',
            '部门',
        ];

        $newMatches = count(array_intersect($newTemplateHeaders, $headers));
        $oldMatches = count(array_intersect($oldTemplateHeaders, $headers));

        if ($newMatches >= 3) {
            return 'new_template';
        } elseif ($oldMatches >= 3) {
            return 'old_template';
        } else {
            return 'unknown_template';
        }
    }





    /**
     * 验证字段格式
     */
    protected function validateFieldFormats(array $data, int $rowNumber): void
    {
        // 验证电话格式（新模板中的各种联系电话）
        $phoneFields = [
            '生产厂商联系电话',
            '供应商联系电话',
            '服务商联系电话',
            '售后部联系电话',
        ];

        foreach ($phoneFields as $field) {
            if (! empty($data[$field]) && ! preg_match('/^[0-9\-\s\+\(\)]{7,20}$/', $data[$field])) {
                throw new \Exception("第{$rowNumber}行：{$field}格式不正确");
            }
        }

        // 验证数值字段
        $numericFields = ['合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)'];
        foreach ($numericFields as $field) {
            if (! empty($data[$field]) && ! is_numeric($data[$field])) {
                throw new \Exception("第{$rowNumber}行：{$field}必须是数字");
            }
        }

        // 验证日期格式
        if (! empty($data['启用日期'])) {
            $date = $data['启用日期'];
            if (! preg_match('/^\d{4}-\d{2}-\d{2}$/', $date) && ! is_numeric($date)) {
                throw new \Exception("第{$rowNumber}行：启用日期格式不正确，应为YYYY-MM-DD格式");
            }
        }
    }



    /**
     * 准备单个相关方数据（旧模板）
     */
    protected function prepareSingleEntity(array $rowData, array &$entities, array &$contacts, int $rowNumber): void
    {
        // 处理单个相关方（旧模板格式）
        if (! empty($rowData['相关方名称'])) {
            $entityKey = $this->getEntityKey($rowData);

            if (! isset($entities[$entityKey])) {
                $entities[$entityKey] = [
                    'name' => $rowData['相关方名称'],
                    'tax_number' => $rowData['税号'] ?? null,
                    'entity_type' => $rowData['相关方类型'] ?? '企业',
                    'address' => $rowData['相关方地址'] ?? null,
                    'phone' => $rowData['相关方电话'] ?? null,
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                    'created_at' => time(),
                    'updated_at' => time(),
                ];
            }

            // 准备联系人数据
            if (! empty($rowData['联系人姓名'])) {
                $contacts[] = [
                    'entity_key' => $entityKey,
                    'name' => $rowData['联系人姓名'],
                    'phone' => $rowData['联系人电话'] ?? null,
                    'position' => $rowData['职位'] ?? null,
                    'department' => $rowData['部门'] ?? null,
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                    'created_at' => time(),
                    'updated_at' => time(),
                    '_row_number' => $rowNumber,
                ];
            }
        }
    }

    /**
     * 准备多个相关方数据（新模板）
     */
    protected function prepareMultipleEntities(array $rowData, array &$entities, array &$contacts, int $rowNumber): void
    {
        // 定义相关方类型映射
        $entityTypes = [
            'manufacturer' => [
                'name_field' => '生产厂商名称',
                'contact_name_field' => '生产厂商联系人',
                'contact_phone_field' => '生产厂商联系电话',
                'contact_position_field' => '生产厂商职位',
                'type' => 'manufacturer',
            ],
            'supplier' => [
                'name_field' => '供应商名称',
                'contact_name_field' => '供应商联系人',
                'contact_phone_field' => '供应商联系电话',
                'contact_position_field' => '供应商职位',
                'type' => 'supplier',
            ],
            'service_provider' => [
                'name_field' => '服务商名称',
                'contact_name_field' => '服务商联系人',
                'contact_phone_field' => '服务商联系电话',
                'contact_position_field' => '服务商职位',
                'type' => 'service_provider',
            ],
            'after_sales' => [
                'name_field' => '售后部名称',
                'contact_name_field' => '售后部联系人',
                'contact_phone_field' => '售后部联系电话',
                'contact_position_field' => '售后部职位',
                'type' => 'after_sales',
            ],
        ];

        foreach ($entityTypes as $typeKey => $config) {
            $entityName = trim($rowData[$config['name_field']] ?? '');

            if (! empty($entityName)) {
                $entityKey = $typeKey . '_' . md5($entityName);

                if (! isset($entities[$entityKey])) {
                    $entities[$entityKey] = [
                        'name' => $entityName,
                        'entity_type' => $config['type'],
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                        'created_at' => time(),
                        'updated_at' => time(),
                    ];
                }

                // 准备联系人数据
                $contactName = trim($rowData[$config['contact_name_field']] ?? '');
                if (! empty($contactName)) {
                    $contacts[] = [
                        'entity_key' => $entityKey,
                        'name' => $contactName,
                        'phone' => trim($rowData[$config['contact_phone_field']] ?? ''),
                        'position' => trim($rowData[$config['contact_position_field']] ?? ''),
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                        'created_at' => time(),
                        'updated_at' => time(),
                        '_row_number' => $rowNumber,
                    ];
                }
            }
        }
    }

    /**
     * 获取相关方唯一键
     */
    protected function getEntityKey(array $rowData): string
    {
        $taxNumber = $rowData['税号'] ?? '';
        $name = $rowData['相关方名称'] ?? '';

        // 优先使用税号作为唯一键，否则使用名称
        return ! empty($taxNumber) ? 'tax_' . $taxNumber : 'name_' . $name;
    }

    /**
     * 准备相关方数据
     */
    protected function prepareEntityData(array $rowData): array
    {
        return [
            'name' => $rowData['相关方名称'],
            'tax_number' => $rowData['税号'] ?? null,
            'entity_type' => $rowData['相关方类型'] ?? 'company',
            'address' => $rowData['相关方地址'] ?? null,
            'phone' => $rowData['相关方电话'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
        ];
    }

    /**
     * 准备资产数据
     */
    protected function prepareAssetData(array $rowData, int $rowNumber): array
    {
        if ($this->templateType === 'new_template') {
            return $this->prepareNewTemplateAssetData($rowData, $rowNumber);
        } else {
            return $this->prepareOldTemplateAssetData($rowData, $rowNumber);
        }
    }

    /**
     * 准备新模板资产数据
     */
    protected function prepareNewTemplateAssetData(array $rowData, int $rowNumber): array
    {
        // 处理资产分类（新模板中可能包含医疗分类、行业分类等）
        $categories = []; // 存储所有分类
        // 获取所有第一级别的分类
        $topCategories = $this->getAllTopCategories();
        foreach ($topCategories as $cateName) {
            $categories[$cateName] = isset($rowData[$cateName]) ? $rowData[$cateName] : '';
        }

        $categories = array_values($categories);

        $assetCategories = $this->parseAssetCategories(implode(',', $categories));

        return [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ? AssetSource::fromLabel($rowData['资产来源']) : null,
            'asset_status' => $rowData['资产状态'] ? AssetStatus::fromLabel($rowData['资产状态']) : null,
            'asset_condition' => $rowData['成色'] ? AssetCondition::fromLabel($rowData['成色']) : null,
            'parent_id' => $this->parseParentAsset($rowData['主设备'] ?? null),
            'region_code' => $this->parseRegionCode($rowData['所在地区'] ?? null),
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'asset_category_ids' => json_encode($assetCategories),
            'remark' => $rowData['备注'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber,
        ];
    }

    /**
     * 准备旧模板资产数据
     */
    protected function prepareOldTemplateAssetData(array $rowData, int $rowNumber): array
    {
        $assetCategories = $this->parseAssetCategories($rowData['资产分类'] ?? '');

        return [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['资产品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ?? null,
            'asset_status' => $rowData['资产状态'] ?? null,
            'asset_condition' => $rowData['成色'] ?? null,
            'region_code' => $rowData['区县代码'] ?? null,
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'asset_category_ids' => json_encode($assetCategories),
            'remark' => $rowData['备注'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber,
        ];
    }

    /**
     * 准备联系人数据
     */
    protected function prepareContactData(array $rowData, int $rowNumber): array
    {
        return [
            'entity_key' => $this->getEntityKey($rowData), // 用于关联相关方
            'name' => $rowData['联系人姓名'],
            'phone' => $rowData['联系人电话'] ?? '',
            'position' => $rowData['职位'] ?? null,
            'department' => $rowData['部门'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber, // 用于错误追踪
        ];
    }

    /**
     * 批量插入相关方数据
     */
    protected function batchInsertEntities(array $entities): void
    {
        if (empty($entities)) {
            return;
        }

        foreach ($entities as $entityKey => $entityData) {
            // 检查是否已存在
            $existingEntity = null;

            if (! empty($entityData['tax_number'])) {
                $existingEntity = Entity::where('tax_number', $entityData['tax_number'])->first();
            }

            if (! $existingEntity) {
                $existingEntity = Entity::where('name', $entityData['name'])->first();
            }

            if ($existingEntity) {
                // 更新现有记录
                $existingEntity->update($entityData);
                $this->processedEntities[$entityKey] = $existingEntity->id;
            } else {
                // 创建新记录
                $newEntity = Entity::create($entityData);
                $this->processedEntities[$entityKey] = $newEntity->id;
            }
        }
    }



    /**
     * 批量插入联系人数据
     */
    protected function batchInsertContacts(array $contacts): void
    {
        if (empty($contacts)) {
            return;
        }

        $insertData = [];

        foreach ($contacts as $contactData) {
            $entityKey = $contactData['entity_key'];

            unset($contactData['entity_key'], $contactData['_row_number']);

            // 获取对应的相关方ID
            if (isset($this->processedEntities[$entityKey])) {
                $contactData['entity_id'] = $this->processedEntities[$entityKey];

                // 检查是否已存在相同联系人
                $existingContact = EntityContact::where('entity_id', $contactData['entity_id'])
                    ->where('name', $contactData['name'])
                    ->where('phone', $contactData['phone'])
                    ->first();

                if (! $existingContact) {
                    $insertData[] = $contactData;
                }
            }
        }

        // 使用批量插入
        if (! empty($insertData)) {
            EntityContact::insert($insertData);
        }
    }





    /**
     * 解析日期
     */
    protected function parseDate(?string $date): ?int
    {
        if (empty($date)) {
            return null;
        }

        try {
            return strtotime($date);
        } catch (\Exception) {
            return null;
        }
    }

    /**
     * 解析整数
     */
    protected function parseInteger(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        return is_numeric($value) ? (int) $value : null;
    }

    /**
     * 解析资产分类（与数据库进行对比匹配）
     */
    protected function parseAssetCategories(string $categories): array
    {
        if (empty($categories)) {
            return [];
        }

        // 支持多种分隔符：逗号、分号、竖线
        $categoryNames = preg_split('/[,;|]/', $categories);
        $categoryIds = [];

        foreach ($categoryNames as $categoryName) {
            $categoryName = trim($categoryName);
            if (empty($categoryName)) {
                continue;
            }

            // 防SQL注入：清理分类名称
            $categoryName = preg_replace('/[\'";\\\\]/', '', $categoryName);
            $categoryName = mb_substr($categoryName, 0, 100);

            // 查找匹配的分类ID
            $categoryId = $this->findCategoryIdByName($categoryName);
            if ($categoryId) {
                $categoryIds[] = $categoryId;
            } else {
                // 如果找不到匹配的分类，记录警告日志
                Log::warning('未找到匹配的资产分类', [
                    'category_name' => $categoryName,
                    'row_context' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3),
                ]);
            }
        }

        return array_unique($categoryIds);
    }

    /**
     * 根据分类名称查找分类ID
     */
    protected function findCategoryIdByName(string $categoryName): ?int
    {
        try {
            // 首先从缓存中查找
            $cacheKey = 'asset_category_name_' . md5($categoryName);

            return Cache::remember($cacheKey, 3600, function () use ($categoryName) {
                // 1. 检查预定义映射
                $mappedCategoryId = $this->getCategoryIdFromMapping($categoryName);
                if ($mappedCategoryId) {
                    return $mappedCategoryId;
                }

                // 2. 精确匹配
                $category = Category::where('name', $categoryName)
                    ->where('status', 1)
                    ->first();

                if ($category) {
                    Log::info('找到精确匹配的资产分类', [
                        'category_name' => $categoryName,
                        'category_id' => $category->id,
                        'match_type' => 'exact',
                    ]);

                    return $category->id;
                }

                // 3. 模糊匹配（包含关系）
                $category = Category::where('name', 'like', "%{$categoryName}%")
                    ->where('status', 1)
                    ->first();

                if ($category) {
                    Log::info('找到模糊匹配的资产分类', [
                        'category_name' => $categoryName,
                        'matched_category' => $category->name,
                        'category_id' => $category->id,
                        'match_type' => 'fuzzy_contains',
                    ]);

                    return $category->id;
                }

                // 4. 反向模糊匹配（被包含关系）
                $categories = Category::where('status', 1)->get();
                foreach ($categories as $category) {
                    if (strpos($categoryName, $category->name) !== false) {
                        Log::info('找到反向模糊匹配的资产分类', [
                            'category_name' => $categoryName,
                            'matched_category' => $category->name,
                            'category_id' => $category->id,
                            'match_type' => 'fuzzy_contained',
                        ]);

                        return $category->id;
                    }
                }

                return null;
            });
        } catch (\Exception $e) {
            Log::error('查找资产分类失败', [
                'category_name' => $categoryName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 从预定义映射中获取分类ID
     */
    protected function getCategoryIdFromMapping(string $categoryName): ?int
    {
        // 预定义的分类映射关系
        $category = Category::where('name', $categoryName)->where('status', 1)->first();

        if ($category) {
            Log::info('通过预定义映射找到资产分类', [
                'original_name' => $categoryName,
                'category_id' => $category->id,
                'match_type' => 'mapping',
            ]);

            return $category->id;
        }

        return null;
    }

    /**
     * 根据名称和类型查找相关方ID
     */
    protected function getAllTopCategories(): array
    {
        $categories = Category::where('status', 1)->where('parent_id', 0)->get();

        return $categories->pluck('name')->toArray();
    }

    /**
     * 解析主设备
     */
    protected function parseParentAsset(?string $parentName): ?int
    {
        if (empty($parentName)) {
            return null;
        }

        // 这里可以根据资产名称查找主设备ID
        // 暂时返回null，实际使用时需要实现查找逻辑
        return null;
    }

    /**
     * 解析地区ID
     */
    protected function parseRegionCode(?string $regionName): ?string
    {
        if (empty($regionName)) {
            return $regionName;
        }

        $regionMap = Region::where('name', '=', $regionName)->orWhere('ext_name', '=', $regionName)->first();

        return isset($regionMap->ext_id) ? $regionMap->ext_id : $regionName;
    }

    /**
     * 构建备注信息
     */
    protected function buildRemark(array $rowData): ?string
    {
        $remarks = [];

        // 添加原始备注
        if (! empty($rowData['备注'])) {
            $remarks[] = $rowData['备注'];
        }

        // 添加科室信息（处理带空格的字段名）
        $department = $rowData['科室'] ?? $rowData['  科室'] ?? '';
        if (! empty($department)) {
            $remarks[] = '科室：' . trim($department);
        }

        return empty($remarks) ? null : implode('；', $remarks);
    }

    /**
     * 构建相关方数据
     */
    protected function buildRelatedEntitiesData(array $assetData): array
    {
        $relatedEntities = [];

        if ($this->templateType === 'new_template') {
            // 新模板：处理多个相关方类型
            $relatedEntities = $this->buildNewTemplateRelatedEntities($assetData);
        } else {
            // 旧模板：处理单个相关方
            $relatedEntities = $this->buildOldTemplateRelatedEntities($assetData);
        }

        return $relatedEntities;
    }

    /**
     * 构建新模板相关方数据
     */
    protected function buildNewTemplateRelatedEntities(array $assetData): array
    {
        $relatedEntities = [];

        // 定义相关方类型映射
        $entityTypes = [
            'manufacturer' => [
                'name_field' => '生产厂商名称',
                'contact_name_field' => '生产厂商联系人',
                'contact_phone_field' => '生产厂商联系电话',
                'contact_position_field' => '生产厂商职位',
            ],
            'supplier' => [
                'name_field' => '供应商名称',
                'contact_name_field' => '供应商联系人',
                'contact_phone_field' => '供应商联系电话',
                'contact_position_field' => '供应商职位',
            ],
            'service_provider' => [
                'name_field' => '服务商名称',
                'contact_name_field' => '服务商联系人',
                'contact_phone_field' => '服务商联系电话',
                'contact_position_field' => '服务商职位',
            ],
            'after_sales' => [
                'name_field' => '售后部名称',
                'contact_name_field' => '售后部联系人',
                'contact_phone_field' => '售后部联系电话',
                'contact_position_field' => '售后部职位',
            ],
        ];

        foreach ($entityTypes as $entityType => $config) {
            $entityName = trim($assetData[$config['name_field']] ?? '');

            if (! empty($entityName)) {
                // 直接使用processedEntities中的键查找（修复关键问题）
                $entityKey = $entityType . '_' . md5($entityName);

                if (isset($this->processedEntities[$entityKey])) {
                    $entityId = $this->processedEntities[$entityKey];

                    $relatedEntity = [
                        'entity_id' => $entityId,
                        'entity_type' => $entityType,
                        'contact_name' => trim($assetData[$config['contact_name_field']] ?? ''),
                        'contact_phone' => trim($assetData[$config['contact_phone_field']] ?? ''),
                        'position' => trim($assetData[$config['contact_position_field']] ?? ''),
                        'department' => null, // 新模板中没有部门字段
                    ];

                    $relatedEntities[] = $relatedEntity;

                    Log::info('成功匹配相关方', [
                        'entity_name' => $entityName,
                        'entity_key' => $entityKey,
                        'entity_id' => $entityId,
                        'entity_type' => $entityType,
                    ]);
                } else {
                    Log::warning('未找到匹配的相关方', [
                        'entity_name' => $entityName,
                        'entity_key' => $entityKey,
                        'entity_type' => $entityType,
                        'available_keys' => array_keys($this->processedEntities),
                    ]);
                }
            }
        }

        return $relatedEntities;
    }

    /**
     * 构建旧模板相关方数据
     */
    protected function buildOldTemplateRelatedEntities(array $assetData): array
    {
        $relatedEntities = [];

        $entityName = trim($assetData['相关方名称'] ?? '');
        if (! empty($entityName)) {
            // 查找对应的相关方ID
            $entityId = $this->findEntityIdByNameAndType($entityName, '企业');

            if ($entityId) {
                $relatedEntity = [
                    'entity_id' => $entityId,
                    'entity_type' => $assetData['相关方类型'] ?? '企业',
                    'contact_name' => trim($assetData['联系人姓名'] ?? ''),
                    'contact_phone' => trim($assetData['联系人电话'] ?? ''),
                    'position' => trim($assetData['职位'] ?? ''),
                    'department' => trim($assetData['部门'] ?? ''),
                ];

                $relatedEntities[] = $relatedEntity;
            }
        }

        return $relatedEntities;
    }

    /**
     * 根据名称和类型查找相关方ID
     */
    protected function findEntityIdByNameAndType(string $entityName, string $entityType): ?int
    {
        try {
            // 首先从已处理的相关方中查找（使用正确的键格式）
            $entityKey = $entityType . '_' . md5($entityName);
            if (isset($this->processedEntities[$entityKey])) {
                return $this->processedEntities[$entityKey];
            }

            // 从数据库中查找
            $entity = Entity::where('name', $entityName)
                ->where('entity_type', $entityType)
                ->first();

            return $entity ? $entity->id : null;
        } catch (\Exception $e) {
            Log::warning('查找相关方ID失败', [
                'entity_name' => $entityName,
                'entity_type' => $entityType,
                'processed_entities_keys' => array_keys($this->processedEntities),
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }


    /**
     * 验证行数据（实现基类抽象方法）
     */
    protected function validateRowData(array $rowData, int $rowNumber): array
    {
        // 检查必填字段
        if (empty($rowData['资产名称'])) {
            throw new \Exception('资产名称不能为空');
        }

        // 清理和验证数据
        $cleanedData = [];

        foreach ($rowData as $key => $value) {
            // 防SQL注入：清理字符串数据
            if (is_string($value)) {
                $value = trim($value);
                // 移除潜在的SQL注入字符
                $value = preg_replace('/[\'";\\\\]/', '', $value);
                // 限制长度
                $value = mb_substr($value, 0, 500);
            }

            $cleanedData[$key] = $value;
        }

        // 验证特定字段格式
        $this->validateFieldFormats($cleanedData, $rowNumber);

        return $cleanedData;
    }

    /**
     * 转换行数据（实现基类抽象方法）
     */
    protected function transformRowData(array $rowData, int $rowNumber): array
    {
        // 根据模板类型转换数据
        if ($this->templateType === 'new_template') {
            return $this->parseNewTemplateRow($rowData, $rowNumber);
        } else {
            return $this->parseOldTemplateRow($rowData, $rowNumber);
        }
    }

    /**
     * 解析新模板行数据
     */
    protected function parseNewTemplateRow(array $rowData, int $rowNumber): array
    {
        // 准备相关方数据
        $entities = [];
        $contacts = [];
        $this->prepareMultipleEntities($rowData, $entities, $contacts, $rowNumber);

        // 准备资产数据
        $assetData = $this->prepareNewTemplateAssetData($rowData, $rowNumber);
        $assetData['_original_row_data'] = $rowData;

        return [
            'asset' => $assetData,
            'entities' => $entities,
            'contacts' => $contacts,
            'related_entities' => json_encode($this->buildNewTemplateRelatedEntities($rowData)),
        ];
    }

    /**
     * 解析旧模板行数据
     */
    protected function parseOldTemplateRow(array $rowData, int $rowNumber): array
    {
        // 准备相关方数据
        $entities = [];
        $contacts = [];
        $this->prepareSingleEntity($rowData, $entities, $contacts, $rowNumber);

        // 准备资产数据
        $assetData = $this->prepareOldTemplateAssetData($rowData, $rowNumber);
        $assetData['_original_row_data'] = $rowData;

        return [
            'asset' => $assetData,
            'entities' => $entities,
            'contacts' => $contacts,
            'related_entities' => json_encode($this->buildOldTemplateRelatedEntities($rowData)),
        ];
    }

    /**
     * 创建记录（实现基类抽象方法）
     */
    protected function createRecord(array $data): void
    {
        DB::beginTransaction();
        try {
            // 1. 先处理相关方数据
            if (!empty($data['entities'])) {
                $this->batchInsertEntities($data['entities']);
            }

            // 2. 处理联系人数据
            if (!empty($data['contacts'])) {
                $this->batchInsertContacts($data['contacts']);
            }

            // 3. 准备资产数据
            $assetData = $data['asset'];
            $originalRowData = $assetData['_original_row_data'] ?? [];
            unset($assetData['_original_row_data']);

            // 4. 构建相关方关联数据
            $relatedEntities = $this->buildRelatedEntitiesData($originalRowData);
            $assetData['related_entities'] = json_encode($relatedEntities);
            unset($assetData['_row_number']);

            // 5. 创建资产记录
            Asset::create($assetData);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}

/**
 * Excel导入读取器
 */
class AssetImportReader
{
    // 这个类用于Excel::toArray()方法
}
