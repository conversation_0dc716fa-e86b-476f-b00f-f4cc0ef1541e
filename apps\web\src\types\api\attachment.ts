/**
 * 附件相关类型定义
 */

import type { PaginatedResponse } from './pagination'

/**
 * 附件管理实体
 */
export interface AttachmentItem {
  id: string
  // 附件主表字段
  file_name: string // 原始文件名
  file_path: string // 存储路径
  file_url?: string // 文件访问URL（后端生成）
  file_size: number // 文件大小(字节)
  mime_type: string // MIME类型
  storage_type: string // 存储类型:local/alioss/qiniu/aws
  md5_hash?: string // MD5哈希(去重用)

  // 关联表字段
  attachment_id?: string // 附件ID
  attachable_id: string // 业务表主键
  attachable_type: string // 业务表模型类名
  sort?: number // 排序
  is_public?: boolean // 是否公开访问
  description?: string // 附件描述

  // 时间字段
  created_at: string // 创建时间
  updated_at?: string // 更新时间
}

/**
 * 附件搜索参数
 */
export interface AttachmentSearchParams {
  attachable_type?: string // 业务类型
  attachable_id?: string // 业务ID
  file_name?: string // 文件名
  start_time?: string // 开始时间
  end_time?: string // 结束时间
  // 分页相关
  size?: number
  per_page?: number
  current?: number
  page?: number
}

/**
 * 附件分页响应
 */
export type AttachmentPageResponse = PaginatedResponse<AttachmentItem>
