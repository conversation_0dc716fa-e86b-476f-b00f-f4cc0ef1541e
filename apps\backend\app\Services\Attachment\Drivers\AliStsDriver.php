<?php

namespace App\Services\Attachment\Drivers;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use App\Services\Attachment\Contracts\StorageDriver;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AliStsDriver implements StorageDriver
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;

        // 记录配置信息到日志
        Log::info('AliStsDriver 初始化配置', [
            'access_key_id' => substr($config['access_key_id'] ?? '', 0, 6).'***',  // 只显示前6位
            'bucket' => $config['bucket'] ?? '',
            'region' => $config['region'] ?? '',
            'endpoint' => $config['endpoint'] ?? '',
            'role_arn' => $config['role_arn'] ?? '',
        ]);

        // 处理 region 格式：如果传入的是 endpoint 格式，提取 region
        $region = $config['region'] ?? 'cn-hangzhou';
        if (strpos($region, 'oss-') !== false && strpos($region, '.aliyuncs.com') !== false) {
            // 从 oss-cn-guangzhou.aliyuncs.com 提取 cn-guangzhou
            $region = str_replace(['oss-', '.aliyuncs.com'], '', $region);
            Log::info('从 endpoint 格式提取 region', ['original' => $config['region'], 'extracted' => $region]);
        }

        // 初始化阿里云客户端
        try {
            AlibabaCloud::accessKeyClient(
                $config['access_key_id'],
                $config['access_key_secret']
            )
                ->regionId($region)
                ->asDefaultClient();

            Log::info('阿里云客户端初始化成功', ['region' => $region]);
        } catch (ClientException $e) {
            Log::error('阿里云客户端初始化失败', [
                'error' => $e->getMessage(),
                'region' => $region,
                'config_region' => $config['region'] ?? null,
            ]);
            throw new \RuntimeException('阿里云客户端初始化失败: '.$e->getMessage());
        }
    }

    /**
     * 获取STS临时凭证
     *
     * @param  array  $params  参数配置
     */
    public function getSTSCredentials(array $params = []): array
    {
        // 尝试从缓存获取
        $cacheKey = $this->getCacheKey($params);
        if ($cached = Cache::get($cacheKey)) {
            Log::info('使用缓存的STS凭证', ['cache_key' => $cacheKey]);

            return $cached;
        }

        try {
            // 获取配置 - 优先使用传入的配置，其次从环境变量读取
            $roleArn = $this->config['role_arn'] ?? config('sts.role_arn');
            if (empty($roleArn)) {
                throw new \RuntimeException('STS角色ARN未配置，请在系统配置中设置RAM角色ARN');
            }

            // 生成会话名称
            $sessionName = $this->generateSessionName($params);

            // 使用 AlibabaCloud SDK 调用 STS
            $result = AlibabaCloud::rpc()
                ->product('Sts')
                ->version('2015-04-01')
                ->action('AssumeRole')
                ->method('POST')
                ->scheme('https')  // 强制使用 HTTPS
                ->host('sts.aliyuncs.com')
                ->options([
                    'query' => [
                        'RoleArn' => $roleArn,
                        'RoleSessionName' => $sessionName,
                        'DurationSeconds' => $params['duration_seconds'] ?? config('sts.duration_seconds', 3500),
                        'Policy' => $this->generatePolicy($params),
                    ],
                ])
                ->request();

            // 获取凭证 - 转换为数组
            $responseData = $result->toArray();
            $credentials = $responseData['Credentials'] ?? [];

            if (empty($credentials)) {
                throw new \RuntimeException('STS凭证响应数据异常');
            }

            $stsData = [
                'AccessKeyId' => $credentials['AccessKeyId'],
                'AccessKeySecret' => $credentials['AccessKeySecret'],
                'SecurityToken' => $credentials['SecurityToken'],
                'Expiration' => $credentials['Expiration'],
            ];

            // 缓存凭证（比过期时间提前5分钟失效）
            $cacheTtl = ($params['duration_seconds'] ?? config('sts.duration_seconds', 3500)) - 300;
            Cache::put($cacheKey, $stsData, $cacheTtl);

            Log::info('STS凭证生成成功', [
                'session_name' => $sessionName,
                'expiration' => $credentials['Expiration'],
            ]);

            return $stsData;

        } catch (ServerException $e) {
            Log::error('STS服务端错误', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
            throw new \RuntimeException('获取STS凭证失败: '.$e->getMessage());
        } catch (ClientException $e) {
            Log::error('STS客户端错误', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
            throw new \RuntimeException('STS请求失败: '.$e->getMessage());
        }
    }

    /**
     * 生成权限策略
     */
    protected function generatePolicy(array $params): string
    {
        $bucket = $this->config['bucket'] ?? '';
        $resourcePrefix = config('sts.policy.resource_prefix', 'attachments/');

        // 生成细粒度的权限策略
        $policy = [
            'Version' => '1',
            'Statement' => [
                [
                    'Effect' => 'Allow',
                    'Action' => config('sts.policy.actions', [
                        'oss:PutObject',
                        'oss:PutObjectAcl',
                        'oss:GetObject',
                        'oss:DeleteObject',
                    ]),
                    'Resource' => [
                        "acs:oss:*:*:{$bucket}/{$resourcePrefix}*",
                    ],
                ],
            ],
        ];

        // 如果指定了特定路径，进一步限制权限
        if (! empty($params['specific_path'])) {
            $policy['Statement'][0]['Resource'] = [
                "acs:oss:*:*:{$bucket}/{$params['specific_path']}*",
            ];
        }

        return json_encode($policy);
    }

    /**
     * 生成会话名称
     */
    protected function generateSessionName(array $params): string
    {
        $prefix = config('sts.role_session_prefix', 'oss-upload-');
        $userId = $params['user_id'] ?? 'anonymous';
        $timestamp = time();

        return "{$prefix}{$userId}-{$timestamp}";
    }

    /**
     * 生成缓存key
     */
    protected function getCacheKey(array $params): string
    {
        $prefix = config('sts.cache.prefix', 'sts:credentials:');
        $userId = $params['user_id'] ?? 'default';
        $bucket = $this->config['bucket'] ?? 'default';

        return "{$prefix}{$bucket}:{$userId}";
    }

    /**
     * 获取存储类型
     */
    public function getType(): string
    {
        return 'alioss-sts';
    }

    /**
     * 本地上传文件（STS模式不支持服务端上传）
     *
     * @throws \BadMethodCallException
     */
    public function store(UploadedFile $file, string $path): string
    {
        throw new \BadMethodCallException('STS driver does not support server-side upload. Use client-side upload with STS credentials.');
    }

    /**
     * 检查文件是否存在
     */
    public function exists(string $path): bool
    {
        // STS模式下，文件存在性检查应由客户端完成
        // 或者使用OSS SDK进行检查
        return false;
    }

    /**
     * 删除文件
     */
    public function delete(string $path): bool
    {
        // STS模式下，文件删除应由客户端完成
        // 或者生成具有删除权限的临时凭证
        return false;
    }

    /**
     * 获取文件访问URL
     */
    public function url(string $path): string
    {
        $scheme = $this->config['use_ssl'] ?? true ? 'https' : 'http';
        $endpoint = $this->config['endpoint'] ?? 'oss-cn-hangzhou.aliyuncs.com';
        $bucket = $this->config['bucket'] ?? '';

        // 如果配置了CDN域名，使用CDN
        if (! empty($this->config['cdn_domain'])) {
            return "{$scheme}://{$this->config['cdn_domain']}/{$path}";
        }

        // 否则使用OSS域名
        return "{$scheme}://{$bucket}.{$endpoint}/{$path}";
    }

    /**
     * 获取上传凭证（返回STS临时凭证）
     *
     * @param  array  $params  上传参数
     */
    public function getUploadCredentials(array $params = []): array
    {
        // 直接调用 getSTSCredentials 方法
        return $this->getSTSCredentials($params);
    }

    /**
     * 验证回调（STS模式不需要）
     */
    public function validateCallback(array $data): bool
    {
        // STS模式不需要回调验证
        return true;
    }

    /**
     * 获取OSS配置信息（供前端使用）
     */
    public function getOssConfig(): array
    {
        return [
            'region' => $this->config['region'] ?? 'cn-hangzhou',
            'bucket' => $this->config['bucket'] ?? '',
            'endpoint' => $this->config['endpoint'] ?? 'oss-cn-hangzhou.aliyuncs.com',
            'prefix' => config('sts.policy.resource_prefix', 'attachments/'),
        ];
    }
}
