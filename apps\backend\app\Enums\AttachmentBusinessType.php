<?php

namespace App\Enums;

/**
 * 附件业务类型
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated
 */
enum AttachmentBusinessType: string
{
    case APP__MODELS__ENTITY = 'App\Models\Entity';
    case APP__MODELS__LIFECYCLE = 'App\Models\Lifecycle';
    case APP__MODELS__LIFECYCLE_FOLLOW_UP = 'App\Models\LifecycleFollowUp';
    case APP__MODELS__USER = 'App\Models\User';
    case APP__MODELS__ASSET = 'App\Models\Asset';
    case APP__MODELS__SYSTEM_CONFIG = 'App\Models\SystemConfig';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::APP__MODELS__ENTITY => '相关方',
            self::APP__MODELS__LIFECYCLE => '生命周期',
            self::APP__MODELS__LIFECYCLE_FOLLOW_UP => '生命周期跟进',
            self::APP__MODELS__USER => '用户',
            self::APP__MODELS__ASSET => '资产',
            self::APP__MODELS__SYSTEM_CONFIG => '系统配置',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }

    /**
     * 获取所有枚举值
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * 根据中文标签获取对应的枚举值
     * @param string $label 中文标签（注意：此参数为中文标签，不是枚举值）
     * @return string|null 匹配的枚举值（如“manufacturer”），未找到则返回null
     */
    public static function fromLabel(string $label): ?string
    {
        foreach (self::cases() as $case) {
            if ($case->label() === $label) {
                return $case->value;
            }
        }
        return null;
    }
}
