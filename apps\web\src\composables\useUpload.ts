import { ref, computed, watch, onUnmounted, type Ref } from 'vue'
import type { UploadFile, UploadFiles, UploadRawFile, UploadRequestOptions } from 'element-plus'
import { ElMessage } from 'element-plus'
import { uploadAttachment, getSTSCredentials, confirmUpload } from '@/api/admin/attachment'
import type { AttachmentItem } from '@/types/api'
import { OSSUploader } from '@/utils/ossUpload'
import { useConfigStore } from '@/store/modules/config'

/**
 * 上传配置选项
 */
export interface UseUploadOptions {
  /** 最大上传数量，默认: 10 */
  limit?: number
  /** 单个文件最大大小（MB），默认: 10 */
  maxSize?: number
  /** 接受的文件类型，默认: '*' (所有类型) */
  accept?: string
  /** 是否支持多选，默认: true */
  multiple?: boolean
  /** 是否显示成功消息，默认: true */
  showSuccessMessage?: boolean
  /** 是否禁用粘贴上传，默认: false */
  disablePaste?: boolean
  /** 上传成功回调 */
  onSuccess?: (file: UploadFile, fileList: UploadFiles) => void
  /** 上传失败回调 */
  onError?: (error: any, file: UploadFile) => void
  /** 文件移除回调 */
  onRemove?: (file: UploadFile) => void
  /** 文件上传前的钩子 */
  beforeUpload?: (file: UploadRawFile) => boolean | Promise<boolean>
}

/**
 * 通用上传组合式函数
 *
 * @param modelValue - v-model 绑定的附件ID数组
 * @param attachments - 附件详情数组（用于编辑时显示）
 * @param options - 上传配置选项
 *
 * @example
 * ```typescript
 * // 在组件中使用
 * const ids = defineModel<number[]>({ default: () => [] })
 * const props = defineProps<{ attachments?: AttachmentItem[] }>()
 *
 * const {
 *   fileList,
 *   beforeUpload,
 *   customUpload,
 *   handleSuccess,
 *   handleError,
 *   handleRemove
 * } = useUpload(ids, toRef(props, 'attachments'), {
 *   limit: 5,
 *   maxSize: 10,
 *   accept: 'image/*'
 * })
 * ```
 */
export function useUpload(
  modelValue: Ref<number[]>,
  attachments: Ref<AttachmentItem[] | undefined> = ref([]),
  options: UseUploadOptions = {}
) {
  const {
    limit = 10,
    maxSize = 10,
    accept = '*',
    multiple = true,
    showSuccessMessage = true,
    disablePaste = false,
    onSuccess,
    onError,
    onRemove,
    beforeUpload: customBeforeUpload
  } = options

  /**
   * 上传容器元素引用
   */
  const containerRef = ref<HTMLElement>()

  /**
   * 文件列表，Element Plus Upload 组件使用
   */
  const fileList = ref<UploadFile[]>([])

  /**
   * 文件ID映射表
   * key: 文件的 uid
   * value: 附件的 ID
   */
  const fileIdMap = ref<Map<string, number>>(new Map())

  /**
   * 是否有文件正在上传
   */
  const hasUploading = computed(() => {
    return fileList.value.some((file) => file.status === 'uploading')
  })

  /**
   * 已上传的文件数量
   */
  const uploadedCount = computed(() => {
    return fileList.value.filter((file) => file.status === 'success').length
  })

  /**
   * 是否达到上传限制
   */
  const isLimitReached = computed(() => {
    return uploadedCount.value >= limit
  })

  /**
   * 初始化文件列表
   * 将传入的附件数据转换为 Element Plus 需要的格式
   */
  const initFileList = () => {
    // 清空现有数据
    fileList.value = []
    fileIdMap.value.clear()

    const attachmentList = attachments.value || []
    if (attachmentList.length > 0) {
      fileList.value = attachmentList.map((att) => {
        const uid = `init-${att.id}`
        // 缓存文件ID映射
        fileIdMap.value.set(uid, Number(att.id))

        return {
          uid,
          name: att.file_name || '文件',
          status: 'success' as const,
          url: att.file_url || '',
          size: att.file_size || 0,
          // 保存完整的响应数据，供后续使用
          response: {
            id: att.id,
            file_url: att.file_url || '',
            file_name: att.file_name,
            file_size: att.file_size
          }
        } as any as UploadFile
      })
    }
  }

  /**
   * 监听附件数据变化，自动初始化文件列表
   */
  watch(
    attachments,
    () => {
      initFileList()
    },
    { immediate: true, deep: true }
  )

  /**
   * 文件上传前的验证
   *
   * @param rawFile - 原始文件对象
   * @returns 是否允许上传
   */
  const beforeUpload = (rawFile: UploadRawFile): boolean | Promise<boolean> => {
    // 检查文件大小
    const isOverSize = rawFile.size / 1024 / 1024 > maxSize
    if (isOverSize) {
      ElMessage.error(`文件大小不能超过 ${maxSize}MB!`)
      return false
    }

    // 调用自定义的 beforeUpload 钩子
    if (customBeforeUpload) {
      return customBeforeUpload(rawFile)
    }

    return true
  }

  /**
   * 自定义上传方法
   * 根据系统配置选择本地上传或OSS直传
   *
   * @param options - Element Plus 提供的上传选项
   */
  const customUpload = async (options: UploadRequestOptions) => {
    const { onError } = options

    try {
      // 获取系统配置
      const configStore = useConfigStore()
      const config = await configStore.fetchConfig()

      // 判断存储方式
      if (config.upload.storage_type === 'aliyun') {
        // OSS直传模式
        await uploadToOSS(options)
      } else {
        // 本地上传模式
        await uploadToLocal(options)
      }
    } catch (error) {
      // 通知 Element Plus 上传失败
      onError(error as any)
      // 注意：错误提示由 http 拦截器处理，这里不需要重复显示
    }
  }

  /**
   * 本地上传
   */
  const uploadToLocal = async (options: UploadRequestOptions) => {
    const { file, onProgress, onSuccess } = options

    // 显示上传进度
    onProgress({ percent: 30 } as any)

    // 调用上传 API
    const response = await uploadAttachment(file as File)

    // 更新进度到 100%
    onProgress({ percent: 100 } as any)

    // 通知 Element Plus 上传成功
    onSuccess(response)

    // 缓存文件ID映射
    if (response?.id) {
      fileIdMap.value.set(file.uid.toString(), Number(response.id))
    }
  }

  /**
   * OSS直传
   */
  const uploadToOSS = async (options: UploadRequestOptions) => {
    const { file, onProgress, onSuccess } = options
    const ossUploader = new OSSUploader()

    try {
      // 1. 计算MD5（可选，用于秒传）
      let md5Hash: string | undefined
      if (file.size < 50 * 1024 * 1024) {
        // 小于50MB才计算MD5
        onProgress({ percent: 5 } as any)
        try {
          md5Hash = await ossUploader.calculateMD5(file as File)
        } catch (error) {
          console.warn('MD5计算失败，跳过秒传检查', error)
        }
      }

      // 2. 获取STS凭证或秒传结果
      onProgress({ percent: 10 } as any)
      const stsResponse = await getSTSCredentials({
        filename: file.name,
        filesize: file.size,
        mime_type: file.type || 'application/octet-stream',
        md5_hash: md5Hash
      })

      // 3. 检查响应类型
      // 如果响应有 id 字段，说明是秒传成功返回的附件资源
      if (stsResponse.id) {
        onProgress({ percent: 100 } as any)
        onSuccess(stsResponse)

        // 缓存文件ID映射
        fileIdMap.value.set(file.uid.toString(), Number(stsResponse.id))

        return
      }

      // 4. 初始化OSS客户端
      if (!stsResponse.credentials || !stsResponse.region || !stsResponse.bucket) {
        throw new Error('获取STS凭证失败')
      }

      // 转换STS凭证格式：API格式（snake_case）-> SDK格式（PascalCase）
      const ossCredentials = {
        AccessKeyId: stsResponse.credentials.access_key_id,
        AccessKeySecret: stsResponse.credentials.access_key_secret,
        SecurityToken: stsResponse.credentials.security_token,
        Expiration: stsResponse.credentials.expiration
      }

      ossUploader.initClient(ossCredentials, {
        region: stsResponse.region,
        bucket: stsResponse.bucket,
        endpoint:
          stsResponse.endpoint?.replace('https://', '') || `oss-${stsResponse.region}.aliyuncs.com`,
        prefix: stsResponse.prefix
      })

      // 5. 执行上传
      onProgress({ percent: 20 } as any)
      const objectKey = await ossUploader.upload(file as File, (percent) => {
        // 进度映射：20% - 95%
        const mappedPercent = 20 + percent * 0.75
        onProgress({ percent: mappedPercent } as any)
      })

      // 6. 确认上传完成
      onProgress({ percent: 95 } as any)
      const attachment = await confirmUpload({
        upload_id: stsResponse.upload_id!,
        object_key: objectKey,
        filename: file.name,
        filesize: file.size,
        mime_type: file.type || 'application/octet-stream'
      })

      // 7. 完成
      onProgress({ percent: 100 } as any)
      onSuccess(attachment)

      // 缓存文件ID映射
      if (attachment.id) {
        fileIdMap.value.set(file.uid.toString(), Number(attachment.id))
      }

      // 清理
      ossUploader.destroy()
    } catch (error) {
      ossUploader.destroy()
      throw error
    }
  }

  /**
   * 处理上传成功
   *
   * @param response - 服务器响应
   * @param file - 上传的文件
   * @param fileList - 文件列表
   */
  const handleSuccess = (response: any, file: UploadFile, fileList: UploadFiles) => {
    if (response?.id) {
      // 设置文件的 URL，用于预览
      file.url = response.file_url || ''

      // 更新 v-model
      updateModelValue()

      // 显示成功消息
      if (showSuccessMessage) {
        ElMessage.success('文件上传成功')
      }

      // 调用成功回调
      onSuccess?.(file, fileList)
    }
  }

  /**
   * 处理上传失败
   *
   * @param error - 错误对象
   * @param file - 上传的文件
   */
  const handleError = (error: any, file: UploadFile) => {
    // 错误提示由 http 拦截器处理，这里只需要调用回调
    onError?.(error, file)
  }

  /**
   * 处理文件移除
   * 注意：不会删除服务器上的文件，只是从列表中移除
   *
   * @param file - 要移除的文件
   */
  const handleRemove = (file: UploadFile) => {
    // 从映射中移除
    fileIdMap.value.delete(file.uid.toString())

    // 更新 v-model
    updateModelValue()

    // 调用移除回调
    onRemove?.(file)
  }

  /**
   * 处理超出上传数量限制
   */
  const handleExceed = () => {
    ElMessage.warning(`最多只能上传${limit}个文件`)
  }

  /**
   * 更新 v-model 的值
   * 提取所有成功上传的文件ID
   */
  const updateModelValue = () => {
    const ids: number[] = []

    fileList.value.forEach((file) => {
      if (file.status === 'success') {
        // 优先从缓存的映射中获取ID
        let fileId = fileIdMap.value.get(file.uid.toString())

        // 如果缓存中没有，尝试从响应中获取
        if (
          !fileId &&
          file.response &&
          typeof file.response === 'object' &&
          'id' in file.response
        ) {
          fileId = Number(file.response.id)
          // 补充到缓存中
          fileIdMap.value.set(file.uid.toString(), fileId)
        }

        if (fileId) {
          ids.push(fileId)
        }
      }
    })

    // 更新 v-model
    modelValue.value = ids
  }

  /**
   * 手动触发上传
   * 用于非自动上传模式
   */
  const submit = () => {
    // 获取所有待上传的文件
    const pendingFiles = fileList.value.filter((file) => file.status === 'ready')
    if (pendingFiles.length === 0) {
      ElMessage.warning('没有待上传的文件')
      return
    }

    // 触发上传
    // 注意：这需要配合 el-upload 的 ref 使用
    ElMessage.info('请使用 el-upload 组件的 submit 方法')
  }

  /**
   * 清空所有文件
   */
  const clearFiles = () => {
    fileList.value = []
    fileIdMap.value.clear()
    modelValue.value = []
  }

  /**
   * 获取已上传的文件ID列表
   */
  const getFileIds = (): number[] => {
    return Array.from(fileIdMap.value.values())
  }

  /**
   * 获取文件列表
   */
  const getFiles = (): UploadFile[] => {
    return fileList.value
  }

  /**
   * 从文件对象创建 UploadRawFile
   */
  const createUploadRawFile = (file: File): UploadRawFile => {
    const rawFile = file as UploadRawFile
    rawFile.uid = Date.now() + Math.random()
    return rawFile
  }

  /**
   * 手动处理文件上传
   * 用于粘贴上传等场景
   */
  const handleFileUpload = async (file: File) => {
    // 检查是否达到上传限制
    if (isLimitReached.value) {
      ElMessage.warning(`最多只能上传${limit}个文件`)
      return
    }

    // 创建 UploadRawFile 对象
    const rawFile = createUploadRawFile(file)

    // 执行上传前验证
    const validationResult = beforeUpload(rawFile)
    const isValid = validationResult instanceof Promise ? await validationResult : validationResult

    if (!isValid) {
      return
    }

    // 添加到文件列表（状态为 uploading）
    const uploadFile: UploadFile = {
      uid: rawFile.uid,
      name: file.name,
      status: 'uploading',
      size: file.size,
      percentage: 0,
      raw: rawFile
    }
    fileList.value.push(uploadFile)

    // 执行上传
    try {
      await customUpload({
        file: rawFile,
        action: '', // 这个在我们的实现中不使用
        filename: file.name,
        data: {},
        headers: {},
        onProgress: (event: any) => {
          uploadFile.percentage = event.percent
        },
        onSuccess: (response: any) => {
          uploadFile.status = 'success'
          uploadFile.response = response
          handleSuccess(response, uploadFile, fileList.value)
        },
        onError: (error: any) => {
          uploadFile.status = 'fail'
          handleError(error, uploadFile)
          // 移除失败的文件
          const index = fileList.value.findIndex((f) => f.uid === uploadFile.uid)
          if (index > -1) {
            fileList.value.splice(index, 1)
          }
        },
        withCredentials: false,
        method: 'POST'
      } as UploadRequestOptions)
    } catch (error) {
      uploadFile.status = 'fail'
      handleError(error, uploadFile)
      // 移除失败的文件
      const index = fileList.value.findIndex((f) => f.uid === uploadFile.uid)
      if (index > -1) {
        fileList.value.splice(index, 1)
      }
    }
  }

  /**
   * 处理粘贴事件
   */
  const handlePaste = async (event: ClipboardEvent) => {
    const clipboardData = event.clipboardData
    if (!clipboardData) return

    const items = Array.from(clipboardData.items)
    let hasValidFile = false

    for (const item of items) {
      // 处理图片文件
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile()
        if (file) {
          hasValidFile = true
          await handleFileUpload(file)
        }
      }
      // 处理其他文件类型（某些浏览器支持）
      else if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          // 检查文件类型是否被接受
          if (
            accept === '*' ||
            accept.includes(file.type) ||
            accept.includes(`.${file.name.split('.').pop()}`)
          ) {
            hasValidFile = true
            await handleFileUpload(file)
          }
        }
      }
    }

    // 如果处理了文件，阻止默认行为
    if (hasValidFile) {
      event.preventDefault()
    }
  }

  /**
   * 设置粘贴事件监听
   * 只在容器元素存在时监听
   */
  const setupPasteListener = () => {
    if (containerRef.value) {
      containerRef.value.addEventListener('paste', handlePaste)
    }
  }

  /**
   * 移除粘贴事件监听
   */
  const removePasteListener = () => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('paste', handlePaste)
    }
  }

  /**
   * 监听容器元素变化，重新设置粘贴监听
   * 只在未禁用粘贴时才设置监听
   */
  watch(containerRef, (newEl, oldEl) => {
    if (disablePaste) return

    // 移除旧元素的监听
    if (oldEl) {
      oldEl.removeEventListener('paste', handlePaste)
    }
    // 为新元素添加监听
    if (newEl) {
      newEl.addEventListener('paste', handlePaste)
    }
  })

  // 在组件卸载时移除监听器
  onUnmounted(() => {
    removePasteListener()
  })

  return {
    // 数据
    fileList,
    fileIdMap,
    containerRef,

    // 计算属性
    hasUploading,
    uploadedCount,
    isLimitReached,

    // 配置
    limit,
    maxSize,
    accept,
    multiple,

    // 方法
    beforeUpload,
    customUpload,
    handleSuccess,
    handleError,
    handleRemove,
    handleExceed,
    updateModelValue,
    submit,
    clearFiles,
    getFileIds,
    getFiles,
    handleFileUpload,
    setupPasteListener,
    removePasteListener
  }
}
