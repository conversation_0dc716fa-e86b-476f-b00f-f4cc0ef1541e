<template>
  <ElDialog
    v-model="visible"
    :title="batchMode ? '批量新增生命周期' : type === 'add' ? '新增生命周期' : '编辑生命周期'"
    width="70%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <!-- 批量模式提示 -->
      <ElAlert v-if="batchMode" type="info" :closable="false" style="margin-bottom: 20px" show-icon>
        <template #title> 批量创建生命周期计划 </template>
        将为以下 {{ preselectedAssets?.length || 0 }} 个资产创建生命周期计划
      </ElAlert>
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem prop="assets" :label-width="'0'">
            <!-- 普通模式的资产选择器 -->
            <template v-if="!batchMode">
              <div class="asset-selector-container">
                <div class="container-header">
                  <span class="container-label">关联资产</span>
                </div>
                <div class="search-section">
                  <ElInput
                    v-model="assetSearchKeyword"
                    placeholder="搜索资产（名称、品牌、型号、序列号）"
                    :prefix-icon="Search"
                    clearable
                    style="margin-bottom: 8px"
                  />
                  <div class="selection-info">
                    <span class="selected-count">已选择：{{ selectedAssetIds.length }} 个资产</span>
                  </div>
                </div>
                <!-- 资产表格 -->
                <ElTable
                  ref="assetTableRef"
                  :data="filteredAssetList"
                  :height="300"
                  stripe
                  @selection-change="handleAssetSelectionChange"
                  row-key="id"
                  style="width: 100%"
                  table-layout="fixed"
                >
                  <ElTableColumn type="selection" width="50" :reserve-selection="true" />
                  <ElTableColumn prop="id" label="ID" width="80" />
                  <ElTableColumn prop="name" label="名称" show-overflow-tooltip />
                  <ElTableColumn label="品牌" show-overflow-tooltip>
                    <template #default="{ row }">
                      {{ row.brand?.name || row.brand?.display_name || '-' }}
                    </template>
                  </ElTableColumn>
                  <ElTableColumn prop="model" label="型号" show-overflow-tooltip />
                  <ElTableColumn prop="serial_number" label="序列号" show-overflow-tooltip />
                </ElTable>
                <div v-if="loadingAssets" class="loading-overlay">
                  <ElIcon class="is-loading"><Loading /></ElIcon>
                  <span>加载中...</span>
                </div>
              </div>
            </template>

            <!-- 批量模式的资产展示 -->
            <template v-else>
              <div class="asset-selector-container">
                <div class="container-header">
                  <span class="container-label">预选资产</span>
                </div>
                <ElTable
                  :data="preselectedAssets || []"
                  size="small"
                  max-height="250"
                  stripe
                  style="width: 100%"
                >
                  <ElTableColumn prop="id" label="ID" width="60" />
                  <ElTableColumn prop="name" label="名称" show-overflow-tooltip min-width="100" />
                  <ElTableColumn label="品牌" width="80" show-overflow-tooltip>
                    <template #default="{ row }">
                      {{ row.brand?.name || row.brand?.display_name || '-' }}
                    </template>
                  </ElTableColumn>
                  <ElTableColumn prop="model" label="型号" width="80" show-overflow-tooltip />
                  <ElTableColumn
                    prop="serial_number"
                    label="序列号"
                    width="100"
                    show-overflow-tooltip
                  />
                </ElTable>
              </div>
            </template>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="类型" prop="type">
            <NullableSelect v-model="formData.type" placeholder="请选择类型">
              <ElOption
                v-for="item in lifecycleTypes"
                :key="item.code"
                :label="item.value"
                :value="item.code"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="日期" prop="date">
            <ElDatePicker
              v-model="formData.date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              :value-format="'X'"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="发起人" prop="initiator_id">
            <NullableSelect
              v-model="formData.initiator_id"
              placeholder="请选择发起人"
              filterable
              clearable
            >
              <ElOption
                v-for="user in userList"
                :key="user.id"
                :label="user.nickname || user.nickName || user.realName"
                :value="user.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="内容" prop="content">
            <ElInput
              v-model="formData.content"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="协助人员" prop="assistants">
            <ElSelect
              v-model="formData.assistants"
              :placeholder="formData.initiator_id ? '请选择协助人员' : '请先选择发起人'"
              multiple
              filterable
              clearable
              collapse-tags
              collapse-tags-tooltip
              :disabled="!formData.initiator_id"
            >
              <ElOption
                v-for="user in filteredAssistantsList"
                :key="user.id"
                :label="user.nickname || user.nickName || user.realName"
                :value="user.id"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="需要考勤管理" prop="is_need_attendance">
            <ElSwitch v-model="needAttendance" active-text="需要" inactive-text="不需要" />
            <span class="text-muted ml-2"> （保存后可在列表中配置考勤规则） </span>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="验收相关方" prop="acceptance_entity_id">
            <NullableSelect
              v-model="formData.acceptance_entity_id"
              placeholder="请选择验收相关方"
              filterable
              clearable
              @change="handleEntityChange"
            >
              <ElOption
                v-for="entity in entityList"
                :key="entity.id"
                :label="entity.name"
                :value="entity.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="验收人员" prop="acceptance_personnel_id">
            <NullableSelect
              v-model="formData.acceptance_personnel_id"
              placeholder="请先选择验收相关方"
              filterable
              clearable
              :disabled="!formData.acceptance_entity_id"
            >
              <ElOption
                v-for="contact in contactList"
                :key="contact.id"
                :label="contact.name"
                :value="contact.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="验收日期" prop="acceptance_time">
            <ElDatePicker
              v-model="formData.acceptance_time"
              type="date"
              placeholder="选择验收日期"
              style="width: 100%"
              :value-format="'X'"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="相关文件">
            <AttachmentUpload
              v-model="formData.attachments"
              :attachments="attachmentDetails"
              :limit="10"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <!-- 标签选择区域 -->
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="关联标签">
            <TagSelector v-model="formData.tag_ids" :tags="allTags" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'LifecycleDialog' })

  // Vue 核心
  import { ref, reactive, watch, computed, nextTick } from 'vue'

  // UI 框架
  import {
    ElMessage,
    ElOption,
    ElIcon,
    ElTable,
    ElTableColumn,
    ElInput,
    ElButton,
    ElAlert,
    ElSwitch
  } from 'element-plus'
  import { Search, Loading } from '@element-plus/icons-vue'
  import type { FormInstance, FormRules } from 'element-plus'

  // API
  import {
    createLifecycle,
    updateLifecycle,
    getAcceptancePersonnel,
    getAssetList
  } from '@/api/admin/asset'
  import type { Lifecycle, LifecycleAsset, LifecycleFormData } from '@/types/api/lifecycle'
  import type { Asset } from '@/types/api/asset'
  import { getEntityList } from '@/api/admin/entity'
  import { getUserList } from '@/api/admin/user'
  import { getTagList } from '@/api/admin/tag'
  import type { Tag } from '@/types/api/tag'

  // Store
  import { useDictionaryStore } from '@/store/modules/dictionary'

  // 组件
  import AttachmentUpload from '@/components/custom/upload/Attachment.vue'
  import NullableSelect from '@/components/custom/nullable-select/index.vue'
  import TagSelector from '@/components/custom/tag-selector/index.vue'

  // Props
  const props = defineProps<{
    type: 'add' | 'edit'
    data?: Lifecycle | null
    batchMode?: boolean // 新增：批量模式标识
    preselectedAssets?: any[] // 新增：预选的资产列表
  }>()

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 过滤后的协助人员列表（排除已选择的发起人）
  const filteredAssistantsList = computed(() => {
    if (!formData.initiator_id) return userList.value
    return userList.value.filter((user) => user.id !== formData.initiator_id)
  })

  // 表单相关
  const formRef = ref<FormInstance>()
  const formData = reactive<Partial<Lifecycle>>({
    id: undefined,
    // 多资产支持
    assets: [] as LifecycleAsset[],
    // 保留兼容性字段
    asset_id: null,
    type: null,
    date: '',
    initiator_id: null,
    content: '',
    assistants: [],
    acceptance_entity_id: null,
    acceptance_personnel_id: null,
    acceptance_time: '',
    attachments: [],
    // 标签字段统一使用 tag_ids
    tag_ids: [] as number[],
    // 考勤管理字段
    is_need_attendance: 0
  })

  // 使用计算属性处理开关
  const needAttendance = computed({
    get: () => formData.is_need_attendance === 1,
    set: (val) => {
      formData.is_need_attendance = val ? 1 : 0
    }
  })

  // 表单规则
  const rules: FormRules = {
    assets: [
      {
        required: false, // 根据业务需求决定是否必选
        message: '请选择关联资产',
        trigger: 'change',
        validator: (rule, value) => {
          if (value && Array.isArray(value) && value.length > 0) {
            return true
          }
          return value && Array.isArray(value) // 允许空数组
        }
      }
    ],
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    date: [{ required: true, message: '请选择日期', trigger: 'change' }],
    initiator_id: [{ required: true, message: '请选择发起人', trigger: 'change' }],
    content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    assistants: [{ required: true, message: '请选择协助人员', trigger: 'change' }],
    acceptance_entity_id: [{ required: true, message: '请选择验收相关方', trigger: 'change' }],
    acceptance_personnel_id: [{ required: true, message: '请选择验收人员', trigger: 'change' }],
    acceptance_time: [{ required: true, message: '请选择验收日期', trigger: 'change' }]
  }

  // 数据源
  const dictionaryStore = useDictionaryStore()
  const lifecycleTypes = ref<Array<{ code: string; value: string }>>([])
  const userList = ref<any[]>([])
  const entityList = ref<any[]>([])
  const contactList = ref<
    Array<{ id: number; name: string; phone: string; position?: string; department?: string }>
  >([])
  const attachmentDetails = ref<any[]>([])

  // 标签选择相关
  const allTags = ref<Tag[]>([])

  // 资产选择相关
  const assetList = ref<Asset[]>([])
  const loadingAssets = ref(false)
  const selectedAssetIds = ref<number[]>([])
  const assetTableRef = ref()
  const assetSearchKeyword = ref('')

  // 过滤后的资产列表（支持搜索）
  const filteredAssetList = computed(() => {
    if (!assetSearchKeyword.value.trim()) {
      return assetList.value
    }

    const keyword = assetSearchKeyword.value.toLowerCase().trim()
    return assetList.value.filter((asset) => {
      return (
        asset.name?.toLowerCase().includes(keyword) ||
        (asset.brand?.name || asset.brand?.display_name)?.toLowerCase().includes(keyword) ||
        asset.model?.toLowerCase().includes(keyword) ||
        asset.serial_number?.toLowerCase().includes(keyword) ||
        String(asset.id).includes(keyword)
      )
    })
  })

  // 处理表格选择变化
  const handleAssetSelectionChange = (selection: Asset[]) => {
    selectedAssetIds.value = selection.map((asset) => asset.id)
    formData.assets = selection.map((asset) => ({ id: asset.id }))
  }

  // 设置表格选择状态（用于回显）
  const setTableSelection = () => {
    if (!assetTableRef.value || selectedAssetIds.value.length === 0) return

    nextTick(() => {
      selectedAssetIds.value.forEach((assetId) => {
        const asset = assetList.value.find((item) => item.id === assetId)
        if (asset) {
          assetTableRef.value.toggleRowSelection(asset, true)
        }
      })
    })
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      // 多资产支持
      assets: [] as LifecycleAsset[],
      // 保留兼容性字段
      asset_id: null,
      type: null,
      date: '',
      initiator_id: null,
      content: '',
      assistants: [],
      acceptance_entity_id: null,
      acceptance_personnel_id: null,
      acceptance_time: '',
      attachments: [],
      is_need_attendance: 0,
      // 统一使用 tag_ids
      tag_ids: [] as number[]
    })
    contactList.value = []
    attachmentDetails.value = []
    // 重置资产选择状态
    selectedAssetIds.value = []
    assetSearchKeyword.value = ''
  }

  // 监听批量模式变化
  watch(
    () => props.batchMode,
    (newVal) => {
      if (newVal && props.preselectedAssets) {
        // 批量模式下，将预选资产设置到表单中
        selectedAssetIds.value = props.preselectedAssets.map((asset) => asset.id)
        formData.assets = props.preselectedAssets.map((asset) => ({ id: asset.id }))
      }
    },
    { immediate: true }
  )

  // 监听props变化
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        // 直接赋值，前后端都使用 snake_case
        Object.assign(formData, newData)

        // 字段映射：支持新旧字段名的兼容
        if (newData.initiator_id !== undefined) {
          formData.initiator_id = newData.initiator_id
        } else if (newData.initiator !== undefined) {
          formData.initiator_id = newData.initiator
        }

        if (newData.acceptance_entity_id !== undefined) {
          formData.acceptance_entity_id = newData.acceptance_entity_id
        } else if (newData.acceptance_entity !== undefined) {
          formData.acceptance_entity_id = newData.acceptance_entity
        }

        if (newData.acceptance_personnel_id !== undefined) {
          formData.acceptance_personnel_id = newData.acceptance_personnel_id
        } else if (newData.acceptance_personnel !== undefined) {
          formData.acceptance_personnel_id = newData.acceptance_personnel
        }

        // 如果有验收相关方，加载对应的联系人
        if (formData.acceptance_entity_id) {
          loadContacts(formData.acceptance_entity_id)
        }

        // 处理附件数据
        if (newData.attachment_details && newData.attachment_details.length > 0) {
          // 设置附件详情供组件显示
          attachmentDetails.value = newData.attachment_details
          // formData.attachments 应该是 ID 数组
          formData.attachments = newData.attachments || []
        } else {
          attachmentDetails.value = []
          formData.attachments = []
        }

        // 处理资产数据（多资产支持）
        if (newData.assets && Array.isArray(newData.assets)) {
          formData.assets = [...newData.assets]
          selectedAssetIds.value = newData.assets.map((asset) => asset.id)
          setTableSelection()
        } else if ((newData as any).asset_ids && Array.isArray((newData as any).asset_ids)) {
          // 兼容旧格式 - 将ID数组转换为LifecycleAsset格式
          formData.assets = (newData as any).asset_ids.map((id: number) => ({ id }))
          selectedAssetIds.value = [...(newData as any).asset_ids]
          setTableSelection()
        } else if (newData.asset_id) {
          // 兼容旧的单资产格式
          formData.assets = [{ id: newData.asset_id }]
          selectedAssetIds.value = [newData.asset_id]
          setTableSelection()
        } else {
          formData.assets = []
          selectedAssetIds.value = []
        }

        // 处理标签数据（统一使用 tag_ids）
        if (newData.tag_ids && newData.tag_ids.length > 0) {
          formData.tag_ids = [...newData.tag_ids]
        } else if (newData.tags && newData.tags.length > 0) {
          // 兼容旧的 tags 字段
          formData.tag_ids = [...newData.tags]
        } else {
          formData.tag_ids = []
        }
      } else {
        resetForm()
      }
    },
    { immediate: true }
  )

  // 监听弹窗显示状态
  watch(visible, async (newVal) => {
    if (newVal) {
      // 字典数据只在第一次显示或数据为空时加载（变化较少）
      if (lifecycleTypes.value.length === 0) {
        await loadDictionaries()
      }
      // 每次打开弹窗都重新加载用户列表和实体列表，确保数据最新
      await loadUserList()
      await loadEntityList()
      // 加载资产列表
      await loadAllAssets()
      // 加载标签列表
      await loadTags()
      // 设置表格选择状态（用于编辑时的数据回显）
      nextTick(() => {
        setTableSelection()
      })
    }
  })

  // 监听批量模式和预选资产变化
  watch(
    () => [props.batchMode, props.preselectedAssets],
    ([batchMode, preselectedAssets]) => {
      if (batchMode && preselectedAssets && preselectedAssets.length > 0) {
        // 批量模式下，自动设置选中的资产
        selectedAssetIds.value = preselectedAssets.map((asset) => asset.id)
        formData.assets = preselectedAssets.map((asset) => ({ id: asset.id }))
      }
    },
    { immediate: true }
  )

  // 监听发起人变化，自动移除协助人员中的发起人
  watch(
    () => formData.initiator_id,
    (newInitiator) => {
      if (newInitiator && formData.assistants && formData.assistants.includes(newInitiator)) {
        // 从协助人员中移除发起人
        formData.assistants = formData.assistants.filter((id) => id !== newInitiator)
      }
    }
  )

  // 加载字典数据
  const loadDictionaries = async () => {
    const data = await dictionaryStore.fetchItemsByCode('lifecycle_config')
    lifecycleTypes.value = data || []
  }

  // 加载用户列表
  const loadUserList = async () => {
    const response = await getUserList({ current: 1, size: 1000 })
    userList.value = response?.data || []
  }

  // 加载相关方列表
  const loadEntityList = async () => {
    const response = await getEntityList({ current: 1, size: 1000 })
    entityList.value = response?.data || []
  }

  // 加载联系人列表
  const loadContacts = async (entityId: number) => {
    const response = await getAcceptancePersonnel(entityId)
    contactList.value = response || []
  }

  // 验收相关方改变
  const handleEntityChange = (entityId: number | null) => {
    formData.acceptance_personnel_id = null
    contactList.value = []
    if (entityId) {
      loadContacts(entityId)
    }
  }

  // 加载所有资产
  const loadAllAssets = async () => {
    loadingAssets.value = true
    try {
      const response = await getAssetList({
        per_page: 10000, // 最大分页，一次加载所有
        page: 1
      })
      assetList.value = response.data || []
    } catch (error) {
      console.error('加载资产失败:', error)
      ElMessage.error('加载资产数据失败')
    } finally {
      loadingAssets.value = false
    }
  }

  // 加载标签列表
  const loadTags = async () => {
    try {
      // 获取所有标签
      const response = await getTagList({ page: 1, per_page: 1000 })
      allTags.value = response.data || []
    } catch (error) {
      console.error('加载标签失败:', error)
    }
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    resetForm()
  }

  // 批量提交处理
  const handleBatchSubmit = async () => {
    // 批量模式：创建一条生命周期记录，关联所有选中的资产
    const submitData: LifecycleFormData = {
      assets: props.preselectedAssets?.map((asset) => asset.id) || [], // 使用所有预选资产的ID
      type: formData.type!,
      date: formData.date || '',
      initiator_id: formData.initiator_id!,
      content: formData.content || '',
      assistants: formData.assistants || [],
      acceptance_entity_id: formData.acceptance_entity_id!,
      acceptance_personnel_id: formData.acceptance_personnel_id!,
      acceptance_time: formData.acceptance_time || '',
      attachments: formData.attachments || [],
      tag_ids: formData.tag_ids || [],
      is_need_attendance: formData.is_need_attendance // 添加考勤管理字段
    }

    await createLifecycle(submitData)
    ElMessage.success(`成功为 ${props.preselectedAssets?.length || 0} 个资产创建生命周期计划`)

    emit('success')
    handleClose()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate()

    if (props.batchMode && props.preselectedAssets) {
      // 批量模式处理
      await handleBatchSubmit()
    } else {
      // 普通模式处理
      const submitData: LifecycleFormData = {
        assets: selectedAssetIds.value, // 直接使用ID数组
        type: formData.type!,
        date: formData.date || '',
        initiator_id: formData.initiator_id!,
        content: formData.content || '',
        assistants: formData.assistants || [],
        acceptance_entity_id: formData.acceptance_entity_id!,
        acceptance_personnel_id: formData.acceptance_personnel_id!,
        acceptance_time: formData.acceptance_time || '',
        attachments: formData.attachments || [],
        tag_ids: formData.tag_ids || [],
        is_need_attendance: formData.is_need_attendance // 添加考勤管理字段
      }

      if (props.type === 'add') {
        await createLifecycle(submitData)
        ElMessage.success('新增成功')
      } else {
        await updateLifecycle(formData.id!, submitData)
        ElMessage.success('编辑成功')
      }
      emit('success')
      handleClose()
    }
  }
</script>

<style lang="scss" scoped>
  .upload-demo {
    width: 100%;
  }

  // 资产选择器样式
  .asset-selector-container {
    width: 100%;
    padding: 12px;
    background-color: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;

    .container-header {
      margin-bottom: 16px;

      .container-label {
        font-size: 14px;
        font-weight: 600;
        line-height: 32px;
        color: var(--el-text-color-primary);
      }
    }

    .search-section {
      margin-bottom: 8px;

      .selection-info {
        margin-top: 8px;
        font-size: 14px;

        .selected-count {
          font-weight: 500;
          color: var(--el-text-color-regular);
        }
      }
    }

    .loading-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: center;
      color: var(--el-text-color-regular);
      transform: translate(-50%, -50%);

      .el-icon {
        font-size: 24px;
      }
    }

    // 表格样式优化
    :deep(.el-table) {
      border-radius: 4px;

      .el-table__header {
        th {
          font-weight: 600;
          background-color: var(--el-fill-color-light);
        }
      }

      .el-table__body {
        tr:hover > td {
          background-color: var(--el-fill-color-lighter);
        }
      }

      // 空状态样式
      .el-table__empty-block {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 120px;
        color: var(--el-text-color-placeholder);
      }
    }
  }
</style>
