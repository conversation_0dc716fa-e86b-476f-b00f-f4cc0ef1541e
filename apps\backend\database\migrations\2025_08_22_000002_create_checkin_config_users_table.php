<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkin_config_users', function (Blueprint $table) {
            $table->id()->comment('配置ID');
            $table->unsignedBigInteger('checkin_config_id')->comment('关联打卡配置ID');
            $table->unsignedBigInteger('user_id')->comment('打卡人员ID');

            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            $table->foreign('checkin_config_id')->references('id')->on('checkin_configs')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkin_config_users');
    }
};
