<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EntityTemplateExport implements FromArray, ShouldAutoSize, WithColumnWidths, WithHeadings, WithStyles
{
    /**
     * 导出数据
     */
    public function array(): array
    {
        return [
            [
                '北京凯吉特医药科技发展有限公司', // 名称
                '91110108MA01A2B3C4', // 税号
                '生产厂商', // 类型
                '北京市海淀区中关村大街1号', // 地址
                '010-12345678', // 联系电话
                '生产厂商', // 备注
            ],
            [
                '北京康达和美经贸有限公司', // 名称
                '91110108MA01A2B3C5', // 税号
                '供应商', // 类型
                '北京市朝阳区建国门外大街2号', // 地址
                '010-87654321', // 联系电话
                '供应商', // 备注
            ],
        ];
    }

    /**
     * 表头
     */
    public function headings(): array
    {
        return [
            '名称',
            '税号',
            '类型',
            '地址',
            '联系电话',
            '备注',
        ];
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet)
    {
        $totalColumns = count($this->headings());
        $lastColumn = $this->getColumnLetter($totalColumns);

        // 添加说明行
        $sheet->insertNewRowBefore(1, 1);

        // 第一行：标题
        $sheet->mergeCells("A1:{$lastColumn}1");
        $sheet->setCellValue('A1', '相关方导入模板');
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // 设置表头样式（第2行）
        $sheet->getStyle("A2:{$lastColumn}2")->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // 设置示例数据行样式
        $sheet->getStyle("A3:{$lastColumn}4")->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F2F2F2'],
            ],
        ]);

        // 设置所有单元格的边框
        $sheet->getStyle("A2:{$lastColumn}4")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // 调整行高
        $sheet->getRowDimension(1)->setRowHeight(30);
        $sheet->getRowDimension(2)->setRowHeight(25);
        $sheet->getRowDimension(3)->setRowHeight(20);
        $sheet->getRowDimension(4)->setRowHeight(20);
    }

    /**
     * 设置列宽
     */
    public function columnWidths(): array
    {
        return [
            'A' => 40, // 名称
            'B' => 25, // 税号
            'C' => 15, // 类型
            'D' => 40, // 地址
            'E' => 20, // 联系电话
            'F' => 30, // 备注
        ];
    }

    /**
     * 获取列字母
     */
    private function getColumnLetter($columnNumber)
    {
        $letter = '';
        while ($columnNumber > 0) {
            $columnNumber--;
            $letter = chr(65 + ($columnNumber % 26)).$letter;
            $columnNumber = intval($columnNumber / 26);
        }

        return $letter;
    }
}
