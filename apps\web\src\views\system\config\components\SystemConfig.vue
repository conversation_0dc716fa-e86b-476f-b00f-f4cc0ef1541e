<template>
  <div class="system-config">
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px" class="config-form">
      <ElFormItem label="系统名称" prop="system_name">
        <ElInput
          v-model="formData.system_name"
          placeholder="请输入系统名称"
          clearable
          maxlength="50"
        />
      </ElFormItem>

      <ElFormItem label="系统Logo" prop="system_logo">
        <AttachmentUpload
          v-model="logoAttachments"
          :attachments="currentLogoAttachments"
          :limit="1"
          :accept="'image/*'"
          list-type="picture-card"
        />
        <div class="el-upload__tip">建议尺寸：200×60px，支持 JPG、PNG 格式</div>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
  // Vue 核心
  import { ref, onMounted } from 'vue'

  // UI 框架
  import { ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // 内部组件
  import { AttachmentUpload } from '@/components/custom/upload'

  // Store
  import { useConfigStore } from '@/store/modules/config'

  // Composables
  import { useSingleAttachment } from '@/composables/useAttachment'

  // 类型
  import type { SystemConfig } from '@/types/api'

  const configStore = useConfigStore()
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = ref<SystemConfig>({
    system_name: '',
    system_logo: null
  })

  // 使用附件处理 composable
  const {
    attachmentIds: logoAttachments,
    attachmentDetails: currentLogoAttachments,
    setAttachments: setLogoAttachments,
    getSubmitValue: getLogoSubmitValue
  } = useSingleAttachment()

  // 表单验证规则
  const rules: FormRules = {
    system_name: [
      { required: true, message: '请输入系统名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ]
  }

  // 加载配置
  const loadConfig = async () => {
    const config = await configStore.fetchConfig()

    // 设置表单数据
    formData.value = {
      system_name: config.system.system_name,
      system_logo: null // 将通过 composable 处理
    }

    // 使用 composable 处理附件数据
    setLogoAttachments(config.system.system_logo)
  }

  onMounted(() => {
    loadConfig()
  })

  // 暴露获取表单数据的方法
  const getFormData = async () => {
    // 先验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return null

    // 返回处理后的数据
    return {
      system_name: formData.value.system_name,
      system_logo: getLogoSubmitValue()
    }
  }

  // 暴露方法给父组件
  defineExpose({
    getFormData
  })
</script>

<style lang="scss" scoped>
  .system-config {
    .config-form {
      max-width: 500px;
    }

    .el-upload__tip {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
</style>
