<template>
  <div class="tag-selector">
    <div class="tag-selector-header">
      <span class="tag-count">已选择：{{ selectedTagsCount }}个标签</span>
      <ElButton
        v-if="availableTags.length > 0"
        type="primary"
        link
        size="small"
        @click="toggleTagSelector"
      >
        {{ showTagSelector ? '收起' : '展开' }}标签选择器
        <ElIcon :class="{ 'rotate-180': showTagSelector }">
          <ArrowDown />
        </ElIcon>
      </ElButton>
    </div>

    <!-- 无标签提示 -->
    <div v-if="availableTags.length === 0" class="no-tags-tip">
      <ElAlert type="info" :closable="false" show-icon>
        <template #title>{{ placeholder || '暂无可选标签' }}</template>
      </ElAlert>
    </div>

    <!-- 已选择标签预览 -->
    <div v-if="selectedTags.length > 0" class="selected-tags-preview">
      <ElTag
        v-for="tag in selectedTags"
        :key="tag.id"
        closable
        @close="removeTag(tag.id)"
        class="selected-tag"
      >
        {{ tag.name }}
      </ElTag>
    </div>

    <!-- 标签选择面板 -->
    <ElCollapse
      v-if="showTagSelector && availableTags.length > 0"
      v-model="activeTagCategories"
      class="tag-selector-panel"
    >
      <ElCollapseItem
        v-for="category in tagCategories"
        :key="category.name"
        :title="`${category.name} (${category.tags.length})`"
        :name="category.name"
      >
        <template #title>
          <div class="category-header">
            <span>{{ category.name }} ({{ category.tags.length }})</span>
            <ElButton type="primary" link size="small" @click.stop="toggleCategoryTags(category)">
              {{ isCategoryAllSelected(category) ? '取消全选' : '全选' }}
            </ElButton>
          </div>
        </template>

        <div class="tag-list">
          <ElCheckbox
            v-for="tag in category.tags"
            :key="tag.id"
            v-model="tagSelections[tag.id]"
            :label="tag.name"
            :disabled="disabled"
            @change="handleTagChange(tag.id)"
          />
        </div>
      </ElCollapseItem>
    </ElCollapse>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'TagSelector' })

  import { ref, computed, watch } from 'vue'
  import { ArrowDown } from '@element-plus/icons-vue'
  import type { Tag } from '@/types/api/tag'

  interface Props {
    tags: Tag[] // 可选标签列表
    placeholder?: string // 无标签时的提示文字
    disabled?: boolean // 是否禁用
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '暂无可选标签',
    disabled: false
  })

  // 使用 defineModel 实现双向绑定
  const selectedTagIds = defineModel<number[]>({ default: [] })

  // 状态管理
  const showTagSelector = ref(false)
  const activeTagCategories = ref<string[]>([])
  const tagSelections = ref<Record<number, boolean>>({})

  // 计算属性
  const availableTags = computed(() => props.tags)

  // 标签按分类分组
  const tagCategories = computed(() => {
    const categories = new Map<string, Tag[]>()

    availableTags.value.forEach((tag) => {
      if (!categories.has(tag.category)) {
        categories.set(tag.category, [])
      }
      categories.get(tag.category)!.push(tag)
    })

    return Array.from(categories.entries())
      .map(([name, tags]) => ({
        name,
        tags: tags.sort((a, b) => a.name.localeCompare(b.name))
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  })

  // 已选择的标签详情
  const selectedTags = computed(() => {
    return availableTags.value.filter((tag) => selectedTagIds.value.includes(tag.id))
  })

  // 已选择标签数量
  const selectedTagsCount = computed(() => {
    return selectedTagIds.value.length
  })

  // 初始化标签选择状态
  const initTagSelections = () => {
    tagSelections.value = {}
    availableTags.value.forEach((tag) => {
      tagSelections.value[tag.id] = selectedTagIds.value.includes(tag.id)
    })
  }

  // 监听标签列表变化
  watch(
    () => availableTags.value,
    () => {
      initTagSelections()
      // 重置展开状态
      showTagSelector.value = false
      activeTagCategories.value = []
    },
    { immediate: true }
  )

  // 监听选中的标签变化
  watch(
    () => selectedTagIds.value,
    () => {
      initTagSelections()
    },
    { immediate: true }
  )

  // 切换标签选择器显示状态
  const toggleTagSelector = () => {
    showTagSelector.value = !showTagSelector.value
    if (showTagSelector.value && activeTagCategories.value.length === 0) {
      // 默认展开第一个分类
      if (tagCategories.value.length > 0) {
        activeTagCategories.value = [tagCategories.value[0].name]
      }
    }
  }

  // 处理标签选择变化
  const handleTagChange = (tagId: number) => {
    if (tagSelections.value[tagId]) {
      // 添加标签
      if (!selectedTagIds.value.includes(tagId)) {
        selectedTagIds.value = [...selectedTagIds.value, tagId]
      }
    } else {
      // 移除标签
      selectedTagIds.value = selectedTagIds.value.filter((id) => id !== tagId)
    }
  }

  // 移除单个标签
  const removeTag = (tagId: number) => {
    selectedTagIds.value = selectedTagIds.value.filter((id) => id !== tagId)
    tagSelections.value[tagId] = false
  }

  // 检查分类是否全选
  const isCategoryAllSelected = (category: { tags: Tag[] }) => {
    return category.tags.every((tag) => tagSelections.value[tag.id])
  }

  // 切换分类全选状态
  const toggleCategoryTags = (category: { tags: Tag[] }) => {
    const allSelected = isCategoryAllSelected(category)

    if (allSelected) {
      // 取消全选：移除该分类的所有标签
      const categoryTagIds = category.tags.map((tag) => tag.id)
      selectedTagIds.value = selectedTagIds.value.filter((id) => !categoryTagIds.includes(id))
      // 更新选择状态
      category.tags.forEach((tag) => {
        tagSelections.value[tag.id] = false
      })
    } else {
      // 全选：添加该分类的所有标签
      const categoryTagIds = category.tags.map((tag) => tag.id)
      const newTagIds = categoryTagIds.filter((id) => !selectedTagIds.value.includes(id))
      selectedTagIds.value = [...selectedTagIds.value, ...newTagIds]
      // 更新选择状态
      category.tags.forEach((tag) => {
        tagSelections.value[tag.id] = true
      })
    }
  }
</script>

<style lang="scss" scoped>
  .tag-selector {
    .tag-selector-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .tag-count {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }

      .rotate-180 {
        transition: transform 0.3s;
        transform: rotate(180deg);
      }
    }

    .no-tags-tip {
      margin-bottom: 8px;
    }

    .selected-tags-preview {
      margin-bottom: 12px;

      .selected-tag {
        margin-right: 8px;
        margin-bottom: 4px;
      }
    }

    .tag-selector-panel {
      padding: 12px; // 增加内边距
      border: 1px solid var(--el-border-color);
      border-radius: 4px;

      .category-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        padding: 12px; // 增加内边距，解决贴在一起的问题

        :deep(.el-checkbox) {
          margin-right: 0;

          .el-checkbox__label {
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
