<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">代办事项</h4>
        <p class="subtitle"
          >待处理<span class="text-danger">{{ pendingCount }}</span></p
        >
      </div>
    </div>

    <div class="list">
      <div v-for="(item, index) in list" :key="index" class="todo-item">
        <div class="content">
          <div class="title-row">
            <span class="priority-dot" :class="getPriorityClass(item.priority)"></span>
            <p class="title">{{ item.title }}</p>
          </div>
          <p class="date subtitle">{{ item.time }}</p>
        </div>
        <el-checkbox v-model="item.completed" @change="updatePendingCount" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed } from 'vue'

  // 待办事项优先级枚举
  enum Priority {
    HIGH = 'high',
    MEDIUM = 'medium',
    LOW = 'low'
  }

  // 待办事项类型定义
  interface TodoItem {
    id: number
    title: string
    time: string
    priority: Priority
    completed: boolean
    type: string
  }

  // 业务相关的待办事项数据
  const list = reactive<TodoItem[]>([
    {
      id: 1,
      title: '审批生产设备资产申请（编号：AS-2024-001）',
      time: '2024-08-15 10:30',
      priority: Priority.HIGH,
      completed: false,
      type: 'asset_approval'
    },
    {
      id: 2,
      title: '处理服务器高温告警（机房A-01）',
      time: '2024-08-15 09:15',
      priority: Priority.HIGH,
      completed: false,
      type: 'device_alert'
    },
    {
      id: 3,
      title: '跟进项目里程碑节点（智能制造系统）',
      time: '2024-08-15 14:00',
      priority: Priority.MEDIUM,
      completed: false,
      type: 'lifecycle_follow'
    },
    {
      id: 4,
      title: '系统数据库维护计划执行',
      time: '2024-08-16 02:00',
      priority: Priority.MEDIUM,
      completed: true,
      type: 'system_maintenance'
    },
    {
      id: 5,
      title: '分配新工单（网络设备故障报修）',
      time: '2024-08-15 11:20',
      priority: Priority.MEDIUM,
      completed: false,
      type: 'work_order'
    },
    {
      id: 6,
      title: '办公软件许可证到期续费提醒',
      time: '2024-08-20 00:00',
      priority: Priority.HIGH,
      completed: false,
      type: 'renewal_reminder'
    },
    {
      id: 7,
      title: '处理网络连接异常告警（办公区B栋）',
      time: '2024-08-15 08:45',
      priority: Priority.LOW,
      completed: true,
      type: 'device_alert'
    },
    {
      id: 8,
      title: '检查备份系统运行状态',
      time: '2024-08-15 16:00',
      priority: Priority.LOW,
      completed: false,
      type: 'system_maintenance'
    }
  ])

  // 计算待处理事项数量
  const pendingCount = computed(() => {
    return list.filter((item) => !item.completed).length
  })

  // 更新待处理数量（当复选框状态改变时触发）
  const updatePendingCount = () => {
    // 这里可以添加额外的逻辑，比如保存到本地存储或发送到后端
  }

  // 获取优先级对应的CSS类
  const getPriorityClass = (priority: Priority): string => {
    switch (priority) {
      case Priority.HIGH:
        return 'priority-high'
      case Priority.MEDIUM:
        return 'priority-medium'
      case Priority.LOW:
        return 'priority-low'
      default:
        return 'priority-medium'
    }
  }
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 510px;
    padding: 0 25px;

    .list {
      height: calc(100% - 90px);
      margin-top: 10px;
      overflow: hidden;

      .todo-item {
        position: relative;
        display: flex;
        align-items: center;
        height: 70px;
        overflow: hidden;
        border-bottom: 1px solid var(--art-border-color);

        .content {
          display: flex;
          flex: 1;
          flex-direction: column;
          justify-content: center;
          padding-right: 40px;

          .title-row {
            display: flex;
            gap: 8px;
            align-items: center;

            .priority-dot {
              flex-shrink: 0;
              width: 8px;
              height: 8px;
              border-radius: 50%;

              &.priority-high {
                background-color: #f56c6c; // 红色 - 高优先级
              }

              &.priority-medium {
                background-color: #e6a23c; // 橙色 - 中优先级
              }

              &.priority-low {
                background-color: #67c23a; // 绿色 - 低优先级
              }
            }

            .title {
              margin: 0;
              font-size: 14px;
              line-height: 1.4;
              color: var(--el-text-color-primary);
            }
          }

          .date {
            margin: 6px 0 0 16px; // 左边距对齐圆点后的位置
            font-size: 12px;
            font-weight: 400;
            color: var(--el-text-color-regular);
          }
        }

        .el-checkbox {
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
        }

        // 已完成的事项样式
        &:has(.el-checkbox.is-checked) {
          .content .title-row .title {
            color: var(--el-text-color-disabled);
            text-decoration: line-through;
          }

          .content .date {
            color: var(--el-text-color-disabled);
          }
        }
      }
    }
  }
</style>
