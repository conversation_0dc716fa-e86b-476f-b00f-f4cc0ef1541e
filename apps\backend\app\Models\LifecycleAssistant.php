<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $lifecycle_id 生命周期ID
 * @property int $user_id 用户ID
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\Lifecycle $lifecycle
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant whereLifecycleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleAssistant whereUserId($value)
 *
 * @mixin \Eloquent
 */
class LifecycleAssistant extends BaseModel
{
    use HasAttachments, HasFactory;

    protected $table = 'lifecycle_assistants';

    protected $fillable = [
        'lifecycle_id',
        'user_id',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取所属的生命周期
     */
    public function lifecycle(): BelongsTo
    {
        return $this->belongsTo(Lifecycle::class, 'lifecycle_id');
    }

    /**
     * 用户信息
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
