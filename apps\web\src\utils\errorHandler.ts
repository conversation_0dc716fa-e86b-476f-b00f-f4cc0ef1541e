import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 统一处理 API 错误
 * @param error 错误对象
 * @param defaultMessage 默认错误提示
 * @param showMessage 是否显示错误提示
 */
export const handleApiError = (
  error: any,
  defaultMessage: string = '操作失败',
  showMessage: boolean = true
): void => {
  console.error(defaultMessage, error)

  if (!showMessage) return

  // 获取错误信息
  const message = error?.response?.data?.message || error?.message || defaultMessage

  ElMessage.error(message)
}

/**
 * 处理成功提示
 * @param message 成功提示信息
 */
export const handleSuccess = (message: string = '操作成功'): void => {
  ElMessage.success(message)
}

/**
 * 确认操作
 * @param message 确认提示信息
 * @param title 标题
 * @returns Promise<boolean>
 */
export const confirmAction = async (message: string, title: string = '提示'): Promise<boolean> => {
  try {
    await ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    return true
  } catch {
    return false
  }
}
