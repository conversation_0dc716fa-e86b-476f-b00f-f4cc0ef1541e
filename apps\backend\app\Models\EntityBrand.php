<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $entity_id 相关方ID
 * @property string $name 品牌名称
 * @property string|null $logo 品牌Logo图片URL
 * @property string|null $description 品牌描述
 * @property int $sort_order 排序顺序
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \App\Models\Entity $entity
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityBrand whereUpdatedBy($value)
 *
 * @mixin \Eloquent
 */
class EntityBrand extends BaseModel
{
    use HasAttachments, HasFactory;

    protected $fillable = [
        'entity_id',
        'name',
        'description',
        'sort_order',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'sort_order' => 'integer',
    ];

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class);
    }
}
