<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EntityContact>
 */
class EntityContactFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $medicalPositions = [
            '设备科主任',
            '设备科工程师',
            '维保工程师',
            '技术支持工程师',
            '销售经理',
            '售后服务经理',
            '质控专员',
            '采购专员',
            '医学工程师',
            '临床工程师',
            '维修技师',
            '设备管理员'
        ];

        $departments = [
            '设备科',
            '医学工程部',
            '采购部',
            '质控科',
            '临床工程科',
            '维保部',
            '技术服务部',
            '销售部',
            '客服部',
            '工程部'
        ];

        return [
            'entity_id' => \App\Models\Entity::factory(),
            'name' => $this->faker->name(),
            'phone' => $this->faker->phoneNumber(),
            'position' => $this->faker->randomElement($medicalPositions),
            'department' => $this->faker->randomElement($departments),
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
}
