<?php

namespace App\Services;

use App\Models\EntityBrand;
use Illuminate\Database\Eloquent\Collection;

class BrandService
{
    /**
     * 获取所有品牌列表
     *
     * @param string|null $keyword 搜索关键字
     * @return Collection
     */
    public function getAllBrands(?string $keyword = null): Collection
    {
        $query = EntityBrand::with('entity:id,name')
            ->orderBy('sort_order', 'desc')
            ->orderBy('name', 'asc');

        if ($keyword) {
            $query->where(function ($subQuery) use ($keyword) {
                $subQuery->where('name', 'like', "%{$keyword}%")
                    ->orWhereHas('entity', function ($entityQuery) use ($keyword) {
                        $entityQuery->where('name', 'like', "%{$keyword}%");
                    });
            });
        }

        return $query->get();
    }
}