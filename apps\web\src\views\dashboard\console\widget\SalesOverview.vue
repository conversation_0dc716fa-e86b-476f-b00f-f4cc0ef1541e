<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">设备运行趋势</h4>
        <div class="stats">
          <span class="stat-item">今日在线率：<span class="text-success">94%</span></span>
          <span class="stat-item">今日告警：<span class="text-danger">8条</span></span>
          <span class="stat-item">7日平均在线率：<span class="text-info">90%</span></span>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="chart" v-loading="loading" />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useChart, useChartOps } from '@/composables/useChart'
  import type { EChartsOption } from 'echarts'

  defineOptions({ name: 'DeviceTrendOverview' })

  // 状态
  const loading = ref(false)

  // 图表实例
  const { chartRef, initChart, destroyChart } = useChart()
  const { colors } = useChartOps()

  // 模拟数据 - 最近7天
  const generateDateLabels = () => {
    const labels: string[] = []
    const today = new Date()
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      labels.push(`${date.getMonth() + 1}/${date.getDate()}`)
    }
    return labels
  }

  // 数据配置
  const xAxisData = generateDateLabels()
  const onlineRateData = [85, 88, 90, 92, 89, 91, 94] // 设备在线率百分比
  const alarmCountData = [23, 18, 15, 12, 20, 16, 8] // 告警数量

  // 图表配置
  const getChartOptions = (): EChartsOption => {
    return {
      backgroundColor: 'transparent',
      grid: {
        top: 30,
        right: 80,
        bottom: 30,
        left: 60,
        containLabel: false
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ddd',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        },
        formatter: (params: any) => {
          const date = params[0].axisValue
          let result = `${date}<br/>`
          params.forEach((param: any) => {
            const value = param.seriesName === '设备在线率' ? `${param.value}%` : `${param.value}条`
            result += `${param.marker}${param.seriesName}: ${value}<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['设备在线率', '告警数量'],
        top: 5,
        right: 'center',
        textStyle: {
          color: '#666'
        }
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E8EB'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#999',
          fontSize: 12
        }
      },
      yAxis: [
        {
          // 左Y轴 - 在线率
          type: 'value',
          name: '在线率(%)',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          min: 0,
          max: 100,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[0] // 绿色
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
            formatter: '{value}%'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F0F2F5',
              type: 'dashed'
            }
          }
        },
        {
          // 右Y轴 - 告警数量
          type: 'value',
          name: '告警数量',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ff4d4f' // 红色
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
            formatter: '{value}条'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '设备在线率',
          type: 'line',
          yAxisIndex: 0, // 使用左Y轴
          data: onlineRateData,
          smooth: true,
          lineStyle: {
            color: colors[0], // 绿色
            width: 3
          },
          itemStyle: {
            color: colors[0],
            borderWidth: 2,
            borderColor: '#fff'
          },
          symbolSize: 6,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: `${colors[0]}20`
                },
                {
                  offset: 1,
                  color: `${colors[0]}05`
                }
              ]
            }
          },
          animationDuration: 2000,
          animationEasing: 'cubicOut'
        },
        {
          name: '告警数量',
          type: 'bar',
          yAxisIndex: 1, // 使用右Y轴
          data: alarmCountData,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#ff7875'
                },
                {
                  offset: 1,
                  color: '#ff4d4f'
                }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '30%',
          animationDuration: 1500,
          animationEasing: 'cubicOut',
          animationDelay: (idx: number) => idx * 100
        }
      ]
    }
  }

  // 初始化图表
  const initChartData = () => {
    loading.value = true

    setTimeout(() => {
      initChart(getChartOptions())
      loading.value = false
    }, 500)
  }

  onMounted(() => {
    initChartData()
  })

  onUnmounted(() => {
    destroyChart()
  })
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 420px;
    padding: 20px 0 30px;

    .card-header {
      padding: 0 18px !important;

      .title {
        .box-title {
          margin-bottom: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .stats {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          font-size: 12px;
          color: #666;

          .stat-item {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .text-success {
              margin-left: 4px;
              font-weight: 600;
              color: var(--el-color-success) !important;
            }

            .text-danger {
              margin-left: 4px;
              font-weight: 600;
              color: var(--el-color-danger) !important;
            }

            .text-info {
              margin-left: 4px;
              font-weight: 600;
              color: var(--el-color-info) !important;
            }
          }
        }
      }
    }

    .chart {
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 80px);
      padding: 10px 20px 0;
    }
  }

  @media screen and (max-width: $device-phone) {
    .card {
      height: 320px;

      .card-header {
        .title {
          .stats {
            flex-direction: column;
            gap: 6px;
            align-items: flex-start;
          }
        }
      }

      .chart {
        height: calc(100% - 100px);
      }
    }
  }

  @media screen and (width <= 576px) {
    .card {
      .card-header {
        .title {
          .stats {
            .stat-item {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
</style>
