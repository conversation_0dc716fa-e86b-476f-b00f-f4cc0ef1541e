<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Asset>
 */
class AssetFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $medicalEquipment = [
            'CT机',
            'MRI核磁共振',
            'X光机',
            'B超机',
            '彩色多普勒超声',
            '生化分析仪',
            '血球计数仪',
            '尿液分析仪',
            '心电监护仪',
            '呼吸机',
            '麻醉机',
            '除颤仪',
            '手术灯',
            '电刀',
            '腹腔镜',
            '胃镜',
            '肠镜',
            '透析机',
            '输液泵',
            '注射泵',
            '心电图机',
            '脑电图机',
            '肌电图机',
            '骨密度仪',
            '胎心监护仪'
        ];

        $assetStatuses = ['new_unstocked', 'in_use', 'pending_check', 'scrap_registered', 'under_repair'];
        $assetSources = ['produce', 'purchase', 'transfer', 'donate'];
        $assetConditions = ['brand_new', 'second_hand', 'refurbished'];

        return [
            'name' => $this->faker->randomElement($medicalEquipment),
            'brand_id' => \App\Models\EntityBrand::factory(),
            'model' => $this->faker->regexify('[A-Z0-9]{3,8}-[0-9]{2,4}'),
            'serial_number' => $this->faker->uuid(),
            'asset_category_ids' => [1, 2],
            'asset_source' => $this->faker->randomElement($assetSources),
            'asset_status' => $this->faker->randomElement($assetStatuses),
            'asset_condition' => $this->faker->randomElement($assetConditions),
            'parent_id' => null,
            'region_code' => \App\Models\Region::where('deep', 2)->inRandomOrder()->first()->ext_id,
            'detailed_address' => $this->faker->streetAddress(),
            'start_date' => $this->faker->unixTime(),
            'warranty_period' => $this->faker->numberBetween(12, 60),
            'warranty_alert' => $this->faker->numberBetween(30, 90),
            'maintenance_cycle' => $this->faker->numberBetween(30, 365),
            'expected_years' => $this->faker->numberBetween(5, 15),
            'related_entities' => null,
            'remark' => $this->faker->sentence(),
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
}
