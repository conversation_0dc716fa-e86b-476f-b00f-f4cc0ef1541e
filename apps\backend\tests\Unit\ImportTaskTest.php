<?php

namespace Tests\Unit;

use App\Models\ImportTask;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ImportTaskTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_import_task()
    {
        $user = User::factory()->create();

        $task = ImportTask::create([
            'type' => 'asset',
            'file_path' => 'imports/test.xlsx',
            'original_filename' => 'test.xlsx',
            'status' => ImportTask::STATUS_PENDING,
            'created_by' => $user->id,
        ]);

        $this->assertInstanceOf(ImportTask::class, $task);
        $this->assertEquals('asset', $task->type);
        $this->assertEquals(ImportTask::STATUS_PENDING, $task->status);
        $this->assertEquals($user->id, $task->created_by);
    }

    /** @test */
    public function it_can_mark_task_as_processing()
    {
        $task = ImportTask::factory()->create([
            'status' => ImportTask::STATUS_PENDING,
        ]);

        $beforeTime = time();
        $task->markAsProcessing();
        $afterTime = time();

        $this->assertEquals(ImportTask::STATUS_PROCESSING, $task->status);
        $this->assertGreaterThanOrEqual($beforeTime, $task->started_at);
        $this->assertLessThanOrEqual($afterTime, $task->started_at);
    }

    /** @test */
    public function it_can_mark_task_as_completed()
    {
        $task = ImportTask::factory()->create([
            'status' => ImportTask::STATUS_PROCESSING,
        ]);

        $result = [
            'total_rows' => 100,
            'success_rows' => 95,
            'failed_rows' => 5,
            'errors' => [
                ['row' => 10, 'error' => 'Test error'],
            ],
            'summary' => 'Import completed with 95% success rate',
        ];

        $beforeTime = time();
        $task->markAsCompleted($result);
        $afterTime = time();

        $this->assertEquals(ImportTask::STATUS_COMPLETED, $task->status);
        $this->assertEquals(100, $task->total_rows);
        $this->assertEquals(95, $task->success_rows);
        $this->assertEquals(5, $task->failed_rows);
        $this->assertEquals($result['errors'], $task->error_details);
        $this->assertEquals($result['summary'], $task->summary);
        $this->assertGreaterThanOrEqual($beforeTime, $task->completed_at);
        $this->assertLessThanOrEqual($afterTime, $task->completed_at);
    }

    /** @test */
    public function it_can_mark_task_as_failed_with_string_error()
    {
        $task = ImportTask::factory()->create([
            'status' => ImportTask::STATUS_PROCESSING,
        ]);

        $errorMessage = 'File not found';

        $beforeTime = time();
        $task->markAsFailed($errorMessage);
        $afterTime = time();

        $this->assertEquals(ImportTask::STATUS_FAILED, $task->status);
        $this->assertEquals([['error' => $errorMessage]], $task->error_details);
        $this->assertGreaterThanOrEqual($beforeTime, $task->completed_at);
        $this->assertLessThanOrEqual($afterTime, $task->completed_at);
    }

    /** @test */
    public function it_can_mark_task_as_failed_with_array_error()
    {
        $task = ImportTask::factory()->create([
            'status' => ImportTask::STATUS_PROCESSING,
        ]);

        $errorDetails = [
            'error' => 'Database connection failed',
            'code' => 'DB_ERROR',
            'details' => 'Connection timeout',
        ];

        $task->markAsFailed($errorDetails);

        $this->assertEquals(ImportTask::STATUS_FAILED, $task->status);
        $this->assertEquals($errorDetails, $task->error_details);
    }

    /** @test */
    public function it_can_update_progress()
    {
        $task = ImportTask::factory()->create([
            'total_rows' => 0,
            'success_rows' => 0,
            'failed_rows' => 0,
        ]);

        $task->updateProgress(100, 80, 20);

        $this->assertEquals(100, $task->total_rows);
        $this->assertEquals(80, $task->success_rows);
        $this->assertEquals(20, $task->failed_rows);
    }

    /** @test */
    public function it_belongs_to_creator()
    {
        $user = User::factory()->create();
        $task = ImportTask::factory()->create([
            'created_by' => $user->id,
        ]);

        $this->assertInstanceOf(User::class, $task->creator);
        $this->assertEquals($user->id, $task->creator->id);
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $task = ImportTask::factory()->create([
            'total_rows' => '100',
            'success_rows' => '95',
            'failed_rows' => '5',
            'error_details' => ['error' => 'test'],
            'started_at' => time(),
            'completed_at' => time(),
        ]);

        $this->assertIsInt($task->total_rows);
        $this->assertIsInt($task->success_rows);
        $this->assertIsInt($task->failed_rows);
        $this->assertIsArray($task->error_details);
        $this->assertIsInt($task->started_at);
        $this->assertIsInt($task->completed_at);
    }

    /** @test */
    public function it_has_correct_status_constants()
    {
        $this->assertEquals('pending', ImportTask::STATUS_PENDING);
        $this->assertEquals('processing', ImportTask::STATUS_PROCESSING);
        $this->assertEquals('completed', ImportTask::STATUS_COMPLETED);
        $this->assertEquals('failed', ImportTask::STATUS_FAILED);
    }

    /** @test */
    public function it_can_filter_by_type()
    {
        ImportTask::factory()->create(['type' => 'asset']);
        ImportTask::factory()->create(['type' => 'category']);
        ImportTask::factory()->create(['type' => 'asset']);

        $assetTasks = ImportTask::where('type', 'asset')->get();
        $categoryTasks = ImportTask::where('type', 'category')->get();

        $this->assertCount(2, $assetTasks);
        $this->assertCount(1, $categoryTasks);
    }

    /** @test */
    public function it_can_filter_by_status()
    {
        ImportTask::factory()->create(['status' => ImportTask::STATUS_PENDING]);
        ImportTask::factory()->create(['status' => ImportTask::STATUS_COMPLETED]);
        ImportTask::factory()->create(['status' => ImportTask::STATUS_PENDING]);

        $pendingTasks = ImportTask::where('status', ImportTask::STATUS_PENDING)->get();
        $completedTasks = ImportTask::where('status', ImportTask::STATUS_COMPLETED)->get();

        $this->assertCount(2, $pendingTasks);
        $this->assertCount(1, $completedTasks);
    }
}
