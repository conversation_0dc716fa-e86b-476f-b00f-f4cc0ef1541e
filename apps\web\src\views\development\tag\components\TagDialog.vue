<template>
  <ElDialog v-model="visible" :title="dialogTitle" width="500px" :close-on-click-modal="false">
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <ElFormItem label="标签名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入标签名称" />
      </ElFormItem>

      <ElFormItem label="分类" prop="category">
        <ElInput v-model="formData.category" placeholder="请输入分类" />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createTag, updateTag } from '@/api/admin/tag'
  import type { TagForm } from '@/types/api/tag'

  // Props
  interface Props {
    title: string
    isEdit: boolean
    tagData?: TagForm
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '',
    isEdit: false
  })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = ref<TagForm>({
    id: undefined,
    name: '',
    category: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入标签名称', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请输入分类', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ]
  }

  // 计算属性
  const dialogTitle = computed(() => props.title || (props.isEdit ? '编辑标签' : '新增标签'))

  // 监听数据变化
  watch(
    () => props.tagData,
    (newData) => {
      if (newData) {
        formData.value = {
          id: newData.id,
          name: newData.name || '',
          category: newData.category || ''
        }
      }
    },
    { immediate: true }
  )

  // 监听对话框显示状态，重置表单
  watch(
    () => visible.value,
    (newVal) => {
      if (!newVal) {
        // 对话框关闭时重置表单
        nextTick(() => {
          formRef.value?.resetFields()
          if (!props.tagData) {
            // 如果没有传入数据，重置为默认值
            formData.value = {
              id: undefined,
              name: '',
              category: ''
            }
          }
        })
      }
    }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      loading.value = true

      if (props.isEdit && formData.value.id) {
        // 提交时只发送 name 和 category 字段
        const { name, category } = formData.value
        await updateTag(formData.value.id, { name, category })
        ElMessage.success('更新成功')
      } else {
        // 提交时只发送 name 和 category 字段
        const { name, category } = formData.value
        await createTag({ name, category })
        ElMessage.success('创建成功')
      }

      visible.value = false
      emit('success')
    } catch {
      // 表单验证失败或API调用失败，错误已由拦截器处理
    } finally {
      loading.value = false
    }
  }

  // 取消
  const handleCancel = () => {
    visible.value = false
  }
</script>

<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
</style>
