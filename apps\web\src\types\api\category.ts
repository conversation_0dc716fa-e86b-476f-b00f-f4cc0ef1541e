// 分类管理相关类型定义

// 分类实体
export interface Category {
  id: number
  name: string
  code: string
  parent_id: number | null
  level: number
  sort: number
  status: number
  remark?: string
  created_at: string
  updated_at?: string
  children?: Category[]
  has_children?: boolean
  children_count?: number
}

// 分类表单
export interface CategoryForm {
  id?: number
  name: string
  code: string
  parent_id: number | null
  sort: number
  status: number
  remark?: string
}
