<?php

namespace App\Services;

use App\Models\Attachment;
use App\Models\AttachmentRelation;
use App\Services\Attachment\StorageManager;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

use function App\Support\string_to_timestamp;

class AttachmentService
{
    public function __construct(
        private StorageManager $storageManager,
        private ConfigService $configService
    ) {}

    /**
     * 上传文件（本地上传）
     */
    public function upload(UploadedFile $file, array $options = []): Attachment
    {
        return DB::transaction(function () use ($file, $options) {
            // 计算MD5
            $md5Hash = md5_file($file->getRealPath());

            // 检查秒传
            if ($existingAttachment = $this->checkQuickUpload($md5Hash)) {
                return $existingAttachment;
            }

            // 获取系统配置
            $configs = $this->configService->getAllConfigs();

            // 使用指定的驱动或从配置中获取
            $driverName = $options['driver'] ?? $configs['upload']['storage_type'] ?? config('attachment.default');
            $driver = $this->storageManager->driver($driverName, $driverName === 'alioss' ? $this->getOssConfig() : null);

            // 生成存储路径
            $path = $this->generateStoragePath($file);

            // 存储文件
            $storedPath = $driver->store($file, $path);

            // 创建附件记录
            return $this->createAttachment([
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $storedPath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'storage_type' => $driver->getType(),
                'md5_hash' => $md5Hash,
            ]);
        });
    }

    /**
     * 检查MD5秒传
     */
    public function checkQuickUpload(string $md5Hash): ?Attachment
    {
        // 检查是否启用秒传功能
        if (! config('attachment.quick_upload.enabled', true)) {
            return null;
        }

        // 查找已存在的相同MD5的文件
        return Attachment::where('md5_hash', $md5Hash)->first();
    }

    /**
     * 获取STS临时凭证
     */
    public function getSTSCredentials(array $params): array
    {
        // 1. MD5秒传检查
        if (! empty($params['md5_hash'])) {
            $existing = $this->checkQuickUpload($params['md5_hash']);
            if ($existing) {
                return [
                    'quick_upload' => true,
                    'attachment' => $existing,  // 返回模型实例，让控制器用 AttachmentResource 格式化
                ];
            }
        }

        // 2. 获取配置
        $configs = $this->configService->getAllConfigs();

        // 检查存储类型
        if ($configs['upload']['storage_type'] !== 'aliyun') {
            throw new \RuntimeException('当前存储类型不是阿里云OSS');
        }

        $ossConfig = $this->getOssConfig();

        // 验证配置
        if (empty($ossConfig['access_key_id']) || empty($ossConfig['access_key_secret'])) {
            throw new \RuntimeException('阿里云OSS配置不完整，请检查系统配置');
        }

        // 验证RAM角色ARN
        if (empty($ossConfig['role_arn'])) {
            throw new \RuntimeException('RAM角色ARN未配置，请在系统配置中设置');
        }

        // 3. 生成STS凭证
        $stsDriver = new \App\Services\Attachment\Drivers\AliStsDriver($ossConfig);

        // 添加用户ID到参数中
        $params['user_id'] = Auth::id() ?? 0;

        $credentials = $stsDriver->getSTSCredentials([
            'duration_seconds' => 3500,  // 固定为3500秒
            'user_id' => $params['user_id'],
        ]);

        // 4. 缓存上传信息
        $uploadId = Str::uuid()->toString();
        Cache::put("upload:{$uploadId}", $params, 3600);

        // 5. 获取OSS配置信息
        $ossInfo = $stsDriver->getOssConfig();

        return [
            'quick_upload' => false,
            'upload_id' => $uploadId,
            'credentials' => $credentials,
            'region' => $ossInfo['region'],
            'bucket' => $ossInfo['bucket'],
            'endpoint' => "https://{$ossInfo['endpoint']}",
            'prefix' => $ossInfo['prefix'],
        ];
    }

    /**
     * 确认上传完成
     */
    public function confirmUpload(array $params): Attachment
    {
        // 1. 验证upload_id
        $uploadId = $params['upload_id'] ?? null;
        if (! $uploadId) {
            throw new \InvalidArgumentException('缺少upload_id参数');
        }

        // 2. 获取缓存的上传信息
        $cacheKey = "upload:{$uploadId}";
        $cachedData = Cache::pull($cacheKey);

        if (! $cachedData) {
            throw new \InvalidArgumentException('upload_id无效或已过期');
        }

        // 3. 创建附件记录
        return $this->createAttachment([
            'file_name' => $cachedData['filename'] ?? $params['filename'] ?? '',
            'file_path' => $params['object_key'],
            'file_size' => $cachedData['filesize'] ?? $params['filesize'] ?? 0,
            'mime_type' => $cachedData['mime_type'] ?? $params['mime_type'] ?? '',
            'md5_hash' => $cachedData['md5_hash'] ?? null,
            'storage_type' => 'alioss',
        ]);
    }

    /**
     * 删除文件
     */
    public function delete(Attachment $attachment): bool
    {
        return DB::transaction(function () use ($attachment) {
            // 使用对应的驱动删除文件
            $driver = $this->storageManager->driver($attachment->storage_type,
                $attachment->storage_type === 'alioss' ? $this->getOssConfig() : null);
            $driver->delete($attachment->file_path);

            // 软删除数据库记录
            return $attachment->delete();
        });
    }

    /**
     * 获取文件内容
     */
    public function getFileContent(Attachment $attachment): ?string
    {
        // 获取系统配置
        $configs = $this->configService->getAllConfigs();

        // 对于OSS文件，直接返回null，让前端通过URL访问
        if ($attachment->storage_type === 'alioss' || $configs['upload']['storage_type'] === 'alioss') {
            return null;
        }

        // 本地文件返回内容
        $driver = $this->storageManager->driver($attachment->storage_type);
        if ($driver->exists($attachment->file_path)) {
            return file_get_contents($driver->url($attachment->file_path));
        }

        return null;
    }

    /**
     * 创建附件记录
     */
    private function createAttachment(array $data): Attachment
    {
        return Attachment::create($data);
    }

    /**
     * 生成存储路径
     */
    protected function generateStoragePath(UploadedFile $file): string
    {
        $date = now()->format('Y/m/d');
        $fileName = Str::random(32).'.'.$file->getClientOriginalExtension();

        return "attachments/{$date}/{$fileName}";
    }

    /**
     * 获取 OSS 配置
     */
    public function getOssConfig(): array
    {
        $configs = $this->configService->getAllConfigs();

        return [
            'access_key_id' => $configs['upload']['aliyun_access_key'] ?? '',
            'access_key_secret' => $configs['upload']['aliyun_secret_key'] ?? '',
            'bucket' => $configs['upload']['aliyun_bucket'] ?? '',
            'endpoint' => $configs['upload']['aliyun_region'] ?? 'oss-cn-hangzhou.aliyuncs.com',
            'region' => $configs['upload']['aliyun_region'] ?? 'oss-cn-hangzhou.aliyuncs.com',
            'role_arn' => $configs['upload']['aliyun_sts_role_arn'] ?? '',
            'is_cname' => false,
            'use_ssl' => true,
            'prefix' => 'attachments',
            'policy_expire' => 3600,
            'cdn_domain' => $configs['upload']['aliyun_cdn_domain'] ?? null,
        ];
    }

    /**
     * 分页查询
     */
    public function paginate(array $params = [], int $perPage = 20)
    {
        $query = Attachment::query();

        // 加载第一个关联（如果有的话）和创建人信息
        $query->with([
            'attachables' => function ($q) {
                $q->orderBy('created_at', 'desc')->limit(1);
            },
            'creator:id,name',
        ]);

        // 文件名搜索
        if (! empty($params['file_name'])) {
            $query->where('file_name', 'like', '%'.$params['file_name'].'%');
        }

        // 存储类型筛选
        if (! empty($params['storage_type'])) {
            $query->where('storage_type', $params['storage_type']);
        }

        // 时间范围
        if (! empty($params['start_time'])) {
            $query->where('created_at', '>=', string_to_timestamp($params['start_time'].' 00:00:00'));
        }

        if (! empty($params['end_time'])) {
            $query->where('created_at', '<=', string_to_timestamp($params['end_time'].' 23:59:59'));
        }

        // 通过关联表筛选
        if (! empty($params['attachable_type'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('attachable_type', $params['attachable_type']);
            });
        }

        if (! empty($params['attachable_id'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('attachable_id', $params['attachable_id']);
            });
        }

        if (! empty($params['category'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('category', $params['category']);
            });
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * 关联附件到业务实体
     */
    public function associate(Model $model, int $attachmentId, array $data = []): AttachmentRelation
    {
        Attachment::findOrFail($attachmentId);

        // 检查是否已关联
        $existingRelation = AttachmentRelation::where([
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->first();

        if ($existingRelation) {
            // 更新现有关联
            $existingRelation->update($data);

            return $existingRelation;
        }

        // 创建新关联
        return AttachmentRelation::create(array_merge($data, [
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ]));
    }

    /**
     * 批量关联附件
     */
    public function associateMany(Model $model, array $attachmentIds, array $data = []): Collection
    {
        $relations = collect();

        DB::transaction(function () use ($model, $attachmentIds, $data, &$relations) {
            foreach ($attachmentIds as $index => $attachmentId) {
                $relationData = array_merge($data, [
                    'sort' => $data['sort'] ?? $index,
                ]);

                $relations->push($this->associate($model, $attachmentId, $relationData));
            }
        });

        return $relations;
    }

    /**
     * 解除附件关联
     */
    public function dissociate(Model $model, int $attachmentId): bool
    {
        return AttachmentRelation::where([
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->delete() > 0;
    }

    /**
     * 解除模型的所有附件关联
     */
    public function dissociateAll(Model $model): int
    {
        return AttachmentRelation::where([
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->delete();
    }

    /**
     * 获取业务实体的附件列表
     */
    public function getByModel(Model $model, ?string $category = null): Collection
    {
        $query = $model->attachments();

        if ($category) {
            $query->wherePivot('category', $category);
        }

        return $query->get();
    }

    /**
     * 同步附件关联（替换现有关联）
     */
    public function sync(Model $model, array $attachmentData): void
    {
        DB::transaction(function () use ($model, $attachmentData) {
            // 删除现有关联
            $this->dissociateAll($model);

            // 创建新关联
            foreach ($attachmentData as $data) {
                if (isset($data['attachment_id'])) {
                    $this->associate($model, $data['attachment_id'], $data);
                }
            }
        });
    }

    /**
     * 更新附件关联信息
     */
    public function updateRelation(int $relationId, array $data): AttachmentRelation
    {
        $relation = AttachmentRelation::findOrFail($relationId);

        $relation->update($data);

        return $relation;
    }
}
