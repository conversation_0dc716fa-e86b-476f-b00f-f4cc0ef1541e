<template>
  <ElDialog
    v-model="visible"
    title="选择位置"
    width="900px"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <div class="map-selector-container">
      <!-- 搜索框 -->
      <div class="search-box">
        <ElInput
          v-model="searchKeyword"
          placeholder="请输入地址搜索"
          clearable
          @keyup.enter="searchPlace"
        >
          <template #append>
            <ElButton @click="searchPlace">
              <ElIcon><Search /></ElIcon>
              搜索
            </ElButton>
          </template>
        </ElInput>
      </div>

      <!-- 地图和搜索结果容器 -->
      <div class="map-wrapper">
        <!-- 地图容器 -->
        <div id="map-container" class="map-container"></div>

        <!-- 搜索结果面板 -->
        <div id="search-panel" class="search-panel"></div>
      </div>

      <!-- 选中的位置信息 -->
      <div v-if="selectedLocation" class="location-info">
        <ElAlert
          :title="`已选择位置：${selectedLocation.address || '未知地址'}`"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="location-details">
              <span>经度：{{ selectedLocation.longitude.toFixed(6) }}</span>
              <ElDivider direction="vertical" />
              <span>纬度：{{ selectedLocation.latitude.toFixed(6) }}</span>
            </div>
          </template>
        </ElAlert>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleConfirm" :disabled="!selectedLocation">
          确定
        </ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'MapSelector' })

  // Vue 核心
  import { ref, watch, onUnmounted, nextTick } from 'vue'

  // UI 框架
  import { ElDialog, ElButton, ElInput, ElIcon, ElAlert, ElDivider, ElMessage } from 'element-plus'
  import { Search } from '@element-plus/icons-vue'

  // 高德地图
  import AMapLoader from '@amap/amap-jsapi-loader'

  // 类型定义
  export interface LocationInfo {
    address: string
    longitude: number
    latitude: number
  }

  // Props
  const props = withDefaults(
    defineProps<{
      // 初始位置
      initLocation?: {
        address?: string
        longitude?: number
        latitude?: number
      }
      // 高德地图 API Key（建议通过环境变量配置）
      apiKey?: string
      // 安全密钥（可选）
      securityJsCode?: string
    }>(),
    {
      apiKey: '907524d4c0c87737e07c516dc1242d31', // 请在使用时提供真实的 API Key
      securityJsCode: '1f6ad1939e48d58c38826f2532171f2b'
    }
  )

  // Emits
  const emit = defineEmits<{
    confirm: [location: LocationInfo]
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 地图相关
  let map: any = null
  let marker: any = null
  let placeSearch: any = null
  let AMap: any = null

  // 搜索关键词
  const searchKeyword = ref('')

  // 选中的位置
  const selectedLocation = ref<LocationInfo | null>(null)

  // 初始化地图
  const initMap = async () => {
    try {
      // 检查容器是否存在
      const container = document.getElementById('map-container')
      if (!container) {
        setTimeout(() => initMap(), 200)
        return
      }

      // 配置安全密钥（如果提供）
      if (props.securityJsCode) {
        ;(window as any)._AMapSecurityConfig = {
          securityJsCode: props.securityJsCode
        }
      }

      // 加载高德地图
      AMap = await AMapLoader.load({
        key: props.apiKey || '', // 申请好的Web端开发者Key
        version: '2.0', // 指定要加载的 JSAPI 的版本
        plugins: [
          'AMap.PlaceSearch', // 地点搜索
          'AMap.Marker', // 标记
          'AMap.Geolocation' // 定位
        ]
      })

      // 创建地图实例
      map = new AMap.Map('map-container', {
        viewMode: '2D', // 默认2D地图模式
        zoom: 13, // 初始化地图级别
        center: [116.397428, 39.90923], // 默认北京天安门
        resizeEnable: true // 自动调整地图大小
      })

      // 立即触发一次 resize 确保地图正确显示
      setTimeout(() => {
        if (map && map.resize) {
          map.resize()
        }
      }, 50)

      // 监听地图加载完成事件
      map.on('complete', () => {
        // 地图完全加载后，再次触发 resize
        if (map && map.resize) {
          map.resize()
        }
      })

      // 初始化地点搜索
      placeSearch = new AMap.PlaceSearch({
        pageSize: 10,
        pageIndex: 1,
        city: '全国',
        map: map, // 结果显示在地图上
        panel: 'search-panel', // 结果列表显示在面板中
        autoFitView: true // 自动调整视野
      })

      // 监听 PlaceSearch 的标记点击事件
      placeSearch.on('markerClick', (e: any) => {
        const poi = e.data
        selectedLocation.value = {
          address: poi.name + (poi.address ? ' - ' + poi.address : ''),
          longitude: poi.location.lng,
          latitude: poi.location.lat
        }
      })

      // 监听列表项选择事件
      placeSearch.on('listElementClick', (e: any) => {
        const poi = e.data
        selectedLocation.value = {
          address: poi.name + (poi.address ? ' - ' + poi.address : ''),
          longitude: poi.location.lng,
          latitude: poi.location.lat
        }
      })

      // 如果有初始位置，添加标记并显示
      if (props.initLocation?.longitude && props.initLocation?.latitude) {
        addMarker(
          props.initLocation.longitude,
          props.initLocation.latitude,
          props.initLocation.address || '已选择的位置'
        )
        // 设置地图中心到初始位置
        map.setCenter([props.initLocation.longitude, props.initLocation.latitude])
        map.setZoom(15)
        // 触发地图重新计算尺寸
        setTimeout(() => {
          if (map && map.resize) {
            map.resize()
          }
        }, 100)
      } else {
        // 没有初始位置，添加手动定位按钮
        const geolocation = new AMap.Geolocation({
          enableHighAccuracy: true, // 是否使用高精度定位
          timeout: 10000, // 超时时间
          maximumAge: 0, // 定位结果缓存时间
          convert: true, // 自动偏移坐标
          showButton: true, // 显示定位按钮
          buttonPosition: 'LB', // 定位按钮位置
          buttonOffset: new AMap.Pixel(10, 20), // 定位按钮偏移
          showMarker: false, // 不显示定位点（我们自己控制）
          showCircle: false, // 不显示定位精度圈
          zoomToAccuracy: false // 不自动调整视野（我们手动控制）
        })

        map.addControl(geolocation)
      }

      // 监听地图点击事件
      map.on('click', (e: any) => {
        const lng = e.lnglat.getLng()
        const lat = e.lnglat.getLat()

        // 清除搜索结果和面板
        if (placeSearch) {
          placeSearch.clear()
        }

        // 清空搜索面板的DOM内容
        const searchPanel = document.getElementById('search-panel')
        if (searchPanel) {
          searchPanel.innerHTML = ''
        }

        // 添加标记
        addMarker(lng, lat, '自定义位置')
      })
    } catch (error) {
      console.error('地图初始化失败:', error)
      ElMessage.error('地图加载失败，请检查配置')
    }
  }

  // 添加标记
  const addMarker = (lng: number, lat: number, address?: string) => {
    // 清除之前的标记
    if (marker) {
      map.remove(marker)
    }

    // 创建新标记
    marker = new AMap.Marker({
      position: new AMap.LngLat(lng, lat),
      title: address || '选中的位置'
    })

    // 添加到地图
    map.add(marker)

    // 设置地图中心点
    map.setCenter([lng, lat])

    // 更新选中的位置
    selectedLocation.value = {
      address: address || '',
      longitude: lng,
      latitude: lat
    }
  }

  // 搜索地点
  const searchPlace = () => {
    if (!searchKeyword.value.trim()) {
      ElMessage.warning('请输入搜索关键词')
      return
    }

    // 清除之前的搜索结果和标记
    if (placeSearch) {
      placeSearch.clear()
    }

    // 清除自定义标记
    if (marker) {
      map.remove(marker)
      marker = null
    }

    // 执行搜索
    placeSearch.search(searchKeyword.value, (status: string, result: any) => {
      if (status === 'complete' && result.info === 'OK') {
        // PlaceSearch 会自动在地图上显示标记和结果列表
        // 用户需要手动点击选择，不自动选中第一个结果
      } else if (status === 'no_data') {
        ElMessage.warning('未找到相关地点')
      } else {
        ElMessage.error('搜索失败，请重试')
      }
    })
  }

  // 确认选择
  const handleConfirm = () => {
    if (selectedLocation.value) {
      emit('confirm', selectedLocation.value)
      handleClose()
    }
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    selectedLocation.value = null
    searchKeyword.value = ''

    // 清除搜索结果
    if (placeSearch) {
      placeSearch.clear()
      placeSearch = null
    }

    // 清除标记
    if (marker) {
      map && map.remove(marker)
      marker = null
    }

    // 销毁地图
    if (map) {
      map.destroy()
      map = null
    }

    // 重置 AMap 引用
    AMap = null
  }

  // 监听弹窗显示状态
  watch(visible, async (newVal) => {
    if (newVal) {
      // 等待 DOM 更新
      await nextTick()
      // 再增加一点延迟确保容器完全渲染
      setTimeout(() => {
        initMap()
      }, 200)
    }
  })

  // 组件卸载时销毁地图
  onUnmounted(() => {
    // 清除搜索结果
    if (placeSearch) {
      placeSearch.clear()
      placeSearch = null
    }

    // 清除标记
    if (marker) {
      map && map.remove(marker)
      marker = null
    }

    // 销毁地图
    if (map) {
      map.destroy()
      map = null
    }

    // 重置 AMap 引用
    AMap = null
  })
</script>

<style lang="scss" scoped>
  .map-selector-container {
    .search-box {
      margin-bottom: 16px;
    }

    .map-wrapper {
      display: flex;
      gap: 16px;
      height: 500px;

      .map-container {
        flex: 1;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
      }

      .search-panel {
        width: 300px;
        overflow-y: auto;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 4px;

        // 高德地图搜索结果样式优化
        :deep(.amap-panel) {
          border: none;
          box-shadow: none;
        }

        :deep(.amap-poi-item) {
          padding: 12px;
          cursor: pointer;
          border-bottom: 1px solid var(--el-border-color-lighter);

          &:hover {
            background-color: var(--el-fill-color-light);
          }
        }

        :deep(.amap-panel-content) {
          padding: 0;
        }
      }
    }

    .location-info {
      margin-top: 16px;

      .location-details {
        margin-top: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
</style>
