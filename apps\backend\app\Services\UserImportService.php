<?php

namespace App\Services;

use App\Models\Role;
use App\Models\User;
use App\Models\ImportTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

/**
 * 用户导入服务
 *
 * @deprecated 此类为旧版导入服务，建议创建继承 BaseImportService 的新实现
 * @todo 重构为继承 BaseImportService 的标准化导入服务
 */
class UserImportService
{
    protected array $errors = [];

    protected int $totalRows = 0;

    protected int $successRows = 0;

    protected int $failedRows = 0;

    public function processImport(ImportTask $importTask): array
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;

        // 获取文件路径
        $filePath = $this->getActualFilePath($importTask->file_path);

        // 读取Excel
        $rowsGroup = Excel::toArray(new class {}, $filePath);
        if (empty($rowsGroup) || empty($rowsGroup[0])) {
            throw new \Exception('Excel文件为空或格式不正确');
        }
        $rows = $rowsGroup[0];

        // 定位标题行
        $headerIndex = $this->findHeaderRow($rows);
        if ($headerIndex === -1) {
            throw new \Exception('未找到有效的标题行');
        }
        $headers = $rows[$headerIndex];
        $map = $this->buildHeaderMap($headers);

        // 数据行
        $dataRows = array_slice($rows, $headerIndex + 1);
        $this->totalRows = count($dataRows);
        if ($this->totalRows <= 0) {
            throw new \Exception('Excel文件中没有数据行');
        }

        foreach ($dataRows as $i => $row) {
            $rowNum = $headerIndex + 2 + $i;
            try {
                $payload = $this->rowToPayload($row, $map);
                if ($this->isRowEmpty($payload)) {
                    continue;
                }
                $this->createUserWithRoles($payload);
                $this->successRows++;
            } catch (\Throwable $e) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $rowNum,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];
                Log::warning('用户导入失败', ['row' => $rowNum, 'message' => $e->getMessage()]);
            }
        }

        // 更新任务进度
        $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

        return [
            'total_rows' => $this->totalRows,
            'success_rows' => $this->successRows,
            'failed_rows' => $this->failedRows,
            'errors' => $this->errors,
            'summary' => $this->generateSummary(),
        ];
    }

    /**
     * 获取实际文件路径
     */
    protected function getActualFilePath(string $filePath): string
    {
        // 如果是绝对路径且文件存在，直接返回
        if (file_exists($filePath)) {
            return $filePath;
        }

        // 处理附件系统的相对路径
        $possiblePaths = [
            // Laravel public disk 路径 (storage/app/public/)
            storage_path('app/public/'.$filePath),
            // Laravel local disk 路径 (storage/app/)
            storage_path('app/'.$filePath),
            // 公共存储路径 (public/storage/)
            public_path('storage/'.$filePath),
            // 直接在public目录
            public_path($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都不存在，记录日志并返回原路径
        Log::warning('导入文件路径查找失败', [
            'original_path' => $filePath,
            'tried_paths' => $possiblePaths,
        ]);

        return $filePath;
    }

    /**
     * 生成导入摘要
     */
    protected function generateSummary(): string
    {
        return sprintf(
            '导入完成：总计 %d 行，成功 %d 行，失败 %d 行',
            $this->totalRows,
            $this->successRows,
            $this->failedRows
        );
    }

    protected function findHeaderRow(array $rows): int
    {
        foreach ($rows as $i => $row) {
            if (in_array('用户账号', $row) && in_array('密码', $row)) {
                return $i;
            }
        }

        return -1;
    }

    protected function buildHeaderMap(array $headers): array
    {
        $map = [];
        foreach ($headers as $index => $header) {
            $map[trim((string) $header)] = $index;
        }

        return $map;
    }

    protected function rowToPayload(array $row, array $map): array
    {
        $account = $this->getCell($row, $map, '用户账号');
        $password = $this->getCell($row, $map, '密码');
        $nickname = $this->getCell($row, $map, '用户昵称');
        $email = $this->getCell($row, $map, '邮箱');
        $roles = $this->getCell($row, $map, '用户角色');

        return [
            'account' => $account,
            'password' => $password,
            'nickname' => $nickname,
            'email' => $email,
            'roles' => $roles,
        ];
    }

    protected function getCell(array $row, array $map, string $header): ?string
    {
        if (! isset($map[$header])) {
            return null;
        }
        $value = $row[$map[$header]] ?? null;

        return is_string($value) ? trim($value) : (is_numeric($value) ? (string) $value : ($value === null ? null : trim((string) $value)));
    }

    protected function isRowEmpty(array $payload): bool
    {
        return empty($payload['account']) && empty($payload['password']) && empty($payload['nickname']) && empty($payload['email']) && empty($payload['roles']);
    }

    protected function createUserWithRoles(array $payload): void
    {
        $account = $payload['account'] ?? '';
        $password = $payload['password'] ?? '';
        $nickname = $payload['nickname'] ?? null;
        $email = $payload['email'] ?? null;
        $rolesString = $payload['roles'] ?? '';

        if ($account === '' || $password === '') {
            throw new \Exception('用户账号与密码为必填');
        }

        DB::transaction(function () use ($account, $password, $nickname, $email, $rolesString) {
            $user = User::firstOrCreate(
                ['account' => $account],
                [
                    'password' => Hash::make($password),
                    'nickname' => $nickname,
                    'email' => $email,
                    'status' => 'enable',
                    'is_super_admin' => false,
                ]
            );

            // 如果用户已存在且提供了密码，则更新密码及基础信息
            if (! $user->wasRecentlyCreated) {
                $update = [];
                if ($password !== '') {
                    $update['password'] = Hash::make($password);
                }
                if (! is_null($nickname)) {
                    $update['nickname'] = $nickname;
                }
                if (! is_null($email)) {
                    $update['email'] = $email;
                }
                if (! empty($update)) {
                    $user->update($update);
                }
            }

            // 处理角色：按中文逗号/英文逗号/分号分割，支持角色名称或ID
            $roleNames = preg_split('/[，,;；\s]+/', (string) $rolesString, -1, PREG_SPLIT_NO_EMPTY) ?: [];
            if (! empty($roleNames)) {
                $roleIds = Role::whereIn('name', $roleNames)->orWhereIn('id', $roleNames)->pluck('id')->unique()->values()->all();
                if (! empty($roleIds)) {
                    $user->roles()->syncWithoutDetaching($roleIds);
                }
            }
        });
    }
}
