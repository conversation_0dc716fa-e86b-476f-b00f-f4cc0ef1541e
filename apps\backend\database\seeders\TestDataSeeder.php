<?php

namespace Database\Seeders;

use App\Models\Entity;
use App\Models\EntityContact;
use Illuminate\Database\Seeder;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "开始添加测试数据...\n";

        // 获取最后一个相关方
        $lastEntity = Entity::orderBy('id', 'desc')->first();

        if ($lastEntity) {
            echo "为相关方 '{$lastEntity->name}' 添加额外联系人...\n";

            // 添加3个测试联系人
            $testContacts = [
                [
                    'entity_id' => $lastEntity->id,
                    'name' => '赵小明',
                    'phone' => '18612345678',
                    'position' => '技术主管',
                    'department' => '技术部',
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'entity_id' => $lastEntity->id,
                    'name' => '钱小红',
                    'phone' => '18687654321',
                    'position' => '项目经理',
                    'department' => '项目管理部',
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'entity_id' => $lastEntity->id,
                    'name' => '孙小刚',
                    'phone' => '18698765432',
                    'position' => '商务专员',
                    'department' => '商务部',
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
            ];

            foreach ($testContacts as $contactData) {
                EntityContact::create($contactData);
            }

            echo "已为相关方 '{$lastEntity->name}' 添加了3个测试联系人\n";
        } else {
            echo "未找到相关方数据，请先运行 MedicalDataSeeder\n";
        }

        echo "测试数据添加完成！\n";
    }
}