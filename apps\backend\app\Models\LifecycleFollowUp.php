<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $lifecycle_id 生命周期ID
 * @property int $date 日期
 * @property int $person_id 跟进人ID
 * @property int $tag_ids 标签ID
 * @property string $content 内容
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\User|null $creator
 * @property-read \App\Models\Lifecycle $lifecycle
 * @property-read \App\Models\User $person
 * @property-read \App\Models\User|null $updater
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Tag> $tags
 * @property-read \App\Models\Tag $tag
 * @property int $tag_id 标签ID
 * @property-read int|null $tags_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereLifecycleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp wherePersonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereTagIds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereUpdatedBy($value)
 *
 * @mixin \Eloquent
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereTagId($value)
 */
class LifecycleFollowUp extends BaseModel
{
    use HasAttachments, HasFactory;

    protected $fillable = [
        'lifecycle_id',
        'date',
        'person_id',
        'tag_ids',
        'content',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'tag_ids' => 'array',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取所属的生命周期
     */
    public function lifecycle(): BelongsTo
    {
        return $this->belongsTo(Lifecycle::class);
    }

    /**
     * 获取跟进人
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(User::class, 'person_id');
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 获取当前记录关联的标签集合
     * 直接通过 tag_ids 数组查询对应的 Tag 模型
     */
    public function tags()
    {
        // 如果 tag_ids 为空，返回空集合
        if (empty($this->tag_ids)) {
            return Tag::query()->whereRaw('1 = 0'); // 空集合
        }

        // 通过 tag_ids 数组中的ID查询标签
        return Tag::whereIn('id', $this->tag_ids);
    }

    /**
     * 快速获取标签集合（已执行查询）
     */
    public function getTagsAttribute()
    {
        return $this->tags()->get();
    }
}
