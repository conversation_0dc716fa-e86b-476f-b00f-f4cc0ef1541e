<template>
  <ElDialog
    v-model="visible"
    :title="isEdit ? '编辑品牌' : '新增品牌'"
    width="500px"
    align-center
    append-to-body
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <ElFormItem label="品牌名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入品牌名称" />
      </ElFormItem>
      <ElFormItem label="品牌Logo" prop="logo_id">
        <AvatarUpload
          v-model="formData.logo_id"
          :attachments="currentLogo"
          :max-size="10"
          accept="image/*"
          :tip="'只能上传图片文件，且不超过10MB'"
        />
      </ElFormItem>
      <ElFormItem label="品牌描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入品牌描述（可选）"
        />
      </ElFormItem>
      <ElFormItem label="排序" prop="sort_order">
        <ElInputNumber
          v-model="formData.sort_order"
          :min="0"
          :max="999"
          placeholder="数字越大越靠前"
          style="width: 100%"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleConfirm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createBrand, updateBrand } from '@/api/admin/entity'
  import { AvatarUpload } from '@/components/custom/upload'
  import type { Brand, BrandForm } from '@/types/api'

  // Props
  interface Props {
    entityId: string | number
  }
  const props = defineProps<Props>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>('visible', { default: false })
  const brand = defineModel<Brand | null>('brand', { default: null })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 计算属性判断是否编辑模式
  const isEdit = computed(() => !!brand.value?.id)

  // 表单实例
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const currentLogo = ref<any[]>([])

  // 表单数据
  const formData = ref<BrandForm>({
    entity_id: props.entityId,
    name: '',
    logo_id: null,
    description: null,
    sort_order: 0
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
    sort_order: [{ required: true, message: '请输入排序', trigger: 'blur' }]
  }

  // 监听 brand 变化，更新表单数据
  watch(
    () => brand.value,
    (newVal) => {
      if (newVal) {
        // 编辑模式
        formData.value = {
          id: newVal.id,
          entity_id: props.entityId,
          name: newVal.name,
          logo_id: newVal.logo_id || null,
          description: newVal.description || null,
          sort_order: newVal.sort_order
        }

        // 设置当前Logo附件
        currentLogo.value =
          newVal.logo && newVal.logo_id
            ? [
                {
                  id: newVal.logo_id,
                  file_name: '品牌Logo',
                  file_path: newVal.logo,
                  file_url: newVal.logo
                }
              ]
            : []
      } else {
        // 新增模式，重置数据
        formData.value = {
          entity_id: props.entityId,
          name: '',
          logo_id: null,
          description: null,
          sort_order: 0
        }
        currentLogo.value = []
      }
    },
    { immediate: true }
  )

  // 处理取消
  const handleCancel = () => {
    visible.value = false
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      if (isEdit.value) {
        await updateBrand(String(props.entityId), String(formData.value.id!), formData.value)
        ElMessage.success('编辑成功')
      } else {
        await createBrand(String(props.entityId), formData.value)
        ElMessage.success('新增成功')
      }

      visible.value = false
      emit('success')
    } catch (error: any) {
      if (error !== false) {
        // 不是验证错误
        console.error('保存品牌失败:', error)
        ElMessage.error(error.message || '保存失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭后的处理
  const handleClosed = () => {
    // 重置表单
    formRef.value?.resetFields()
    // 清空编辑数据
    brand.value = null
    // 重置Logo
    currentLogo.value = []
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
