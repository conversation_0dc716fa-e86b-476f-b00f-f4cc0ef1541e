<template>
  <div class="brand-manage-table">
    <!-- 表格头部 -->
    <div class="table-header">
      <div class="header-left">
        <span class="title">品牌管理</span>
        <span class="count">（{{ brands.length }} 个品牌）</span>
      </div>
      <div class="header-right">
        <ElButton type="primary" size="small" @click="handleAdd">
          <ElIcon><Plus /></ElIcon>
          添加品牌
        </ElButton>
      </div>
    </div>

    <!-- 品牌列表 -->
    <div class="brand-list" v-if="brands.length > 0">
      <div v-for="(brand, index) in sortedBrands" :key="brand.id || index" class="brand-item">
        <!-- 品牌Logo -->
        <div class="brand-logo">
          <ElAvatar
            :size="40"
            :src="brand.logo"
            :alt="brand.name"
            style="color: #909399; background-color: #f5f7fa"
          >
            {{ brand.name.charAt(0) }}
          </ElAvatar>
        </div>

        <!-- 品牌信息 -->
        <div class="brand-info">
          <div class="brand-name">{{ brand.name }}</div>
          <div class="brand-description" v-if="brand.description">
            {{ brand.description }}
          </div>
          <div class="brand-meta">
            <span class="sort-order">排序：{{ brand.sort_order }}</span>
            <span class="created-time" v-if="brand.created_at">
              创建时间：{{ formatDate(brand.created_at, 'YYYY-MM-DD') }}
            </span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="brand-actions">
          <ElButton size="small" @click="handleEdit(brand)">编辑</ElButton>
          <ElButton size="small" type="danger" @click="handleDelete(brand)">删除</ElButton>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <ElEmpty description="暂无品牌数据">
        <ElButton type="primary" @click="handleAdd">添加第一个品牌</ElButton>
      </ElEmpty>
    </div>

    <!-- 品牌表单弹窗 -->
    <BrandFormDialog
      v-model:visible="dialogVisible"
      v-model:brand="selectedBrand"
      :entity-id="entityId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { ElMessage, ElMessageBox, ElAvatar } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import { formatDate } from '@/utils/dataprocess/format'
  import BrandFormDialog from './BrandFormDialog.vue'
  import type { Brand } from '@/types/api'

  // Props
  interface Props {
    brands: Brand[]
    entityId: string
  }
  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    add: [brand: Brand]
    edit: [brand: Brand]
    delete: [brand: Brand]
  }>()

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedBrand = ref<Brand | null>(null)

  // 按 sort_order 倒序排列的品牌列表
  const sortedBrands = computed(() => {
    return [...props.brands].sort((a, b) => b.sort_order - a.sort_order)
  })

  // 添加品牌
  const handleAdd = () => {
    selectedBrand.value = null
    dialogVisible.value = true
  }

  // 编辑品牌
  const handleEdit = (brand: Brand) => {
    selectedBrand.value = brand
    dialogVisible.value = true
  }

  // 删除品牌
  const handleDelete = async (brand: Brand) => {
    try {
      await ElMessageBox.confirm(`确定要删除品牌"${brand.name}"吗？`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
      emit('delete', brand)
    } catch {
      // 用户取消删除
    }
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    const isAdd = !selectedBrand.value?.id
    if (isAdd) {
      ElMessage.success('品牌添加成功')
    } else {
      ElMessage.success('品牌编辑成功')
    }
    // 通知父组件刷新数据
    emit('edit', selectedBrand.value!)
  }
</script>

<style lang="scss" scoped>
  .brand-manage-table {
    .table-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .header-left {
        display: flex;
        gap: 8px;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: 500;
        }

        .count {
          font-size: 14px;
          color: #909399;
        }
      }
    }

    .brand-list {
      .brand-item {
        display: flex;
        align-items: center;
        padding: 16px;
        margin-bottom: 12px;
        background-color: #fff;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        transition: all 0.2s;

        &:hover {
          background-color: #ecf5ff;
          border-color: #c6e2ff;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .brand-logo {
          flex-shrink: 0;
          margin-right: 16px;
        }

        .brand-info {
          flex: 1;

          .brand-name {
            margin-bottom: 4px;
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }

          .brand-description {
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.4;
            color: #606266;
          }

          .brand-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #909399;

            .sort-order,
            .created-time {
              display: flex;
              align-items: center;
            }
          }
        }

        .brand-actions {
          display: flex;
          flex-shrink: 0;
          gap: 8px;
        }
      }
    }

    .empty-state {
      padding: 40px 20px;
      text-align: center;
    }
  }
</style>
