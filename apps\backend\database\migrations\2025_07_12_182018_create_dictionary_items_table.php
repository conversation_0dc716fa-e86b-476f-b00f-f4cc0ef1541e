<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dictionary_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id')->comment('分类ID');
            $table->string('code', 50)->comment('字典编码');
            $table->string('value', 200)->comment('字典值');
            $table->string('label', 200)->nullable()->comment('显示标签');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->string('color', 20)->nullable()->comment('颜色值');
            $table->string('icon', 50)->nullable()->comment('图标');
            $table->json('config')->nullable()->comment('扩展配置');
            $table->string('remark', 500)->nullable()->comment('备注');
            $table->boolean('is_enabled')->default(true)->comment('是否启用');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
            // $table->softDeletes();

            // 唯一索引：分类内编码唯一
            $table->unique(['category_id', 'code']);

            // 普通索引
            $table->index('category_id');
            $table->index('is_enabled');
            $table->index('sort');

            // 外键约束已移除，使用 Laravel ORM 关系管理
        });

        // 添加表注释
        DB::statement("ALTER TABLE dictionary_items COMMENT '字典项表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dictionary_items');
    }
};
