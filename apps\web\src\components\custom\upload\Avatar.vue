<template>
  <div class="avatar-upload">
    <ElUpload
      ref="uploadRef"
      v-model:file-list="fileList"
      :class="['avatar-uploader', { 'has-avatar': hasAvatar }]"
      :http-request="customUpload"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :limit="2"
      :accept="accept"
      :show-file-list="false"
      :disabled="disabled"
      :on-change="handleChange"
    >
      <!-- 已有头像时显示 -->
      <div v-if="hasAvatar" class="avatar-container">
        <img :src="avatarUrl" class="avatar" :alt="altText" />
        <div class="avatar-mask">
          <ElIcon :size="20"><Upload /></ElIcon>
          <div class="mask-text">{{ maskText }}</div>
        </div>
        <!-- 删除按钮 -->
        <ElIcon v-if="!disabled && showRemove" class="remove-btn" @click.stop="handleClear">
          <CircleClose />
        </ElIcon>
      </div>

      <!-- 无头像时显示上传区域 -->
      <div v-else class="upload-trigger">
        <ElIcon :size="iconSize" class="upload-icon"><Plus /></ElIcon>
        <div v-if="showUploadText" class="upload-text">{{ uploadText }}</div>
      </div>
    </ElUpload>

    <!-- 提示文字 -->
    <div v-if="tip && showTip" class="upload-tip">
      {{ tip }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, toRef } from 'vue'
  import { Plus, Upload, CircleClose } from '@element-plus/icons-vue'
  import { ElUpload, ElIcon, type UploadProps } from 'element-plus'
  import { useUpload } from '@/composables/useUpload'
  import type { AttachmentItem } from '@/types/api'

  /**
   * 头像上传组件
   *
   * @description
   * 专门用于上传用户头像的组件，支持预览、更换、删除功能。
   * 使用圆形展示区域，适合用户头像、Logo等场景。
   *
   * @example
   * ```vue
   * <AvatarUpload
   *   v-model="avatarId"
   *   :attachments="avatarData"
   *   :size="120"
   *   tip="建议上传正方形图片"
   * />
   * ```
   */
  defineOptions({
    name: 'AvatarUpload'
  })

  interface Props {
    /**
     * 附件详情数组（用于编辑时显示）
     * 虽然头像只有一个，但为了与 useUpload 保持一致，使用数组
     */
    attachments?: AttachmentItem[]
    /**
     * 单个文件最大大小（MB）
     * @default 10
     */
    maxSize?: number
    /**
     * 接受的文件类型
     * @default 'image/*'
     */
    accept?: string
    /**
     * 提示文字
     * @default '只能上传图片文件，且不超过10MB'
     */
    tip?: string
    /**
     * 上传区域文字
     * @default '上传头像'
     */
    uploadText?: string
    /**
     * 是否禁用
     * @default false
     */
    disabled?: boolean
    /**
     * 头像大小（宽高，单位：px）
     * @default 100
     */
    size?: number
    /**
     * 是否显示删除按钮
     * @default true
     */
    showRemove?: boolean
    /**
     * 是否显示提示文字
     * @default true
     */
    showTip?: boolean
    /**
     * 是否显示上传文字
     * @default true
     */
    showUploadText?: boolean
    /**
     * 头像替代文字
     * @default '头像'
     */
    altText?: string
    /**
     * 遮罩层文字
     * @default '更换头像'
     */
    maskText?: string
    /**
     * 上传图标大小
     * @default 32
     */
    iconSize?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    attachments: () => [],
    maxSize: 10,
    accept: 'image/*',
    tip: '只能上传图片文件，且不超过10MB',
    uploadText: '上传头像',
    disabled: false,
    size: 100,
    showRemove: true,
    showTip: true,
    showUploadText: true,
    altText: '头像',
    maskText: '更换头像',
    iconSize: 32
  })

  /**
   * 定义 v-model，支持单个ID或null
   * 内部转换为数组与 useUpload 兼容
   */
  const modelValue = defineModel<number | null>({
    default: null
  })

  /**
   * 定义事件
   */
  const emit = defineEmits<{
    /**
     * 头像变化事件
     */
    change: [value: number | null]
    /**
     * 上传成功事件
     */
    'upload-success': [file: any]
    /**
     * 上传失败事件
     */
    'upload-error': [error: any]
  }>()

  /**
   * 内部使用的ID数组，用于与 useUpload 兼容
   */
  const idsArray = computed({
    get: () => (modelValue.value ? [modelValue.value] : []),
    set: (newIds) => {
      modelValue.value = newIds.length > 0 ? newIds[0] : null
      emit('change', modelValue.value)
    }
  })

  /**
   * 使用上传 composable
   */
  const {
    fileList,
    fileIdMap,
    beforeUpload,
    customUpload,
    handleSuccess: baseHandleSuccess,
    handleError: baseHandleError,
    handleRemove: baseHandleRemove,
    updateModelValue
  } = useUpload(idsArray, toRef(props, 'attachments'), {
    limit: 1,
    maxSize: props.maxSize,
    accept: props.accept,
    multiple: false,
    showSuccessMessage: true,
    disablePaste: true,
    onSuccess: (file) => {
      emit('upload-success', file.response)
    },
    onError: (error) => {
      emit('upload-error', error)
    }
  })

  /**
   * 是否有头像
   */
  const hasAvatar = computed(() => {
    return fileList.value.length > 0 && fileList.value[0].status === 'success'
  })

  /**
   * 头像URL
   */
  const avatarUrl = computed(() => {
    if (hasAvatar.value) {
      return fileList.value[0].url || ''
    }
    return ''
  })

  /**
   * 处理成功，包装参数
   */
  const handleSuccess: UploadProps['onSuccess'] = (response, file, fileList) => {
    baseHandleSuccess(response, file, fileList)
  }

  /**
   * 处理错误，包装参数
   */
  const handleError: UploadProps['onError'] = (error, file) => {
    baseHandleError(error, file)
  }

  /**
   * 处理移除
   */
  const handleRemove = (file: any) => {
    baseHandleRemove(file)
  }

  /**
   * 处理文件变化
   * 当有2个文件时，移除第一个保留最新的
   */
  const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
    // 当有2个文件时（达到limit），保留最新的
    if (uploadFiles.length === 2) {
      // 确保是新文件触发的变化，而不是状态更新
      const newFile = uploadFiles.find((f) => f.uid === uploadFile.uid)
      if (newFile && newFile === uploadFiles[1]) {
        // 获取旧文件
        const oldFile = uploadFiles[0]

        // 从映射中移除旧文件的ID
        if (oldFile.uid) {
          fileIdMap.value.delete(oldFile.uid.toString())
        }

        // 只保留新文件
        fileList.value = [uploadFiles[1]]

        // 如果新文件已上传成功，更新v-model
        if (uploadFile.status === 'success') {
          updateModelValue()
        }
      }
    }
  }

  /**
   * 清除头像
   */
  const handleClear = () => {
    if (fileList.value.length > 0) {
      handleRemove(fileList.value[0])
    }
  }

  /**
   * 动态样式
   */
  const uploadStyles = computed(() => ({
    width: `${props.size}px`,
    height: `${props.size}px`
  }))

  /**
   * 暴露方法给父组件
   */
  defineExpose({
    /**
     * 清除头像
     */
    clearAvatar: handleClear,
    /**
     * 获取当前头像URL
     */
    getAvatarUrl: () => avatarUrl.value
  })
</script>

<style lang="scss" scoped>
  .avatar-upload {
    display: inline-block;

    .avatar-uploader {
      :deep(.el-upload) {
        position: relative;
        width: v-bind('uploadStyles.width');
        height: v-bind('uploadStyles.height');
        overflow: hidden;
        cursor: pointer;
        background-color: #fafafa;
        border: 1px dashed #d9d9d9;
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          border-color: var(--el-color-primary);
        }
      }

      &.has-avatar {
        :deep(.el-upload) {
          background-color: transparent;
          border: none;
        }
      }
    }

    .avatar-container {
      position: relative;
      width: 100%;
      height: 100%;

      .avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      .avatar-mask {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #fff;
        background: rgb(0 0 0 / 50%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s;

        .mask-text {
          margin-top: 4px;
          font-size: 12px;
        }
      }

      &:hover .avatar-mask {
        opacity: 1;
      }

      .remove-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: #f56c6c;
        cursor: pointer;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgb(0 0 0 / 12%);
        transition: all 0.3s;

        &:hover {
          color: #f23c3c;
          transform: scale(1.1);
        }
      }
    }

    .upload-trigger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #8c939d;
      transition: color 0.3s;

      .upload-icon {
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 12px;
        white-space: nowrap;
      }

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .upload-tip {
      margin-top: 8px;
      font-size: 12px;
      line-height: 1.4;
      color: #909399;
      text-align: center;
    }
  }
</style>
