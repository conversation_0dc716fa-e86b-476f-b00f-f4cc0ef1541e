<template>
  <div
    ref="uploadContainer"
    class="attachment-upload"
    tabindex="0"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <ElUpload
      v-model:file-list="fileList"
      :class="{ 'hide-upload': hideUpload }"
      :list-type="listType"
      :http-request="customUpload"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :disabled="disabled"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :auto-upload="autoUpload"
      :drag="drag"
    >
      <!-- 上传触发器插槽 -->
      <slot>
        <ElButton type="primary" plain size="small" :disabled="disabled">
          <ElIcon class="el-icon--left"><Upload /></ElIcon>
          {{ buttonText }}
        </ElButton>
      </slot>

      <!-- 提示信息插槽 -->
      <template #tip v-if="showTip">
        <div class="el-upload__tip">
          <slot name="tip">
            {{ tipText }}
          </slot>
        </div>
      </template>
    </ElUpload>

    <!-- 图片预览器 -->
    <ElImageViewer
      v-if="showImageViewer"
      :url-list="imagePreviewList"
      :initial-index="imageViewerIndex"
      @close="closeImageViewer"
      teleported
    />
  </div>
</template>

<script setup lang="ts">
  import { computed, toRef, nextTick, watch, ref } from 'vue'
  import { ElMessage, ElUpload, ElButton, ElIcon, ElImageViewer } from 'element-plus'
  import { Upload } from '@element-plus/icons-vue'
  import type { UploadProps } from 'element-plus'
  import { useUpload } from '@/composables/useUpload'
  import type { AttachmentItem } from '@/types/api'

  /**
   * 附件上传组件
   *
   * @description
   * 通用的附件上传组件，支持多文件上传、文件类型限制、大小限制等功能。
   * 使用 Vue 3.5 的 defineModel 实现 v-model 双向绑定。
   *
   * @example
   * ```vue
   * <AttachmentUpload
   *   v-model="attachmentIds"
   *   :attachments="attachmentList"
   *   :limit="5"
   *   :max-size="10"
   *   accept=".pdf,.doc,.docx"
   *   button-text="上传文档"
   * />
   * ```
   */
  defineOptions({
    name: 'AttachmentUpload'
  })

  interface Props {
    /**
     * 附件详情数组（用于编辑时显示）
     */
    attachments?: AttachmentItem[]
    /**
     * 列表展示类型
     * @default 'text'
     */
    listType?: 'text' | 'picture' | 'picture-card'
    /**
     * 是否支持多选
     * @default true
     */
    multiple?: boolean
    /**
     * 最大上传数量
     * @default 10
     */
    limit?: number
    /**
     * 接受的文件类型，如 '.jpg,.png' 或 'image/*'
     * @default ''
     */
    accept?: string
    /**
     * 单个文件最大大小（MB）
     * @default 10
     */
    maxSize?: number
    /**
     * 是否禁用
     * @default false
     */
    disabled?: boolean
    /**
     * 是否显示提示信息
     * @default true
     */
    showTip?: boolean
    /**
     * 上传按钮文字
     * @default '上传附件'
     */
    buttonText?: string
    /**
     * 提示文字
     * @default '最多上传10个文件，单个文件不超过10MB'
     */
    tipText?: string
    /**
     * 是否自动上传
     * @default true
     */
    autoUpload?: boolean
    /**
     * 是否启用拖拽上传
     * @default false
     */
    drag?: boolean
    /**
     * 是否显示主图功能（仅图片文件）
     * @default false
     */
    showPrimary?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    attachments: () => [],
    listType: 'text',
    multiple: true,
    limit: 10,
    accept: '',
    maxSize: 10,
    disabled: false,
    showTip: true,
    buttonText: '上传附件',
    tipText: '最多上传10个文件，单个文件不超过10MB，支持粘贴上传 (Ctrl+V)',
    autoUpload: true,
    drag: false,
    showPrimary: false
  })

  /**
   * 定义 v-model，使用 Vue 3.5 的 defineModel
   */
  const modelValue = defineModel<number[]>({
    default: () => []
  })

  /**
   * 定义事件
   */
  const emit = defineEmits<{
    /**
     * 附件列表变化事件
     */
    change: [value: number[]]
    /**
     * 上传成功事件
     */
    'upload-success': [file: any]
    /**
     * 上传失败事件
     */
    'upload-error': [error: any]
  }>()

  /**
   * 使用上传 composable
   */
  const {
    fileList,
    hasUploading,
    isLimitReached,
    beforeUpload,
    customUpload,
    handleSuccess: baseHandleSuccess,
    handleError: baseHandleError,
    handleRemove,
    handleExceed,
    clearFiles,
    getFiles,
    containerRef
  } = useUpload(modelValue, toRef(props, 'attachments'), {
    limit: props.limit,
    maxSize: props.maxSize,
    accept: props.accept,
    multiple: props.multiple,
    showSuccessMessage: false, // 自定义成功消息
    onSuccess: (file) => {
      // 触发成功事件
      emit('upload-success', file.response)
      // 触发 change 事件
      emit('change', modelValue.value)
      // 显示成功消息
      ElMessage.success('文件上传成功')
    },
    onError: (error) => {
      // 触发错误事件
      emit('upload-error', error)
    },
    onRemove: () => {
      // 触发 change 事件
      emit('change', modelValue.value)
    }
  })

  /**
   * 是否隐藏上传按钮
   * 单文件模式下，达到限制时隐藏
   */
  const hideUpload = computed(() => {
    return !props.multiple && isLimitReached.value
  })

  /**
   * 获取所有图片的预览URL列表
   */
  const imagePreviewList = computed(() => {
    return fileList.value
      .filter((file) => isImageFile(file))
      .map((file) => file.url)
      .filter(Boolean) as string[]
  })

  /**
   * 图片预览器相关状态
   */
  const showImageViewer = ref(false)
  const imageViewerIndex = ref(0)

  /**
   * 关闭图片预览器
   */
  const closeImageViewer = () => {
    showImageViewer.value = false
    imageViewerIndex.value = 0
  }

  /**
   * 包装处理成功方法，确保参数正确传递
   */
  const handleSuccess: UploadProps['onSuccess'] = (response, file, fileList) => {
    baseHandleSuccess(response, file, fileList)
  }

  /**
   * 包装错误处理方法
   */
  const handleError: UploadProps['onError'] = (error, file) => {
    baseHandleError(error, file)
  }

  /**
   * 容器元素引用
   */
  const uploadContainer = containerRef

  /**
   * 处理容器获得焦点
   */
  const handleFocus = () => {
    // 可以添加视觉提示
  }

  /**
   * 处理容器失去焦点
   */
  const handleBlur = () => {
    // 可以移除视觉提示
  }

  /**
   * 判断是否为图片文件
   */
  const isImageFile = (file: any) => {
    // 检查 MIME 类型
    const mimeType = file.response?.mime_type || file.response?.data?.mime_type || file.raw?.type
    if (mimeType) {
      return mimeType.startsWith('image/')
    }

    // 如果没有 MIME 类型，通过文件扩展名判断
    const fileName = file.name || file.response?.file_name || file.response?.data?.file_name || ''
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
    return imageExtensions.some((ext) => fileName.toLowerCase().endsWith(ext))
  }

  /**
   * 获取文件在列表中的索引
   */
  const getFileIndex = (file: any) => {
    return fileList.value.findIndex((item) => item.uid === file.uid)
  }

  /**
   * 设置为主图（将指定文件移动到第一位）
   */
  const setAsPrimary = (file: any) => {
    const currentIndex = getFileIndex(file)
    if (currentIndex <= 0) return // 已经是第一位或找不到文件

    // 移动文件到第一位
    const fileListCopy = [...fileList.value]
    const [targetFile] = fileListCopy.splice(currentIndex, 1)
    fileListCopy.unshift(targetFile)

    // 更新文件列表
    fileList.value = fileListCopy

    // 更新modelValue（提取对应的附件ID）
    const newIds = fileListCopy
      .map((item) => {
        const response = item.response as any
        return response?.id || response?.data?.id
      })
      .filter((id) => id !== undefined)

    modelValue.value = newIds

    // 触发change事件
    emit('change', modelValue.value)

    ElMessage.warning('主图已调整，请点击保存按钮生效')

    // 更新主图功能
    nextTick(() => {
      updatePrimaryFunction()
    })
  }

  /**
   * 为缩略图添加点击事件处理器
   */
  const addThumbnailClickHandlers = () => {
    try {
      nextTick(() => {
        const uploadList = uploadContainer.value?.querySelector('.el-upload-list')
        if (!uploadList) return

        const thumbnails = uploadList.querySelectorAll('.el-upload-list__item-thumbnail')
        const fileNameLinks = uploadList.querySelectorAll('.el-upload-list__item-name')

        // 禁用文件名的点击行为
        fileNameLinks.forEach((link: Element) => {
          if (link instanceof HTMLElement) {
            link.style.cursor = 'default'
            link.style.pointerEvents = 'none'
          }
        })

        // 为缩略图添加点击事件
        thumbnails.forEach((thumbnail: Element, index: number) => {
          if (thumbnail instanceof HTMLElement) {
            const file = fileList.value[index]
            if (!file || !isImageFile(file)) return

            // 移除已存在的点击事件
            const newThumbnail = thumbnail.cloneNode(true) as HTMLElement
            thumbnail.parentNode?.replaceChild(newThumbnail, thumbnail)

            // 添加新的点击事件
            newThumbnail.style.cursor = 'pointer'
            newThumbnail.addEventListener('click', (e) => {
              e.stopPropagation()
              e.preventDefault()

              if (file.url && imagePreviewList.value.length > 0) {
                const currentIndex = imagePreviewList.value.indexOf(file.url)
                if (currentIndex !== -1) {
                  imageViewerIndex.value = currentIndex
                  showImageViewer.value = true
                }
              }
            })
          }
        })
      })
    } catch (error) {
      console.warn('添加缩略图点击事件失败:', error)
    }
  }

  /**
   * 更新主图功能显示
   */
  const updatePrimaryFunction = () => {
    if (!props.showPrimary || props.listType !== 'picture') return

    nextTick(() => {
      const uploadList = uploadContainer.value?.querySelector('.el-upload-list--picture')
      if (!uploadList) return

      const listItems = uploadList.querySelectorAll('.el-upload-list__item')

      listItems.forEach((item: Element, index: number) => {
        // 移除已存在的主图功能
        const existingPrimary = item.querySelector('.primary-function')
        if (existingPrimary) {
          existingPrimary.remove()
        }

        const file = fileList.value[index]
        if (!file || !isImageFile(file)) return

        // 创建主图功能容器
        const primaryDiv = document.createElement('div')
        primaryDiv.className = 'primary-function'

        if (index === 0) {
          // 第一张图片显示主图标识
          primaryDiv.innerHTML = `
            <div class="primary-badge">
              <span class="primary-icon">⭐</span>
              <span class="primary-text">主图</span>
            </div>
          `
        } else {
          // 其他图片显示设为主图按钮
          primaryDiv.innerHTML = `
            <button class="set-primary-btn" type="button">设为主图</button>
          `

          // 添加点击事件
          const btn = primaryDiv.querySelector('.set-primary-btn') as HTMLButtonElement
          if (btn) {
            btn.addEventListener('click', (e) => {
              e.stopPropagation()
              setAsPrimary(file)
            })
          }
        }

        // 添加到列表项
        item.appendChild(primaryDiv)
      })

      // 在主图功能更新后，同时更新缩略图点击事件
      addThumbnailClickHandlers()
    })
  }

  // 监听文件列表变化，更新主图功能和缩略图点击事件
  watch(
    () => fileList.value.length,
    () => {
      if (props.showPrimary && props.listType === 'picture') {
        updatePrimaryFunction()
      } else {
        // 即使没有主图功能，也要添加缩略图点击事件
        addThumbnailClickHandlers()
      }
    },
    { flush: 'post' }
  )

  // 监听文件列表内容变化
  watch(
    fileList,
    () => {
      if (props.showPrimary && props.listType === 'picture') {
        updatePrimaryFunction()
      } else {
        // 即使没有主图功能，也要添加缩略图点击事件
        addThumbnailClickHandlers()
      }
    },
    { deep: true, flush: 'post' }
  )

  /**
   * 暴露方法给父组件
   */
  defineExpose({
    /**
     * 获取已上传的文件列表
     */
    getFiles,
    /**
     * 清空文件列表
     */
    clearFiles,
    /**
     * 是否有文件正在上传
     */
    hasUploading
  })
</script>

<style lang="scss" scoped>
  .attachment-upload {
    width: 100%;
    border-radius: 4px;
    outline: none;
    transition: box-shadow 0.3s;

    // 聚焦状态
    &:focus {
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }

    // 隐藏上传按钮（单文件模式下达到限制时）
    .hide-upload {
      :deep(.el-upload) {
        display: none;
      }
    }

    // 拖拽上传区域样式
    :deep(.el-upload-dragger) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 180px;

      .el-icon {
        margin-bottom: 16px;
        font-size: 67px;
        color: var(--el-text-color-placeholder);
      }

      .el-upload__text {
        font-size: 14px;
        color: var(--el-text-color-regular);

        em {
          font-style: normal;
          color: var(--el-color-primary);
        }
      }
    }

    // picture 列表模式中的主图功能样式
    :deep(.el-upload-list--picture) {
      .el-upload-list__item {
        position: relative;
        transition: all 0.3s;

        // 为主图功能预留空间
        .el-upload-list__item-info {
          padding-right: 120px; // 为主图区域预留空间
        }

        // 主图功能容器
        .primary-function {
          position: absolute;
          top: 50%;
          right: 16px;
          z-index: 10;
          display: flex;
          align-items: center;
          transform: translateY(-50%);

          // 主图标识样式
          .primary-badge {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #8b4513;
            white-space: nowrap;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border: 1px solid rgb(255 215 0 / 50%);
            border-radius: 12px;
            box-shadow: 0 2px 6px rgb(255 215 0 / 30%);

            .primary-icon {
              margin-right: 4px;
              font-size: 14px;
            }

            .primary-text {
              font-size: 12px;
              line-height: 1;
            }
          }

          // 设为主图按钮样式
          .set-primary-btn {
            padding: 6px 12px;
            font-size: 12px;
            color: #e6a23c;
            white-space: nowrap;
            cursor: pointer;
            background: #fff;
            border: 1px solid #e6a23c;
            border-radius: 12px;
            transition: all 0.3s;

            &:hover {
              color: #fff;
              background: #e6a23c;
            }

            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px rgb(230 162 60 / 20%);
            }
          }
        }
      }
    }

    // 缩略图可点击样式
    :deep(.el-upload-list__item-thumbnail) {
      cursor: pointer !important;
      transition: transform 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }

    // 文件名不可点击样式
    :deep(.el-upload-list__item-name) {
      pointer-events: none !important;
      cursor: default !important;
    }
  }
</style>
