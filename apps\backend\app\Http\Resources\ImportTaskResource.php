<?php

namespace App\Http\Resources;

use App\Models\ImportTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 统一导入任务资源类
 */
class ImportTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var ImportTask $this */
        return [
            'id' => $this->id,
            'type' => $this->type,
            'type_text' => $this->getTypeText(),
            'file_path' => $this->file_path,
            'original_filename' => $this->original_filename,
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'total_rows' => $this->total_rows,
            'success_rows' => $this->success_rows,
            'failed_rows' => $this->failed_rows,
            'progress_percent' => $this->getProgressPercent(),
            'error_details' => $this->error_details,
            'summary' => $this->summary,
            'created_by' => $this->created_by,
            'started_at' => $this->started_at ? date('Y-m-d H:i:s', $this->started_at) : null,
            'completed_at' => $this->completed_at ? date('Y-m-d H:i:s', $this->completed_at) : null,
            'created_at' => $this->created_at ? date('Y-m-d H:i:s', $this->created_at) : null,
            'updated_at' => $this->updated_at ? date('Y-m-d H:i:s', $this->updated_at) : null,
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                ];
            }),
            'duration' => $this->getDuration(),
            'error_count' => $this->getErrorCount(),
            'has_errors' => $this->hasErrors(),
        ];
    }

    /**
     * 获取类型文本
     */
    protected function getTypeText(): string
    {
        return match ($this->type) {
            'asset' => '资产',
            'category' => '分类',
            'entity' => '相关方',
            'user' => '用户',
            default => '未知类型',
        };
    }

    /**
     * 获取状态文本
     */
    protected function getStatusText(): string
    {
        return match ($this->status) {
            ImportTask::STATUS_PENDING => '等待处理',
            ImportTask::STATUS_PROCESSING => '处理中',
            ImportTask::STATUS_COMPLETED => '已完成',
            ImportTask::STATUS_FAILED => '失败',
            default => '未知状态',
        };
    }

    /**
     * 计算进度百分比
     */
    protected function getProgressPercent(): int
    {
        if ($this->total_rows <= 0) {
            return 0;
        }

        $processedRows = $this->success_rows + $this->failed_rows;
        return (int) round(($processedRows / $this->total_rows) * 100);
    }

    /**
     * 获取处理时长（秒）
     */
    protected function getDuration(): ?int
    {
        if (!$this->started_at) {
            return null;
        }

        $endTime = $this->completed_at ?: time();
        return $endTime - $this->started_at;
    }

    /**
     * 获取错误数量
     */
    protected function getErrorCount(): int
    {
        if (!$this->error_details || !is_array($this->error_details)) {
            return 0;
        }

        return count($this->error_details);
    }

    /**
     * 是否有错误
     */
    protected function hasErrors(): bool
    {
        return $this->failed_rows > 0 || $this->status === ImportTask::STATUS_FAILED;
    }
}
