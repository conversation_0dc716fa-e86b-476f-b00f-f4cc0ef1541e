import request from '@/utils/http'
import type {
  DictionaryCategory,
  DictionaryItem,
  DictionaryCategoryForm,
  DictionaryItemForm,
  Category,
  CategoryForm
} from '@/types/api'
import { buildTree } from '@/utils/dataprocess'

// ========== 字典管理 ==========

/**
 * 字段映射：前端到后端
 */
const transformCategoryToBackend = (data: DictionaryCategoryForm): any => {
  return {
    name: data.name,
    code: data.code,
    description: data.description,
    is_enabled: data.enabled,
    sort: data.sort || 0
  }
}

/**
 * 字段映射：后端到前端（分类）
 */
const transformCategoryFromBackend = (data: any): DictionaryCategory => {
  return {
    id: String(data.id),
    name: data.name,
    code: data.code,
    description: data.description || '',
    enabled: data.is_enabled,
    sort: data.sort || 0,
    item_count: data.item_count || 0,
    created_at: data.created_at,
    updated_at: data.updated_at
  }
}

/**
 * 字段映射：前端到后端（字典项）
 */
const transformItemToBackend = (data: DictionaryItemForm): any => {
  return {
    category_id: Number(data.category_id),
    code: data.code,
    value: data.value,
    is_enabled: data.enabled,
    sort: data.sort || 0,
    remark: data.remark || '',
    color: data.color || '',
    config: data.config || {} // 直接传递对象，不转为字符串
  }
}

/**
 * 字段映射：后端到前端（字典项）
 */
const transformItemFromBackend = (data: any): DictionaryItem => {
  return {
    id: String(data.id),
    category_id: String(data.category_id),
    code: data.code,
    value: data.value,
    enabled: data.is_enabled,
    sort: data.sort || 0,
    remark: data.remark || '',
    color: data.color || '',
    config: data.config
      ? typeof data.config === 'string'
        ? JSON.parse(data.config)
        : data.config
      : {},
    created_at: data.created_at,
    updated_at: data.updated_at
  }
}

/**
 * 获取字典分类列表
 */
export const getDictionaryCategories = (params?: {
  name?: string
  code?: string
  is_enabled?: string | boolean
}): Promise<DictionaryCategory[]> => {
  const backendParams: any = {}

  if (params) {
    if (params.name) backendParams.name = params.name
    if (params.code) backendParams.code = params.code
    if (params.is_enabled !== undefined) {
      backendParams.is_enabled = params.is_enabled
    }
  }

  return request
    .get<any[]>({
      url: '/api/admin/dictionary/categories',
      params: backendParams
    })
    .then((res) => res.map(transformCategoryFromBackend))
}

/**
 * 创建字典分类
 */
export const createDictionaryCategory = (
  data: DictionaryCategoryForm
): Promise<DictionaryCategory> => {
  return request
    .post<any>({
      url: '/api/admin/dictionary/categories',
      data: transformCategoryToBackend(data)
    })
    .then(transformCategoryFromBackend)
}

/**
 * 更新字典分类
 */
export const updateDictionaryCategory = (
  id: string,
  data: DictionaryCategoryForm
): Promise<DictionaryCategory> => {
  return request
    .put<any>({
      url: `/api/admin/dictionary/categories/${id}`,
      data: transformCategoryToBackend(data)
    })
    .then(transformCategoryFromBackend)
}

/**
 * 删除字典分类
 */
export const deleteDictionaryCategory = (id: string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/dictionary/categories/${id}`
  })
}

/**
 * 获取字典项列表
 */
export const getDictionaryItems = (params?: {
  categoryId?: string
  code?: string
  value?: string
  enabled?: boolean
}): Promise<DictionaryItem[]> => {
  const backendParams: any = {}

  if (params) {
    if (params.categoryId) backendParams.category_id = Number(params.categoryId)
    if (params.code) backendParams.code = params.code
    if (params.value) backendParams.value = params.value
    if (params.enabled !== undefined) backendParams.is_enabled = params.enabled
  }

  return request
    .get<any[]>({
      url: '/api/admin/dictionary/items',
      params: backendParams
    })
    .then((res) => res.map(transformItemFromBackend))
}

/**
 * 创建字典项
 */
export const createDictionaryItem = (data: DictionaryItemForm): Promise<DictionaryItem> => {
  return request
    .post<any>({
      url: '/api/admin/dictionary/items',
      data: transformItemToBackend(data)
    })
    .then(transformItemFromBackend)
}

/**
 * 更新字典项
 */
export const updateDictionaryItem = (
  id: string,
  data: DictionaryItemForm
): Promise<DictionaryItem> => {
  return request
    .put<any>({
      url: `/api/admin/dictionary/items/${id}`,
      data: transformItemToBackend(data)
    })
    .then(transformItemFromBackend)
}

/**
 * 删除字典项
 */
export const deleteDictionaryItem = (id: string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/dictionary/items/${id}`
  })
}

/**
 * 根据分类代码获取字典项
 * 特殊接口，获取指定分类下的所有启用的字典项
 */
export const getDictionaryByCode = (categoryCode: string): Promise<DictionaryItem[]> => {
  return request
    .get<any[]>({
      url: `/api/admin/dictionary/code/${categoryCode}`
    })
    .then((res) => res.map(transformItemFromBackend))
}

// ========== 分类管理 ==========

/**
 * 获取分类列表（树形结构）
 */
export const getCategoryList = async (): Promise<Category[]> => {
  const flatData = await request.get<Category[]>({
    url: '/api/admin/categories'
  })
  // 分类的根节点 parent_id 为 0
  return buildTree<Category>(flatData, { rootParentValue: 0 })
}

/**
 * 创建分类
 */
export const createCategory = (data: CategoryForm): Promise<Category> => {
  return request.post<Category>({
    url: '/api/admin/categories',
    data
  })
}

/**
 * 更新分类
 */
export const updateCategory = (id: number | string, data: CategoryForm): Promise<Category> => {
  return request.put<Category>({
    url: `/api/admin/categories/${id}`,
    data
  })
}

/**
 * 删除分类
 */
export const deleteCategory = (id: number | string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/categories/${id}`
  })
}

/**
 * 获取分类的子分类
 */
export const getCategoryChildren = (parentId: number | string): Promise<Category[]> => {
  return request.get<Category[]>({
    url: `/api/admin/categories/children/${parentId}`
  })
}
