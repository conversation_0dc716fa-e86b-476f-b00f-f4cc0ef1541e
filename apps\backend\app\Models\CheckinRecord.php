<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\BaseModel;
use App\Traits\HasAttachments;

class CheckinRecord extends BaseModel
{

    use HasAttachments;

    protected $fillable = [
        'checkin_config_id',
        'user_id',
        'checkin_time',
        'status',
        'location_range',
        'location',
        'latitude',
        'longitude',
        'ip_address',
        'attachment_id',
        'content'
    ];

    protected $casts = [
        'checkin_config_id' => 'integer',
        'user_id' => 'integer',
        'checkin_time' => 'integer',
        'status' => 'integer',
        'location_range' => 'integer',
        'attachment_id' => 'integer'
    ];

    protected $hidden = [
        'created_at',
        'updated_at'
    ];

    public function config(): BelongsTo
    {
        return $this->belongsTo(CheckinConfig::class, 'checkin_config_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function attachment(): BelongsTo
    {
        return $this->belongsTo(Attachment::class);
    }
}
