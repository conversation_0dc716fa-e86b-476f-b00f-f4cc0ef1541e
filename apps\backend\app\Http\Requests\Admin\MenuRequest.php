<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $menuId = $this->route('menu')?->id;

        return [
            'parent_id' => 'nullable|exists:menus,id',
            'name' => [
                'nullable',  // 改为可选，后端会自动生成
                'string',
                'max:50',
                Rule::unique('menus')->ignore($menuId),
            ],
            'path' => 'nullable|string|max:255',  // 改为可选，后端会自动生成
            'component' => 'nullable|string|max:255',
            'title' => 'required|string|max:100',
            'icon' => 'nullable|string|max:50',
            'label' => 'nullable|string|max:100',
            'sort' => 'integer',
            'is_hide' => 'boolean',
            'is_hide_tab' => 'boolean',
            'link' => 'nullable|string|max:255',
            'is_iframe' => 'boolean',
            'keep_alive' => 'boolean',
            'is_first_level' => 'boolean',
            'fixed_tab' => 'boolean',
            'active_path' => 'nullable|string|max:255',
            'is_full_page' => 'boolean',
            'show_badge' => 'boolean',
            'show_text_badge' => 'nullable|string|max:50',
            'status' => 'boolean',
            'permissions' => 'array',
            'permissions.*.title' => 'required_with:permissions|string|max:50',
            'permissions.*.auth_mark' => 'nullable|string|max:100',  // 改为可选，后端会自动生成
            'permissions.*.sort' => 'integer',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'parent_id.exists' => '父级菜单不存在',
            'name.unique' => '路由名称已存在',
            'name.max' => '路由名称不能超过50个字符',
            'path.max' => '路由路径不能超过255个字符',
            'title.required' => '菜单标题不能为空',
            'title.max' => '菜单标题不能超过100个字符',
            'permissions.*.title.required_with' => '权限名称不能为空',
        ];
    }
}
