<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\EntityBrand
 */
class BrandResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'entity_id' => $this->entity_id,
            'entity_name' => $this->entity?->name,
            'display_name' => $this->entity?->name . ' - ' . $this->name,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
        ];
    }
}