/**
 * 考勤管理相关API接口
 */

import request from '@/utils/http'
import type { PaginatedResponse } from '@/types/api/pagination'
import type {
  CheckinConfig,
  CheckinRecord,
  CheckinConfigForm,
  CheckinConfigSearchParams,
  CheckinConfigSwitchParams,
  CheckinRecordForm,
  CheckinRecordSearchParams,
  LifecycleOption,
  UserOption
} from '@/types/api/attendance'

// ========== 考勤配置接口 ==========

/**
 * 获取考勤配置列表
 */
export const getCheckinConfigs = (
  params?: CheckinConfigSearchParams
): Promise<PaginatedResponse<CheckinConfig>> => {
  return request.get<PaginatedResponse<CheckinConfig>>({
    url: '/api/admin/checkin-configs',
    params
  })
}

/**
 * 获取考勤配置详情
 */
export const getCheckinConfig = (id: number): Promise<CheckinConfig> => {
  return request.get<CheckinConfig>({
    url: `/api/admin/checkin-configs/${id}`
  })
}

/**
 * 创建考勤配置
 */
export const createCheckinConfig = (data: CheckinConfigForm): Promise<CheckinConfig> => {
  return request.post<CheckinConfig>({
    url: '/api/admin/checkin-configs',
    data
  })
}

/**
 * 更新考勤配置
 */
export const updateCheckinConfig = (
  id: number,
  data: Partial<CheckinConfigForm>
): Promise<CheckinConfig> => {
  return request.put<CheckinConfig>({
    url: `/api/admin/checkin-configs/${id}`,
    data
  })
}

/**
 * 删除考勤配置
 */
export const deleteCheckinConfig = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/checkin-configs/${id}`
  })
}

/**
 * 切换考勤配置状态（启用/禁用）
 */
export const switchCheckinConfig = (
  id: number,
  data: CheckinConfigSwitchParams
): Promise<CheckinConfig> => {
  return request.put<CheckinConfig>({
    url: `/api/admin/checkin-configs/${id}/switch`,
    data
  })
}

// ========== 打卡记录接口 ==========

/**
 * 获取打卡记录列表
 */
export const getCheckinRecords = (
  params?: CheckinRecordSearchParams
): Promise<PaginatedResponse<CheckinRecord>> => {
  return request.get<PaginatedResponse<CheckinRecord>>({
    url: '/api/admin/checkin-records',
    params
  })
}

/**
 * 创建打卡记录（打卡操作）
 */
export const createCheckinRecord = (data: CheckinRecordForm): Promise<CheckinRecord> => {
  return request.post<CheckinRecord>({
    url: '/api/admin/checkin-records',
    data
  })
}

/**
 * 获取打卡记录详情
 */
export const getCheckinRecord = (id: number): Promise<CheckinRecord> => {
  return request.get<CheckinRecord>({
    url: `/api/admin/checkin-records/${id}`
  })
}

// ========== 辅助接口 ==========

/**
 * 获取生命周期项目选项列表
 * 用于下拉选择
 */
export const getLifecycleOptions = (params?: {
  keyword?: string
  status?: number
  page?: number
  per_page?: number
}): Promise<PaginatedResponse<LifecycleOption>> => {
  return request.get<PaginatedResponse<LifecycleOption>>({
    url: '/api/admin/lifecycles',
    params: {
      ...params,
      per_page: params?.per_page || 100 // 默认获取100条
    }
  })
}

/**
 * 获取用户选项列表
 * 用于考勤人员选择
 */
export const getUserOptions = (params?: {
  keyword?: string
  status?: number
  page?: number
  per_page?: number
}): Promise<PaginatedResponse<UserOption>> => {
  return request.get<PaginatedResponse<UserOption>>({
    url: '/api/admin/users',
    params: {
      ...params,
      per_page: params?.per_page || 100 // 默认获取100条
    }
  })
}

/**
 * 批量删除考勤配置
 * @param ids 考勤配置ID数组
 */
export const batchDeleteCheckinConfigs = (ids: number[]): Promise<{ message: string }> => {
  return request.post<{ message: string }>({
    url: '/api/admin/checkin-configs/batch/destroy',
    data: { ids }
  })
}

// ========== 工具函数 ==========

/**
 * 将时间字符串转换为Unix时间戳
 * @param timeStr 时间字符串，格式 HH:mm
 * @returns Unix时间戳
 */
export const timeToTimestamp = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  const date = new Date()
  date.setHours(hours, minutes, 0, 0)
  return Math.floor(date.getTime() / 1000)
}

/**
 * 将Unix时间戳转换为时间字符串
 * @param timestamp Unix时间戳
 * @returns 时间字符串，格式 HH:mm
 */
export const timestampToTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * 格式化日期时间戳
 * @param timestamp Unix时间戳
 * @returns 格式化的日期时间字符串
 */
export const formatDateTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 获取状态文本
 * @param status 状态值
 * @returns 状态文本
 */
export const getStatusText = (status: number): string => {
  return status === 1 ? '启用' : '禁用'
}

/**
 * 获取状态标签类型
 * @param status 状态值
 * @returns Element Plus Tag组件的type属性值
 */
export const getStatusType = (status: number): 'success' | 'danger' => {
  return status === 1 ? 'success' : 'danger'
}
