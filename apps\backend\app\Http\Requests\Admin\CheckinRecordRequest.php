<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CheckinRecordRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'checkin_config_id' => 'required|integer|exists:checkin_configs,id',
            'location' => 'nullable|string|max:255',
            'latitude' => 'required',
            'longitude' => 'required',
            'location_range' => 'nullable|integer|min:0',
            'attachment_id' => 'nullable|integer|exists:attachments,id',
            'content' => 'nullable|string|max:255',
            'ip_address' => 'nullable|string|max:255',
        ];
    }

    public function attributes()
    {
        return [
            'checkin_config_id' => '打卡配置',
            'location' => '打卡地点',
            'latitude' => '打卡位置经度',
            'longitude' => '打卡位置纬度',
            'location_range' => '打卡范围',
            'attachment_id' => '打卡照片',
            'content' => '打卡备注',
            'ip_address' => 'IP地址'
        ];
    }

    public function messages()
    {
        return [
            'checkin_config_id.required' => '打卡配置不能为空',
            'checkin_config_id.integer' => '打卡配置必须为整数',
            'checkin_config_id.exists' => '打卡配置不存在',
            'location.string' => '打卡地点必须为字符串',
            'location.max' => '打卡地点长度不能超过255个字符',
        ];
    }
}
