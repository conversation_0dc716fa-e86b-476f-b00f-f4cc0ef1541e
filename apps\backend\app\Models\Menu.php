<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int|null $parent_id 父级ID
 * @property string $name 路由名称（唯一）
 * @property string $path 路由路径
 * @property string|null $component 组件路径
 * @property string $title 菜单标题
 * @property string|null $icon 菜单图标
 * @property string|null $label 权限标识
 * @property int $sort 排序
 * @property bool $is_hide 是否隐藏
 * @property bool $is_hide_tab 是否在标签页隐藏
 * @property string|null $link 外部链接
 * @property bool $is_iframe 是否为iframe
 * @property bool $keep_alive 是否缓存
 * @property bool $is_first_level 是否为一级菜单
 * @property bool $fixed_tab 是否固定标签页
 * @property string|null $active_path 激活菜单路径
 * @property bool $is_full_page 是否全屏页面
 * @property bool $show_badge 是否显示徽章
 * @property string|null $show_text_badge 文本徽章内容
 * @property bool $status 状态：1启用 0禁用
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property-read array $meta
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MenuPermission> $permissions
 * @property-read int|null $permissions_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereActivePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereComponent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereFixedTab($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsFirstLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsFullPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsHide($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsHideTab($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsIframe($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereKeepAlive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereShowBadge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereShowTextBadge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Menu extends Model
{
    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    protected $fillable = [
        'parent_id',
        'name',
        'path',
        'component',
        'title',
        'icon',
        'label',
        'sort',
        'is_hide',
        'is_hide_tab',
        'link',
        'is_iframe',
        'keep_alive',
        'is_first_level',
        'fixed_tab',
        'active_path',
        'is_full_page',
        'show_badge',
        'show_text_badge',
        'status',
    ];

    protected $casts = [
        'is_hide' => 'boolean',
        'is_hide_tab' => 'boolean',
        'is_iframe' => 'boolean',
        'keep_alive' => 'boolean',
        'is_first_level' => 'boolean',
        'fixed_tab' => 'boolean',
        'is_full_page' => 'boolean',
        'show_badge' => 'boolean',
        'status' => 'boolean',
    ];

    // 默认加载权限关联
    protected $with = ['permissions'];

    // 追加 meta 属性
    protected $appends = ['meta'];

    /**
     * 关联权限
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(MenuPermission::class)->orderBy('sort', 'desc');
    }

    /**
     * 获取meta数据
     */
    public function getMetaAttribute(): array
    {
        return [
            'title' => $this->title,
            'icon' => $this->icon,
            'keepAlive' => $this->keep_alive,
            'showBadge' => $this->show_badge,
            'showTextBadge' => $this->show_text_badge,
            'isHide' => $this->is_hide,
            'isHideTab' => $this->is_hide_tab,
            'link' => $this->link,
            'isIframe' => $this->is_iframe,
            'authList' => $this->permissions->map(function ($permission) {
                return [
                    'title' => $permission->title,
                    'authMark' => $permission->auth_mark,
                ];
            })->toArray(),
            'isFirstLevel' => $this->is_first_level,
            'fixedTab' => $this->fixed_tab,
            'activePath' => $this->active_path,
            'isFullPage' => $this->is_full_page,
        ];
    }
}
