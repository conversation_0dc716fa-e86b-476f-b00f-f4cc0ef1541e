<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckinRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $data = [
            'id' => $this->id,
            'checkin_time' => $this->checkin_time,
            'status' => $this->status,
            'location' => $this->location,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'location_range' => $this->location_range,
            'ip_address' => $this->ip_address,
            'content' => $this->content,
            'attachment_id' => $this->attachment_id,
            'attachment' => $this->whenLoaded('attachment', function () {
                return $this->attachment ? [
                    'id' => $this->attachment->id,
                    'file_name' => $this->attachment->file_name,
                    'file_url' => $this->attachment->file_url,
                    'file_size' => $this->attachment->file_size,
                    'formatted_file_size' => $this->attachment->formatted_file_size,
                    'mime_type' => $this->attachment->mime_type,
                    'storage_type' => $this->attachment->storage_type,
                    'created_at' => $this->attachment->created_at,
                ] : null;
            }),
            'user' => $this->whenLoaded('user'),
            'config' => $this->whenLoaded('config'),
        ];

        return $data;
    }
}
