<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operation_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->string('user_name', 100)->nullable()->comment('用户名');
            $table->unsignedBigInteger('menu_id')->nullable()->comment('菜单ID');
            $table->string('menu_name', 100)->nullable()->comment('菜单名称');
            $table->string('operation_type', 50)->nullable()->comment('操作类型：create,update,delete,view,export,import等');
            $table->string('operation_description', 500)->nullable()->comment('操作描述');
            $table->string('target_type', 100)->nullable()->comment('操作目标类型：Asset,User,Menu等');
            $table->unsignedBigInteger('target_id')->nullable()->comment('操作目标ID');
            $table->string('target_name', 200)->nullable()->comment('操作目标名称');
            $table->string('method', 10)->comment('请求方法');
            $table->string('path', 500)->comment('请求路径');
            $table->string('ip', 45)->comment('IP地址');
            $table->json('headers')->nullable()->comment('请求头');
            $table->json('params')->nullable()->comment('请求参数');
            $table->string('user_agent', 500)->nullable()->comment('User-Agent');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引
            $table->index('user_id');
            $table->index('menu_id');
            $table->index('operation_type');
            $table->index('target_type');
            $table->index('target_id');
            $table->index('method');
            $table->index('path');
            $table->index('created_at');

            $table->comment('操作日志表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operation_logs');
    }
};
