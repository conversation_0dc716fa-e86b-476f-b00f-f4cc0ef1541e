<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AttachmentCategory;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\EntityBrandResource;
use App\Models\Entity;
use App\Models\EntityBrand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * @group 相关方管理
 */
class EntityBrandController extends Controller
{
    /**
     * 获取相关方的品牌列表
     *
     * @urlParam entity int required 相关方ID Example: 1
     *
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 10
     *
     * @apiResourceCollection App\Http\Resources\Admin\EntityBrandResource
     *
     * @apiResourceModel App\Models\EntityBrand paginate=10
     */
    public function index(Entity $entity, Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $brands = $entity->brands()
            ->with(['attachments' => function ($query) {
                $query->where('attachment_relations.category', AttachmentCategory::LOGO->value);
            }])
            ->orderBy('sort_order', 'desc')
            ->paginate($perPage);

        return EntityBrandResource::collection($brands);
    }

    /**
     * 创建品牌
     *
     * @urlParam entity int required 相关方ID Example: 1
     *
     * @bodyParam name string required 品牌名称 Example: 华为
     * @bodyParam description string 品牌描述 Example: 全球领先的信息与通信技术解决方案供应商
     * @bodyParam logo_id int 品牌Logo附件ID Example: 1
     * @bodyParam sort_order int 排序顺序 Example: 100
     */
    public function store(Entity $entity, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'logo_id' => 'nullable|integer|exists:attachments,id',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()->first()], 422);
        }

        $brand = $entity->brands()->create([
            'name' => $request->name,
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? 0,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);

        // 处理Logo附件关联
        if ($request->logo_id) {
            $brand->attachments()->attach($request->logo_id, [
                'category' => AttachmentCategory::LOGO->value,
                'created_at' => time(),
                'updated_at' => time(),
            ]);
        }

        // 重新加载品牌数据以包含附件信息
        $brand->load(['attachments' => function ($query) {
            $query->where('attachment_relations.category', AttachmentCategory::LOGO->value);
        }]);

        return (new EntityBrandResource($brand))->response()->setStatusCode(201);
    }

    /**
     * 更新品牌
     *
     * @urlParam entity int required 相关方ID Example: 1
     * @urlParam brand int required 品牌ID Example: 1
     *
     * @bodyParam name string required 品牌名称 Example: 华为
     * @bodyParam description string 品牌描述 Example: 全球领先的信息与通信技术解决方案供应商
     * @bodyParam logo_id int 品牌Logo附件ID Example: 1
     * @bodyParam sort_order int 排序顺序 Example: 100
     */
    public function update(Entity $entity, EntityBrand $brand, Request $request)
    {
        // 验证品牌是否属于该相关方
        if ($brand->entity_id !== $entity->id) {
            return response()->json(['message' => '品牌不属于该相关方'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'logo_id' => 'nullable|integer|exists:attachments,id',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()->first()], 422);
        }

        $brand->update([
            'name' => $request->name,
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? $brand->sort_order,
            'updated_by' => auth()->id(),
        ]);

        // 处理Logo附件关联
        // 先删除现有的Logo附件关联
        $brand->attachments()->wherePivot('category', AttachmentCategory::LOGO->value)->detach();

        // 添加新的Logo附件关联
        if ($request->logo_id) {
            $brand->attachments()->attach($request->logo_id, [
                'category' => AttachmentCategory::LOGO->value,
                'created_at' => time(),
                'updated_at' => time(),
            ]);
        }

        // 重新加载品牌数据以包含附件信息
        $brand->load(['attachments' => function ($query) {
            $query->where('attachment_relations.category', AttachmentCategory::LOGO->value);
        }]);

        return new EntityBrandResource($brand);
    }

    /**
     * 删除品牌
     *
     * @urlParam entity int required 相关方ID Example: 1
     * @urlParam brand int required 品牌ID Example: 1
     */
    public function destroy(Entity $entity, EntityBrand $brand)
    {
        // 验证品牌是否属于该相关方
        if ($brand->entity_id !== $entity->id) {
            return response()->json(['message' => '品牌不属于该相关方'], 403);
        }

        // 删除附件关联
        $brand->attachments()->detach();

        $brand->delete();

        return response()->noContent();
    }
}
