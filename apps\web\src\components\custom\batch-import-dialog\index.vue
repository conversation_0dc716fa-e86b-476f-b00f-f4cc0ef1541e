<template>
  <ElDialog
    v-model="visible"
    :title="dialogTitle"
    :width="dialogWidth"
    :close-on-click-modal="false"
    @close="handleClose"
    align-center
  >
    <div class="import-container">
      <!-- 步骤指引 -->
      <div class="steps-wrapper">
        <ElSteps :active="currentStep" finish-status="success" process-status="process">
          <ElStep title="下载模板" description="获取标准导入模板" />
          <ElStep title="上传文件" description="上传填写好的文件" />
          <ElStep title="导入结果" description="查看处理结果" />
        </ElSteps>
      </div>

      <!-- 步骤1: 下载模板 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="action-center">
          <ElButton
            type="primary"
            size="large"
            :loading="downloadLoading"
            @click="downloadTemplate"
          >
            <ElIcon :size="16"><Download /></ElIcon>
            下载Excel模板
          </ElButton>
        </div>
        <p class="step-description">
          下载包含字段说明和示例数据的Excel模板文件，按格式填写{{
            entityNameComputed
          }}信息后保存为Excel格式
        </p>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <AttachmentUpload
          v-model="uploadedAttachments"
          :multiple="false"
          :limit="1"
          :accept="fileAccept"
          button-text="点击选择文件"
          tip-text="或拖拽Excel文件到此区域"
          :drag="true"
        />

        <div v-if="uploadedAttachments.length > 0" class="file-info">
          <ElAlert type="success" :closable="false">
            <template #title>
              <ElSpace>
                <ElIcon :size="16"><DocumentChecked /></ElIcon>
                <span>文件上传成功，可以开始导入</span>
              </ElSpace>
            </template>
          </ElAlert>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <!-- 状态结果 -->
        <ElResult
          :icon="getResultIcon()"
          :title="getResultTitle()"
          :sub-title="getResultSubTitle()"
        />

        <!-- 数据统计 -->
        <div class="statistics-section">
          <div class="statistics-header">
            <h4>导入统计</h4>
            <ElTag v-if="importTask">{{ importTask.original_filename }}</ElTag>
          </div>

          <div class="statistics-flex">
            <div class="stat-card">
              <div class="stat-number">{{ importTask?.total_rows || 0 }}</div>
              <div class="stat-label">总行数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number stat-success">{{ importTask?.success_rows || 0 }}</div>
              <div class="stat-label">成功</div>
            </div>
            <div class="stat-card">
              <div class="stat-number stat-error">{{ importTask?.failed_rows || 0 }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>

          <div v-if="importTask && importTask.total_rows > 0" class="progress-section">
            <ElProgress
              :percentage="getProgressPercentage()"
              :status="getProgressStatus()"
              :stroke-width="8"
              :show-text="true"
            />
          </div>
        </div>

        <!-- 错误详情 -->
        <div v-if="hasErrors" class="error-section">
          <h4 class="error-title">
            <ElIcon :size="16"><Warning /></ElIcon>
            错误详情
          </h4>

          <ElTable :data="importTask?.error_details" border size="small" max-height="300">
            <ElTableColumn prop="row" label="行号" width="80" align="center" />
            <ElTableColumn prop="error" label="错误信息" min-width="200" />
            <ElTableColumn label="原始数据" min-width="300">
              <template #default="{ row }">
                <ElText type="info" size="small">
                  {{ Array.isArray(row.data) ? row.data.join(' | ') : row.data }}
                </ElText>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
      </div>
    </div>

    <template #footer>
      <ElSpace>
        <ElButton @click="handleClose"> 取消 </ElButton>
        <ElButton v-if="currentStep > 0 && currentStep < 2" @click="currentStep--">
          上一步
        </ElButton>
        <ElButton v-if="currentStep === 0" type="primary" @click="currentStep++"> 下一步 </ElButton>
        <ElButton
          v-if="currentStep === 1 && uploadedAttachments.length > 0"
          type="primary"
          :loading="importLoading"
          @click="startImport"
        >
          开始导入
        </ElButton>
        <ElButton
          v-if="currentStep === 2 && importTask?.status === 'completed'"
          type="primary"
          @click="handleImportComplete"
        >
          完成
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import {
    ElDialog,
    ElSteps,
    ElStep,
    ElSpace,
    ElAlert,
    ElButton,
    ElIcon,
    ElTag,
    ElProgress,
    ElTable,
    ElTableColumn,
    ElResult,
    ElText,
    ElMessage
  } from 'element-plus'
  import {
    Download,
    Warning,
    DocumentChecked,
    CircleCheck,
    CircleClose,
    Loading,
    InfoFilled
  } from '@element-plus/icons-vue'
  import { AttachmentUpload } from '@/components/custom/upload'
  import {
    exportTemplate,
    importData,
    getImportTaskStatus,
    generateFilename,
    downloadBlob,
    type ImportTask,
    type ImportExportType
  } from '@/api/admin/import-export'

  /**
   * 组件 Props 接口
   */
  export interface BatchImportDialogProps {
    visible: boolean // 弹窗显示状态
    title?: string // 弹窗标题（默认："{entityName}批量导入"）
    entityName?: string // 实体名称（如："资产"、"用户"、"相关方"）- 如果提供了type，可以自动推断
    type?: ImportExportType // 导入类型（新增：支持直接传入类型）

    // 兼容旧版本的 API 函数（可选，优先使用type）
    exportTemplateApi?: () => Promise<Blob>
    importApi?: (attachmentId: number) => Promise<{ message: string; task_id: number }>
    getTaskStatusApi?: (taskId: number) => Promise<ImportTask>

    // 可选配置
    pollingInterval?: number // 轮询间隔（默认：2000ms）
    fileAccept?: string // 文件类型（默认：".xlsx,.xls"）
    dialogWidth?: string // 弹窗宽度（默认："700px"）
    templateParams?: Record<string, any> // 模板额外参数（如category的with_sample）
  }

  interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'import-success'): void
  }

  const props = withDefaults(defineProps<BatchImportDialogProps>(), {
    title: undefined,
    entityName: undefined,
    type: undefined,
    pollingInterval: 2000,
    fileAccept: '.xlsx,.xls',
    dialogWidth: '700px'
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const currentStep = ref(0)
  const downloadLoading = ref(false)
  const importLoading = ref(false)
  const uploadedAttachments = ref<number[]>([])
  const importTask = ref<ImportTask | null>(null)
  const pollingTimer = ref<number | null>(null)

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 实体名称（优先从type推断，其次使用传入的entityName）
  const entityNameComputed = computed(() => {
    if (props.entityName) return props.entityName

    const typeNames: Record<ImportExportType, string> = {
      asset: '资产',
      category: '分类',
      entity: '相关方',
      user: '用户'
    }

    return props.type ? typeNames[props.type] : '数据'
  })

  // 弹窗标题
  const dialogTitle = computed(() => {
    return props.title || `${entityNameComputed.value}批量导入`
  })

  // 错误详情计算属性
  const hasErrors = computed(() => {
    return importTask.value?.error_details && importTask.value.error_details.length > 0
  })

  // 监听弹窗关闭
  watch(
    () => props.visible,
    (newVal) => {
      if (!newVal) {
        resetDialog()
      }
    }
  )

  // 重置对话框
  const resetDialog = () => {
    currentStep.value = 0
    uploadedAttachments.value = []
    importTask.value = null
    if (pollingTimer.value) {
      clearInterval(pollingTimer.value)
      pollingTimer.value = null
    }
  }

  // 下载模板
  const downloadTemplate = async () => {
    try {
      downloadLoading.value = true
      let blob: Blob

      // 优先使用type调用统一接口，否则使用传入的API函数
      if (props.type) {
        blob = await exportTemplate(props.type, props.templateParams)
      } else if (props.exportTemplateApi) {
        blob = await props.exportTemplateApi()
      } else {
        throw new Error('未提供导出模板的方式')
      }

      // 生成文件名并下载
      const filename = props.type
        ? generateFilename(props.type, '模板')
        : `${entityNameComputed.value}导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`

      downloadBlob(blob, filename)

      ElMessage.success('模板下载成功')
      currentStep.value = 1
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败，请重试')
    } finally {
      downloadLoading.value = false
    }
  }

  // 开始导入
  const startImport = async () => {
    if (uploadedAttachments.value.length === 0) {
      ElMessage.warning('请先上传Excel文件')
      return
    }

    try {
      importLoading.value = true
      const attachmentId = uploadedAttachments.value[0]
      let result: { message: string; task_id: number }

      // 优先使用type调用统一接口，否则使用传入的API函数
      if (props.type) {
        result = await importData(props.type, attachmentId)
      } else if (props.importApi) {
        result = await props.importApi(attachmentId)
      } else {
        throw new Error('未提供导入数据的方式')
      }

      ElMessage.success(result.message)
      currentStep.value = 2

      // 开始轮询任务状态
      startPolling(result.task_id)
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请重试')
    } finally {
      importLoading.value = false
    }
  }

  // 开始轮询任务状态
  const startPolling = (taskId: number) => {
    const checkStatus = async () => {
      try {
        let task: ImportTask

        // 优先使用type调用统一接口，否则使用传入的API函数
        if (props.type) {
          task = await getImportTaskStatus(props.type, taskId)
        } else if (props.getTaskStatusApi) {
          task = await props.getTaskStatusApi(taskId)
        } else {
          throw new Error('未提供查询任务状态的方式')
        }

        importTask.value = task

        // 如果任务完成，停止轮询
        if (task.status === 'completed' || task.status === 'failed') {
          if (pollingTimer.value) {
            clearInterval(pollingTimer.value)
            pollingTimer.value = null
          }

          if (task.status === 'completed') {
            ElMessage.success('导入完成！')
          } else {
            ElMessage.error('导入失败，请检查错误详情')
          }
        }
      } catch (error) {
        console.error('查询任务状态失败:', error)
      }
    }

    // 立即检查一次
    checkStatus()

    // 按配置的间隔检查
    pollingTimer.value = window.setInterval(checkStatus, props.pollingInterval)
  }

  // 获取进度百分比
  const getProgressPercentage = () => {
    if (!importTask.value || importTask.value.total_rows === 0) return 0

    const processedRows = importTask.value.success_rows + importTask.value.failed_rows
    return Math.round((processedRows / importTask.value.total_rows) * 100)
  }

  // 获取进度条状态
  const getProgressStatus = () => {
    if (!importTask.value) return undefined

    switch (importTask.value.status) {
      case 'completed':
        return importTask.value.failed_rows > 0 ? 'warning' : 'success'
      case 'failed':
        return 'exception'
      default:
        return undefined
    }
  }

  // 获取结果图标
  const getResultIcon = () => {
    if (!importTask.value) return Loading

    switch (importTask.value.status) {
      case 'completed':
        return importTask.value.failed_rows > 0 ? Warning : CircleCheck
      case 'failed':
        return CircleClose
      case 'processing':
        return Loading
      default:
        return InfoFilled
    }
  }

  // 获取结果标题
  const getResultTitle = () => {
    if (!importTask.value) return '处理中...'

    switch (importTask.value.status) {
      case 'completed':
        if (importTask.value.failed_rows > 0) {
          return '导入完成，部分数据存在问题'
        }
        return '导入成功完成'
      case 'failed':
        return '导入失败'
      case 'processing':
        return '正在处理导入任务'
      case 'pending':
        return '等待处理'
      default:
        return '未知状态'
    }
  }

  // 获取结果副标题
  const getResultSubTitle = () => {
    if (!importTask.value) return '请稍候...'

    const { total_rows, success_rows, failed_rows } = importTask.value

    switch (importTask.value.status) {
      case 'completed':
        if (failed_rows > 0) {
          return `共 ${total_rows} 行数据，成功 ${success_rows} 行，失败 ${failed_rows} 行`
        }
        return `成功导入 ${success_rows} 条${entityNameComputed.value}数据`
      case 'failed':
        return '请检查文件格式或联系管理员'
      case 'processing':
        return `正在处理第 ${success_rows + failed_rows}/${total_rows} 行数据`
      case 'pending':
        return '任务已加入队列，即将开始处理'
      default:
        return ''
    }
  }

  // 导入完成处理
  const handleImportComplete = () => {
    emit('import-success')
    handleClose()
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
  }
</script>

<style lang="scss" scoped>
  // 主容器
  .import-container {
    padding: 0;
  }

  // 步骤指引容器
  .steps-wrapper {
    padding: 20px;
    margin-bottom: 24px;
    background: #f8faff;
    border-radius: 8px;
  }

  // 步骤内容
  .step-content {
    min-height: 200px;
    padding: 20px 0;
  }

  // 操作按钮居中
  .action-center {
    margin-bottom: 16px;
    text-align: center;
  }

  .step-description {
    margin: 16px 0 0;
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    text-align: center;
  }

  // 文件信息
  .file-info {
    margin-top: 16px;
  }

  // 统计区域
  .statistics-section {
    margin: 24px 0;

    .statistics-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .statistics-flex {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
  }

  .stat-card {
    flex: 1;
    padding: 16px;
    text-align: center;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  .stat-number {
    margin-bottom: 4px;
    font-size: 20px;
    font-weight: 600;
  }

  .stat-success {
    color: #52c41a;
  }

  .stat-error {
    color: #ff4d4f;
  }

  .stat-label {
    font-size: 12px;
    color: #666;
  }

  .progress-section {
    margin-top: 16px;
  }

  // 错误区域
  .error-section {
    margin-top: 24px;

    .error-title {
      display: flex;
      gap: 8px;
      align-items: center;
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #ff4d4f;
    }
  }

  // 拖拽上传区域图标样式覆盖
  :deep(.el-upload-dragger) {
    height: 120px;

    .el-icon {
      margin-bottom: 0 !important;
      font-size: 14px !important;
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .statistics-flex {
      flex-direction: column;
      gap: 12px;
    }
  }
</style>
