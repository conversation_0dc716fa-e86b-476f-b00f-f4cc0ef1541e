<?php

namespace Tests\Unit;

use App\Http\Resources\ImportTaskResource;
use App\Models\ImportTask;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class ImportTaskResourceTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_transforms_import_task_correctly()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
        ]);

        $task = ImportTask::factory()->create([
            'id' => 1,
            'type' => 'asset',
            'file_path' => 'imports/test.xlsx',
            'original_filename' => 'test.xlsx',
            'status' => ImportTask::STATUS_COMPLETED,
            'total_rows' => 100,
            'success_rows' => 95,
            'failed_rows' => 5,
            'error_details' => [
                ['row' => 10, 'error' => 'Test error'],
            ],
            'summary' => 'Import completed successfully',
            'created_by' => $user->id,
            'started_at' => 1640995200, // 2022-01-01 00:00:00
            'completed_at' => 1640998800, // 2022-01-01 01:00:00
            'created_at' => 1640995200,
            'updated_at' => 1640998800,
        ]);

        $task->load('creator');

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals([
            'id' => 1,
            'type' => 'asset',
            'type_text' => '资产',
            'file_path' => 'imports/test.xlsx',
            'original_filename' => 'test.xlsx',
            'status' => ImportTask::STATUS_COMPLETED,
            'status_text' => '已完成',
            'total_rows' => 100,
            'success_rows' => 95,
            'failed_rows' => 5,
            'progress_percent' => 100,
            'error_details' => [
                ['row' => 10, 'error' => 'Test error'],
            ],
            'summary' => 'Import completed successfully',
            'created_by' => $user->id,
            'started_at' => '2022-01-01 00:00:00',
            'completed_at' => '2022-01-01 01:00:00',
            'created_at' => '2022-01-01 00:00:00',
            'updated_at' => '2022-01-01 01:00:00',
            'creator' => [
                'id' => $user->id,
                'name' => 'Test User',
            ],
            'duration' => 3600, // 1 hour
            'error_count' => 1,
            'has_errors' => true,
        ], $array);
    }

    /** @test */
    public function it_handles_pending_task()
    {
        $task = ImportTask::factory()->pending()->create([
            'type' => 'category',
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals('category', $array['type']);
        $this->assertEquals('分类', $array['type_text']);
        $this->assertEquals(ImportTask::STATUS_PENDING, $array['status']);
        $this->assertEquals('等待处理', $array['status_text']);
        $this->assertEquals(0, $array['progress_percent']);
        $this->assertNull($array['started_at']);
        $this->assertNull($array['completed_at']);
        $this->assertNull($array['duration']);
        $this->assertEquals(0, $array['error_count']);
        $this->assertFalse($array['has_errors']);
    }

    /** @test */
    public function it_handles_processing_task()
    {
        $task = ImportTask::factory()->processing()->create([
            'type' => 'entity',
            'total_rows' => 200,
            'success_rows' => 150,
            'failed_rows' => 10,
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals('entity', $array['type']);
        $this->assertEquals('相关方', $array['type_text']);
        $this->assertEquals(ImportTask::STATUS_PROCESSING, $array['status']);
        $this->assertEquals('处理中', $array['status_text']);
        $this->assertEquals(80, $array['progress_percent']); // (150 + 10) / 200 * 100
        $this->assertNotNull($array['started_at']);
        $this->assertNull($array['completed_at']);
        $this->assertNotNull($array['duration']);
        $this->assertTrue($array['has_errors']); // has failed_rows > 0
    }

    /** @test */
    public function it_handles_failed_task()
    {
        $task = ImportTask::factory()->failed()->create([
            'type' => 'user',
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals('user', $array['type']);
        $this->assertEquals('用户', $array['type_text']);
        $this->assertEquals(ImportTask::STATUS_FAILED, $array['status']);
        $this->assertEquals('失败', $array['status_text']);
        $this->assertTrue($array['has_errors']);
    }

    /** @test */
    public function it_calculates_progress_percent_correctly()
    {
        // Test with zero total rows
        $task = ImportTask::factory()->create([
            'total_rows' => 0,
            'success_rows' => 0,
            'failed_rows' => 0,
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        $array = $resource->toArray($request);

        $this->assertEquals(0, $array['progress_percent']);

        // Test with partial progress
        $task = ImportTask::factory()->create([
            'total_rows' => 100,
            'success_rows' => 30,
            'failed_rows' => 20,
        ]);

        $resource = new ImportTaskResource($task);
        $array = $resource->toArray($request);

        $this->assertEquals(50, $array['progress_percent']); // (30 + 20) / 100 * 100
    }

    /** @test */
    public function it_handles_unknown_type()
    {
        $task = ImportTask::factory()->create([
            'type' => 'unknown',
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals('unknown', $array['type']);
        $this->assertEquals('未知类型', $array['type_text']);
    }

    /** @test */
    public function it_handles_unknown_status()
    {
        $task = ImportTask::factory()->create([
            'status' => 'unknown',
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertEquals('unknown', $array['status']);
        $this->assertEquals('未知状态', $array['status_text']);
    }

    /** @test */
    public function it_handles_task_without_creator()
    {
        $task = ImportTask::factory()->create([
            'created_by' => null,
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        
        $array = $resource->toArray($request);

        $this->assertNull($array['creator']);
    }

    /** @test */
    public function it_calculates_error_count_correctly()
    {
        // Test with no errors
        $task = ImportTask::factory()->create([
            'error_details' => null,
        ]);

        $resource = new ImportTaskResource($task);
        $request = Request::create('/');
        $array = $resource->toArray($request);

        $this->assertEquals(0, $array['error_count']);

        // Test with multiple errors
        $task = ImportTask::factory()->create([
            'error_details' => [
                ['row' => 1, 'error' => 'Error 1'],
                ['row' => 2, 'error' => 'Error 2'],
                ['row' => 3, 'error' => 'Error 3'],
            ],
        ]);

        $resource = new ImportTaskResource($task);
        $array = $resource->toArray($request);

        $this->assertEquals(3, $array['error_count']);
    }
}
