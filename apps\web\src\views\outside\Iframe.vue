<template>
  <div class="iframe-container" v-loading="isLoading">
    <iframe
      ref="iframeRef"
      :src="iframeUrl"
      frameborder="0"
      class="iframe-content"
      @load="handleIframeLoad"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
  import { getIframeRoutes } from '@/router/utils/menuToRouter'

  const route = useRoute()
  const isLoading = ref(true)
  const iframeUrl = ref('')
  const iframeRef = ref<HTMLIFrameElement | null>(null)

  onMounted(() => {
    // 从URL路径提取菜单名
    const pathParts = route.path.split('/')
    const menuName = pathParts[pathParts.length - 1]

    // 获取所有iframe路由
    const allIframeRoutes = getIframeRoutes()

    // 通过菜单名查找对应的iframe路由
    const iframeRoute = allIframeRoutes.find((item: any) => item.name === menuName)

    if (iframeRoute?.meta?.link) {
      iframeUrl.value = iframeRoute.meta.link
    }
  })

  const handleIframeLoad = () => {
    isLoading.value = false
  }
</script>

<style scoped>
  .iframe-container {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
  }

  .iframe-content {
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 120px);
    border: none;
  }
</style>
