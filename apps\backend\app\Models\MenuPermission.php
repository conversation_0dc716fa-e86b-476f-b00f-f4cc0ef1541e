<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $menu_id 菜单ID
 * @property string $title 权限名称
 * @property string $auth_mark 权限标识
 * @property string $route_name 路由标识
 * @property int $sort 排序
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property-read \App\Models\Menu|null $menu
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereAuthMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereRouteName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class MenuPermission extends Model
{
    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    protected $fillable = ['menu_id', 'title', 'auth_mark', 'route_name', 'sort'];

    /**
     * 关联菜单
     */
    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }
}
