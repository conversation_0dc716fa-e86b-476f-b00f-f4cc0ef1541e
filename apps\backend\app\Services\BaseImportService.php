<?php

namespace App\Services;

use App\Models\ImportTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

/**
 * 导入服务基类
 * 定义通用的导入逻辑和接口
 */
abstract class BaseImportService
{
    /**
     * 批处理大小
     */
    protected int $batchSize = 100;

    /**
     * 最大错误数量，超过此数量将停止导入
     */
    protected int $maxErrors = 1000;

    /**
     * 处理导入任务
     */
    public function processImport(ImportTask $importTask): array
    {
        $startTime = microtime(true);

        try {
            Log::info("开始处理{$importTask->type}导入任务", [
                'task_id' => $importTask->id,
                'file_path' => $importTask->file_path,
                'original_filename' => $importTask->original_filename,
                'created_by' => $importTask->created_by,
            ]);

            // 标记任务为处理中
            $importTask->markAsProcessing();

            // 验证文件存在性
            $this->validateFileExists($importTask->file_path);

            // 读取Excel文件
            $data = $this->readExcelFile($importTask->file_path);

            if (empty($data)) {
                throw new \Exception('Excel文件为空或无法读取');
            }

            Log::info("成功读取Excel文件", [
                'task_id' => $importTask->id,
                'total_rows' => count($data),
            ]);

            // 验证表头
            $headers = array_keys($data[0]);
            $this->validateHeaders($headers);

            // 处理数据
            $result = $this->processData($data, $importTask);

            // 计算处理时长
            $duration = round(microtime(true) - $startTime, 2);
            $result['duration'] = $duration;

            // 标记任务完成
            $importTask->markAsCompleted($result);

            Log::info("完成{$importTask->type}导入任务", [
                'task_id' => $importTask->id,
                'total_rows' => $result['total_rows'],
                'success_rows' => $result['success_rows'],
                'failed_rows' => $result['failed_rows'],
                'duration' => $duration . 's',
                'success_rate' => $result['total_rows'] > 0 ? round(($result['success_rows'] / $result['total_rows']) * 100, 2) . '%' : '0%',
            ]);

            return $result;

        } catch (\Exception $e) {
            $duration = round(microtime(true) - $startTime, 2);

            Log::error("处理{$importTask->type}导入任务失败", [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
                'duration' => $duration . 's',
                'file_path' => $importTask->file_path,
                'trace' => $e->getTraceAsString(),
            ]);

            // 构建详细的错误信息
            $errorDetails = [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'duration' => $duration,
                'timestamp' => date('Y-m-d H:i:s'),
            ];

            $importTask->markAsFailed($errorDetails);
            throw $e;
        }
    }

    /**
     * 验证文件是否存在
     */
    protected function validateFileExists(string $filePath): void
    {
        $fullPath = storage_path('app/' . $filePath);

        if (!file_exists($fullPath)) {
            throw new \Exception("导入文件不存在: {$filePath}");
        }

        if (!is_readable($fullPath)) {
            throw new \Exception("导入文件无法读取: {$filePath}");
        }

        // 检查文件大小
        $fileSize = filesize($fullPath);
        $maxSize = 50 * 1024 * 1024; // 50MB

        if ($fileSize > $maxSize) {
            throw new \Exception("导入文件过大，最大支持50MB，当前文件大小: " . round($fileSize / 1024 / 1024, 2) . "MB");
        }

        Log::debug("文件验证通过", [
            'file_path' => $filePath,
            'file_size' => round($fileSize / 1024, 2) . 'KB',
        ]);
    }

    /**
     * 读取Excel文件
     */
    protected function readExcelFile(string $filePath): array
    {
        // 设置表头格式化器为不格式化，保持原始表头
        HeadingRowFormatter::default('none');

        try {
            $data = Excel::toArray([], storage_path('app/' . $filePath));

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空');
            }

            $rows = $data[0];
            if (count($rows) < 2) {
                throw new \Exception('Excel文件至少需要包含表头和一行数据');
            }

            // 第一行作为表头
            $headers = array_shift($rows);

            // 过滤空行
            $rows = array_filter($rows, function ($row) {
                return !empty(array_filter($row, function ($cell) {
                    return !is_null($cell) && $cell !== '';
                }));
            });

            // 将数据转换为关联数组
            $result = [];
            foreach ($rows as $row) {
                $rowData = [];
                foreach ($headers as $index => $header) {
                    $rowData[$header] = $row[$index] ?? null;
                }
                $result[] = $rowData;
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('读取Excel文件失败', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('读取Excel文件失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理数据
     */
    protected function processData(array $data, ImportTask $importTask): array
    {
        $totalRows = count($data);
        $successRows = 0;
        $failedRows = 0;
        $errors = [];
        $currentBatch = [];
        $rowNumber = 2; // Excel行号从2开始（第1行是表头）

        foreach ($data as $rowData) {
            try {
                // 验证行数据
                $validatedData = $this->validateRowData($rowData, $rowNumber);

                // 转换数据
                $transformedData = $this->transformRowData($validatedData, $rowNumber);

                $currentBatch[] = [
                    'data' => $transformedData,
                    'row_number' => $rowNumber,
                ];

                // 达到批处理大小时处理批次
                if (count($currentBatch) >= $this->batchSize) {
                    $batchResult = $this->processBatch($currentBatch, $importTask);
                    $successRows += $batchResult['success'];
                    $failedRows += $batchResult['failed'];
                    $errors = array_merge($errors, $batchResult['errors']);
                    $currentBatch = [];

                    // 更新进度
                    $importTask->updateProgress($totalRows, $successRows, $failedRows);
                }

            } catch (\Exception $e) {
                $failedRows++;
                $errors[] = [
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                    'data' => $rowData,
                ];

                // 如果错误太多，停止导入
                if (count($errors) >= $this->maxErrors) {
                    Log::warning('导入错误过多，停止处理', [
                        'task_id' => $importTask->id,
                        'error_count' => count($errors),
                        'max_errors' => $this->maxErrors,
                    ]);
                    break;
                }
            }

            $rowNumber++;
        }

        // 处理剩余的批次
        if (!empty($currentBatch)) {
            $batchResult = $this->processBatch($currentBatch, $importTask);
            $successRows += $batchResult['success'];
            $failedRows += $batchResult['failed'];
            $errors = array_merge($errors, $batchResult['errors']);
        }

        return [
            'total_rows' => $totalRows,
            'success_rows' => $successRows,
            'failed_rows' => $failedRows,
            'errors' => $errors,
            'summary' => $this->generateSummary($totalRows, $successRows, $failedRows),
        ];
    }

    /**
     * 处理批次数据
     */
    protected function processBatch(array $batch, ImportTask $importTask): array
    {
        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        $batchStartRow = $batch[0]['row_number'] ?? 0;
        $batchEndRow = end($batch)['row_number'] ?? 0;

        Log::debug("开始处理批次", [
            'task_id' => $importTask->id,
            'batch_size' => count($batch),
            'start_row' => $batchStartRow,
            'end_row' => $batchEndRow,
        ]);

        DB::beginTransaction();
        try {
            foreach ($batch as $item) {
                try {
                    $this->createRecord($item['data']);
                    $successCount++;

                    Log::debug("记录创建成功", [
                        'task_id' => $importTask->id,
                        'row' => $item['row_number'],
                    ]);

                } catch (\Exception $e) {
                    $failedCount++;
                    $errorInfo = [
                        'row' => $item['row_number'],
                        'error' => $e->getMessage(),
                        'data' => $item['data'],
                        'exception_type' => get_class($e),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ];

                    $errors[] = $errorInfo;

                    Log::warning("记录创建失败", [
                        'task_id' => $importTask->id,
                        'row' => $item['row_number'],
                        'error' => $e->getMessage(),
                        'exception_type' => get_class($e),
                    ]);
                }
            }

            DB::commit();

            Log::info("批次处理完成", [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'start_row' => $batchStartRow,
                'end_row' => $batchEndRow,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error("批次事务失败，回滚所有更改", [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'error' => $e->getMessage(),
                'start_row' => $batchStartRow,
                'end_row' => $batchEndRow,
            ]);

            // 如果整个批次失败，标记所有记录为失败
            $failedCount = count($batch);
            $successCount = 0;
            $errors = [];

            foreach ($batch as $item) {
                $errors[] = [
                    'row' => $item['row_number'],
                    'error' => '批次处理失败: ' . $e->getMessage(),
                    'data' => $item['data'],
                    'exception_type' => get_class($e),
                ];
            }
        }

        return [
            'success' => $successCount,
            'failed' => $failedCount,
            'errors' => $errors,
        ];
    }

    /**
     * 生成导入摘要
     */
    protected function generateSummary(int $totalRows, int $successRows, int $failedRows): string
    {
        $successRate = $totalRows > 0 ? round(($successRows / $totalRows) * 100, 2) : 0;

        return "导入完成：总计 {$totalRows} 条记录，成功 {$successRows} 条，失败 {$failedRows} 条，成功率 {$successRate}%";
    }

    /**
     * 验证表头（子类实现）
     */
    abstract protected function validateHeaders(array $headers): void;

    /**
     * 验证行数据（子类实现）
     */
    abstract protected function validateRowData(array $rowData, int $rowNumber): array;

    /**
     * 转换行数据（子类实现）
     */
    abstract protected function transformRowData(array $rowData, int $rowNumber): array;

    /**
     * 创建记录（子类实现）
     */
    abstract protected function createRecord(array $data): void;
}
