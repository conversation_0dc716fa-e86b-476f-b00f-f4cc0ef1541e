<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`为用户「${userName}」分配角色`"
    width="900px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div v-loading="loading">
      <div class="tip-text">
        <el-icon><InfoFilled /></el-icon>
        操作后自动保存，无需手动确认
      </div>
      <ElTransfer
        v-model="selectedRoleIds"
        :data="transferData"
        :titles="['可分配角色', '已分配角色']"
        :button-texts="['移除', '分配']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}'
        }"
        filterable
        filter-placeholder="搜索角色"
        @change="handleRoleChange"
        class="role-transfer"
        :loading="submitLoading"
      >
        <template #default="{ option }">
          <div class="role-item">
            <div class="role-name">{{ option.label }}</div>
            <div v-if="option.description" class="role-description">{{ option.description }}</div>
          </div>
        </template>
      </ElTransfer>
    </div>
    <template #footer>
      <ElButton @click="handleCancel">关闭</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'UserRoleDialog' })

  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { InfoFilled } from '@element-plus/icons-vue'
  import type { TransferKey, TransferDirection } from 'element-plus'
  import { getRoleList, syncUserRoles } from '@/api/admin/system'
  import type { RoleListItem } from '@/types/api/role'

  // Transfer 组件需要的数据结构
  interface TransferOption {
    key: number
    label: string
    description?: string
    disabled?: boolean
  }

  interface Props {
    modelValue: boolean
    userId: number | null
    userName: string
    currentRoleIds: number[]
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 数据状态
  const loading = ref(false)
  const submitLoading = ref(false)
  const roleList = ref<RoleListItem[]>([])
  const selectedRoleIds = ref<number[]>([])

  // Transfer 组件数据
  const transferData = computed<TransferOption[]>(() => {
    return roleList.value.map((role) => ({
      key: role.id,
      label: role.name,
      description: role.description || '暂无描述',
      disabled: false
    }))
  })

  // 监听对话框打开
  watch(
    () => props.modelValue,
    async (newVal) => {
      if (newVal && props.userId) {
        await loadRoles()
        // 设置当前用户已有的角色
        selectedRoleIds.value = [...props.currentRoleIds]
      }
    }
  )

  // 加载角色列表
  const loadRoles = async () => {
    loading.value = true
    const res = await getRoleList({ per_page: 100 })
    roleList.value = res.data
    loading.value = false
  }

  // 处理角色变更 - 实时同步
  const handleRoleChange = async (
    value: TransferKey[],
    direction: TransferDirection,
    movedKeys: TransferKey[]
  ) => {
    // 将 TransferKey 转换为 number 数组
    selectedRoleIds.value = value.map((key) => Number(key))
    console.log('角色变更:', { value, direction, movedKeys })

    // 立即同步到后端
    if (!props.userId) return

    try {
      // 显示加载状态
      submitLoading.value = true

      // 调用API同步角色
      await syncUserRoles(props.userId, {
        role_ids: selectedRoleIds.value
      })

      // 提示用户操作成功
      if (direction === 'right') {
        ElMessage.success(`已自动分配 ${movedKeys.length} 个角色`)
      } else {
        ElMessage.success(`已自动移除 ${movedKeys.length} 个角色`)
      }

      // 通知父组件刷新数据
      emit('success')
    } catch {
      ElMessage.error('角色同步失败，请重试')
      // 同步失败时恢复之前的状态
      selectedRoleIds.value = [...props.currentRoleIds]
    } finally {
      submitLoading.value = false
    }
  }

  // 取消
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 对话框关闭后重置
  const handleClosed = () => {
    selectedRoleIds.value = []
    roleList.value = []
  }
</script>

<style scoped>
  .tip-text {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #0288d1;
    background-color: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 6px;
  }

  .role-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    line-height: 1.5;
  }

  .role-name {
    margin-bottom: 2px;
    font-weight: 500;
    color: #303133;
  }

  .role-description {
    font-size: 12px;
    line-height: 1.4;
    color: #909399;
  }

  .role-transfer {
    min-height: 450px;
  }

  :deep(.el-transfer) {
    display: flex !important;
    flex-direction: row !important;
    gap: 20px !important;
    align-items: flex-start !important;
    justify-content: center !important;
  }

  :deep(.el-transfer-panel) {
    flex-shrink: 0;
    width: 320px;
    height: 450px;
  }

  :deep(.el-transfer-panel__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-transfer-panel__item) {
    padding: 8px 12px;
    margin-bottom: 0;
    border-bottom: 1px solid #f5f7fa;
  }

  :deep(.el-transfer-panel__item:hover) {
    background-color: #f5f7fa;
  }

  :deep(.el-transfer-panel__filter) {
    padding: 12px;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-transfer__buttons) {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    align-items: center !important;
    align-self: center !important;
    justify-content: center !important;
    padding: 0 15px !important;
  }

  :deep(.el-transfer__button) {
    display: block !important;
    min-width: 60px !important;
    margin: 0 0 10px !important;
  }
</style>
