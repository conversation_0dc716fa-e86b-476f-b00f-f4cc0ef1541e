<?php

namespace Database\Seeders;

use App\Models\Entity;
use App\Models\EntityBrand;
use App\Models\EntityContact;
use App\Models\Asset;
use App\Models\Tag;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MedicalDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "开始填充医疗测试数据...\n";

        // 1. 创建医疗相关方
        $this->createMedicalEntities();

        // 2. 创建维保标签
        $this->createMaintenanceTags();

        // 3. 创建医疗设备资产
        $this->createMedicalAssets();

        echo "医疗测试数据填充完成！\n";
    }

    /**
     * 创建医疗相关方及其品牌和联系人
     */
    private function createMedicalEntities(): void
    {
        echo "正在创建医疗相关方数据...\n";

        // 医疗设备供应商
        $suppliers = [
            ['name' => '通用电气医疗科技有限公司', 'type' => 'supplier', 'brands' => ['GE Healthcare', 'GE医疗']],
            ['name' => '西门子医疗设备有限公司', 'type' => 'supplier', 'brands' => ['Siemens Healthineers', 'Siemens医疗']],
            ['name' => '飞利浦医疗科技有限公司', 'type' => 'supplier', 'brands' => ['Philips Healthcare', 'Philips医疗']],
            ['name' => '深圳迈瑞生物医疗电子股份有限公司', 'type' => 'supplier', 'brands' => ['迈瑞医疗', 'Mindray']],
            ['name' => '上海联影医疗科技股份有限公司', 'type' => 'supplier', 'brands' => ['联影医疗', 'United Imaging']],
        ];

        // 医院机构（作为最终客户）
        $hospitals = [
            ['name' => '北京协和医院', 'type' => 'end_customer'],
            ['name' => '上海交通大学医学院附属瑞金医院', 'type' => 'end_customer'],
            ['name' => '四川大学华西医院', 'type' => 'end_customer'],
        ];

        // 维保服务商
        $serviceProviders = [
            ['name' => '北京康泰医疗设备维保有限公司', 'type' => 'service_provider'],
            ['name' => '上海华健医疗技术服务有限公司', 'type' => 'service_provider'],
        ];

        $allEntities = array_merge($suppliers, $hospitals, $serviceProviders);

        foreach ($allEntities as $entityData) {
            $entity = Entity::factory()->create([
                'name' => $entityData['name'],
                'entity_type' => $entityData['type'],
            ]);

            // 为供应商创建品牌
            if (isset($entityData['brands'])) {
                foreach ($entityData['brands'] as $brandName) {
                    EntityBrand::factory()->create([
                        'entity_id' => $entity->id,
                        'name' => $brandName,
                    ]);
                }
            }

            // 为每个相关方创建联系人
            EntityContact::factory()->count(rand(2, 4))->create([
                'entity_id' => $entity->id,
            ]);
        }

        echo "医疗相关方数据创建完成！\n";
    }

    /**
     * 创建维保相关标签
     */
    private function createMaintenanceTags(): void
    {
        echo "正在创建维保标签...\n";

        $tags = [
            // 状态类标签
            ['name' => '待巡查保养', 'category' => '状态'],
            ['name' => '待维修', 'category' => '状态'],
            ['name' => '维修中', 'category' => '状态'],
            ['name' => '已维修', 'category' => '状态'],
            ['name' => '已到场确认', 'category' => '状态'],
            ['name' => '正常运行', 'category' => '状态'],
            ['name' => '故障停机', 'category' => '状态'],
            ['name' => '备用待命', 'category' => '状态'],

            // 任务类标签
            ['name' => '设备巡检', 'category' => '任务'],
            ['name' => '设备维保', 'category' => '任务'],
            ['name' => '设备质控', 'category' => '任务'],
            ['name' => '设备盘点', 'category' => '任务'],
            ['name' => '预防性维护', 'category' => '任务'],
            ['name' => '校准检测', 'category' => '任务'],
            ['name' => '计量检定', 'category' => '任务'],

            // 优先级标签
            ['name' => '紧急维修', 'category' => '优先级'],
            ['name' => '常规保养', 'category' => '优先级'],
            ['name' => '年度质控', 'category' => '优先级'],

            // 操作类标签
            ['name' => '安全检查', 'category' => '操作'],
            ['name' => '性能测试', 'category' => '操作'],
        ];

        foreach ($tags as $tagData) {
            Tag::factory()->create($tagData);
        }

        echo "维保标签创建完成！\n";
    }

    /**
     * 创建医疗设备资产
     */
    private function createMedicalAssets(): void
    {
        echo "正在创建医疗设备资产...\n";

        // 获取已创建的品牌
        $brands = EntityBrand::all();
        
        if ($brands->isEmpty()) {
            echo "警告：没有找到品牌数据，跳过设备创建\n";
            return;
        }

        // 创建50个医疗设备
        Asset::factory()->count(50)->make()->each(function ($asset) use ($brands) {
            // 随机分配品牌
            $asset->brand_id = $brands->random()->id;
            $asset->save();
        });

        echo "医疗设备资产创建完成！\n";
    }
}
