<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('entity_brands', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('entity_id')->comment('相关方ID');
            $table->string('name', 100)->comment('品牌名称');
            $table->text('description')->nullable()->comment('品牌描述');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 外键约束已移除，使用 Laravel ORM 关系管理
            $table->index('entity_id');
            $table->index('sort_order');
            $table->comment('相关方品牌表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('entity_brands');
    }
};
