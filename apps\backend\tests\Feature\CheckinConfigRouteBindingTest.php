<?php

namespace Tests\Feature;

use App\Models\CheckinConfig;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CheckinConfigRouteBindingTest extends TestCase
{
    use RefreshDatabase;

    public function test_checkin_config_route_binding_works()
    {
        // Create a user for authentication
        $user = User::factory()->create();
        
        // Create a checkin config
        $config = CheckinConfig::create([
            'attachable_type' => 'test',
            'attachable_id' => 1,
            'checkin_time' => time(),
            'status' => 1,
            'is_photo' => 0,
            'created_by' => $user->id,
        ]);

        // Test the route binding by making a request to the show endpoint
        $response = $this->actingAs($user, 'api')
            ->getJson("/api/admin/checkin-configs/{$config->id}");

        // If route binding works, we should get a successful response
        $response->assertStatus(200);
        $response->assertJsonFragment(['id' => $config->id]);
    }

    public function test_checkin_config_route_binding_fails_for_nonexistent_id()
    {
        // Create a user for authentication
        $user = User::factory()->create();

        // Test with a non-existent ID
        $response = $this->actingAs($user, 'api')
            ->getJson("/api/admin/checkin-configs/999999");

        // Should return 404 for non-existent record
        $response->assertStatus(404);
    }
}
