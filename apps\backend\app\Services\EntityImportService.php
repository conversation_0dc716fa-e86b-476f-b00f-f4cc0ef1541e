<?php

namespace App\Services;

use App\Enums\EntityType;
use App\Models\Entity;
use App\Models\ImportTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

/**
 * 相关方导入服务
 *
 * @deprecated 此类为旧版导入服务，建议创建继承 BaseImportService 的新实现
 * @todo 重构为继承 BaseImportService 的标准化导入服务
 */
class EntityImportService
{
    protected array $errors = [];

    protected int $totalRows = 0;

    protected int $successRows = 0;

    protected int $failedRows = 0;

    // 批量处理大小
    protected int $batchSize = 1000;

    public function __construct()
    {
        // 设置标题行格式化器为不格式化，保持原样
        HeadingRowFormatter::default('none');
    }

    /**
     * 处理导入任务
     */
    public function processImport(ImportTask $importTask): array
    {
        $this->resetCounters();

        try {
            // 检查文件是否存在
            $filePath = $this->getActualFilePath($importTask->file_path);
            if (! file_exists($filePath)) {
                throw new \Exception("导入文件不存在: {$importTask->file_path}");
            }

            // 读取Excel文件
            $data = Excel::toArray(new class {}, $filePath);

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空或格式不正确');
            }

            $rows = $data[0];

            // 找到标题行（包含"名称"的行）
            $headerRowIndex = -1;
            $headers = [];

            for ($i = 0; $i < count($rows); $i++) {
                if (in_array('名称', $rows[$i])) {
                    $headerRowIndex = $i;
                    $headers = $rows[$i];
                    break;
                }
            }

            if ($headerRowIndex === -1) {
                throw new \Exception('Excel文件中未找到标题行（包含"名称"的行）');
            }

            // 移除标题行之前的所有行，只保留数据行
            $dataRows = array_slice($rows, $headerRowIndex + 1);
            $this->totalRows = count($dataRows);

            if ($this->totalRows <= 0) {
                throw new \Exception('Excel文件中没有数据行');
            }

            $this->validateHeaders($headers);

            // 处理数据行
            $this->processRows($dataRows, $headers, $importTask);

            // 更新任务进度
            $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

            return [
                'total_rows' => $this->totalRows,
                'success_rows' => $this->successRows,
                'failed_rows' => $this->failedRows,
                'errors' => $this->errors,
            ];
        } catch (\Exception $e) {
            Log::error('相关方导入失败', [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 重置计数器
     */
    protected function resetCounters(): void
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;
    }

    /**
     * 获取实际文件路径
     */
    protected function getActualFilePath(string $filePath): string
    {
        // 如果是绝对路径且文件存在，直接返回
        if (file_exists($filePath)) {
            return $filePath;
        }

        // 处理附件系统的相对路径
        // 附件路径格式：attachments/YYYY/mm/dd/filename.ext
        $possiblePaths = [
            // Laravel public disk 路径 (storage/app/public/)
            storage_path('app/public/'.$filePath),
            // Laravel local disk 路径 (storage/app/)
            storage_path('app/'.$filePath),
            // 公共存储路径 (public/storage/)
            public_path('storage/'.$filePath),
            // 直接在public目录
            public_path($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都不存在，记录日志并返回原路径
        Log::warning('导入文件路径查找失败', [
            'original_path' => $filePath,
            'tried_paths' => $possiblePaths,
        ]);

        return $filePath;
    }

    /**
     * 验证表头
     */
    protected function validateHeaders(array $headers): void
    {
        $requiredHeaders = ['名称', '税号', '类型', '地址', '联系电话', '备注'];
        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (! empty($missingHeaders)) {
            throw new \Exception('Excel文件缺少必要的列: '.implode(', ', $missingHeaders));
        }
    }

    /**
     * 处理数据行
     */
    protected function processRows(array $rows, array $headers, ImportTask $importTask): void
    {
        $currentBatch = [];
        $batchStartRow = 2; // Excel行号从2开始

        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2;
            $rowData = array_combine($headers, $row);

            // 验证和清理数据
            try {
                $cleanedData = $this->validateAndCleanRowData($rowData, $rowNumber);
                $currentBatch[] = [
                    'row_number' => $rowNumber,
                    'data' => $cleanedData,
                ];

                // 当批次达到指定大小或是最后一行时，执行批量处理
                if (count($currentBatch) >= $this->batchSize || $index === count($rows) - 1) {
                    $this->processBatch($currentBatch, $importTask, $batchStartRow);

                    // 重置批次
                    $currentBatch = [];
                    $batchStartRow = $rowNumber + 1;
                }
            } catch (\Exception $e) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];

                Log::warning('数据验证失败', [
                    'task_id' => $importTask->id,
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * 验证和清理行数据
     */
    protected function validateAndCleanRowData(array $rowData, int $rowNumber): array
    {
        // 验证必填字段
        if (empty($rowData['名称'])) {
            throw new \Exception('名称不能为空');
        }

        if (empty($rowData['类型'])) {
            throw new \Exception('类型不能为空');
        }

        // 处理类型转换（支持中文名称）
        $entityType = $this->getEntityType($rowData['类型']);
        if (! $entityType) {
            $validTypes = implode(', ', array_map(fn ($case) => $case->label(), EntityType::cases()));
            throw new \Exception("类型无效，有效值: {$validTypes}");
        }

        // 验证税号格式（如果提供）
        if (! empty($rowData['税号'])) {
            if (strlen($rowData['税号']) > 50) {
                throw new \Exception('税号长度不能超过50个字符');
            }
        }

        // 验证联系电话格式（如果提供）
        if (! empty($rowData['联系电话'])) {
            if (strlen($rowData['联系电话']) > 20) {
                throw new \Exception('联系电话长度不能超过20个字符');
            }
        }

        // 验证地址长度（如果提供）
        if (! empty($rowData['地址'])) {
            if (strlen($rowData['地址']) > 255) {
                throw new \Exception('地址长度不能超过255个字符');
            }
        }

        // 验证备注长度（如果提供）
        if (! empty($rowData['备注'])) {
            if (strlen($rowData['备注']) > 1000) {
                throw new \Exception('备注长度不能超过1000个字符');
            }
        }

        return [
            'name' => trim($rowData['名称']),
            'tax_number' => ! empty($rowData['税号']) ? trim($rowData['税号']) : null,
            'entity_type' => $entityType,
            'address' => ! empty($rowData['地址']) ? trim($rowData['地址']) : null,
            'phone' => ! empty($rowData['联系电话']) ? trim($rowData['联系电话']) : null,
            'remark' => ! empty($rowData['备注']) ? trim($rowData['备注']) : null,
            'row_number' => $rowNumber,
        ];
    }

    /**
     * 处理批次数据
     */
    protected function processBatch(array $batch, ImportTask $importTask, int $batchStartRow): void
    {
        $failedRowsBefore = $this->failedRows;

        try {
            DB::beginTransaction();

            Log::info('开始处理相关方批次', [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'start_row' => $batchStartRow,
            ]);

            // 收集相关方数据
            $entities = [];

            foreach ($batch as $item) {
                $rowData = $item['data'];
                $rowNumber = $item['row_number'];

                try {
                    // 检查名称是否已存在
                    $existingEntity = Entity::where('name', $rowData['name'])->first();
                    if ($existingEntity) {
                        throw new \Exception("相关方名称 {$rowData['name']} 已存在");
                    }

                    // 检查税号是否已存在（如果提供且不为空）
                    if (! empty($rowData['tax_number'])) {
                        $existingEntity = Entity::where('tax_number', $rowData['tax_number'])->first();
                        if ($existingEntity) {
                            // 不抛出异常，而是让后续逻辑生成新的税号
                            $rowData['tax_number'] = null;
                        }
                    }

                    // 总是生成一个唯一的税号，避免重复
                    // $rowData['tax_number'] = $this->generateUniqueTaxNumber($rowData['name'], $rowNumber);

                    // 准备插入数据
                    $entityData = array_merge($rowData, [
                        'created_by' => $importTask->created_by,
                        'updated_by' => $importTask->created_by,
                        'created_at' => time(),
                        'updated_at' => time(),
                    ]);

                    // 移除 row_number，因为它不是数据库字段
                    unset($entityData['row_number']);

                    $entities[] = $entityData;
                } catch (\Exception $e) {
                    $this->failedRows++;
                    $this->errors[] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $rowData,
                    ];
                }
            }

            // 批量插入相关方数据
            $this->batchInsertEntities($entities);

            // 计算当前批次的成功数量
            $batchFailedCount = $this->failedRows - $failedRowsBefore;
            $batchSuccessCount = count($batch) - $batchFailedCount;
            $this->successRows += $batchSuccessCount;

            DB::commit();

            Log::info('相关方批次处理完成', [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'batch_success' => $batchSuccessCount,
                'batch_failed' => $batchFailedCount,
                'total_success' => $this->successRows,
                'total_failed' => $this->failedRows,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            // 将整个批次标记为失败
            foreach ($batch as $item) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $item['row_number'],
                    'error' => '批量处理失败: '.$e->getMessage(),
                    'data' => $item['data'],
                ];
            }

            Log::error('相关方批次处理失败', [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
                'batch_start' => $batchStartRow,
            ]);
        }
    }

    /**
     * 批量插入相关方数据
     */
    protected function batchInsertEntities(array $entities): void
    {
        if (empty($entities)) {
            return;
        }

        // 使用批量插入
        Entity::insert($entities);
    }

    /**
     * 根据中文名称获取对应的枚举值
     */
    private function getEntityType($name)
    {
        // 根据中文名称获取对应的枚举值
        foreach (EntityType::cases() as $case) {
            if ($case->label() === $name) {
                return $case->value;
            }
        }

        // 如果没找到中文名称，尝试直接匹配枚举值
        if (EntityType::isValid($name)) {
            return $name;
        }

        return null;
    }



    /**
     * 生成唯一的税号
     */
    public function generateUniqueTaxNumber($entityName, $rowNumber = null)
    {
        // 使用实体名称、行号和时间戳生成唯一税号
        $timestamp = time();
        $rowSuffix = $rowNumber ? str_pad($rowNumber, 3, '0', STR_PAD_LEFT) : str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
        $randomSuffix = strtoupper(substr(md5($entityName.$timestamp.$rowSuffix), 0, 6));

        $taxNumber = '91110108MA'.$rowSuffix.$randomSuffix;

        // 检查是否已存在，如果存在则添加更多随机字符
        $counter = 1;
        while (Entity::where('tax_number', $taxNumber)->exists()) {
            $taxNumber = '91110108MA'.$rowSuffix.$randomSuffix.str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;

            // 防止无限循环
            if ($counter > 99) {
                $taxNumber = '91110108MA'.strtoupper(substr(md5($entityName.$timestamp.rand()), 0, 12));
                break;
            }
        }

        return $taxNumber;
    }
}
