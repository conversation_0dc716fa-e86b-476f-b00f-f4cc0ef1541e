<?php

namespace App\Http\Resources;

use App\Models\ExportTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 导出任务资源类
 */
class ExportTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var ExportTask $this */
        return [
            'id' => $this->id,
            'type' => $this->type,
            'type_text' => $this->type_text,
            'filename' => $this->filename,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'total_rows' => $this->total_rows,
            'filters' => $this->filters,
            'error_details' => $this->error_details,
            'file_size' => $this->file_size,
            'file_size_text' => $this->file_size_text,
            'file_path' => $this->file_path,
            'can_download' => $this->canDownload(),
            'download_url' => $this->canDownload() ? route('export.download', $this->id) : null,
            'duration' => $this->duration,
            'duration_text' => $this->duration_text,
            'created_by' => $this->created_by,
            'started_at' => $this->started_at ? date('Y-m-d H:i:s', $this->started_at) : null,
            'completed_at' => $this->completed_at ? date('Y-m-d H:i:s', $this->completed_at) : null,
            'created_at' => $this->created_at ? date('Y-m-d H:i:s', $this->created_at) : null,
            'updated_at' => $this->updated_at ? date('Y-m-d H:i:s', $this->updated_at) : null,
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                ];
            }),
        ];
    }
}
