# 统一导出接口文档

## 概述

统一导出接口提供了一套标准化的导出功能，支持模板导出、数据导出、导出历史管理等功能。

## API 接口

### 1. 导出模板

**接口地址：** `GET /api/admin/export/{type}/template`

**支持的类型：**
- `asset` - 资产
- `category` - 分类  
- `entity` - 相关方
- `user` - 用户

**请求参数：**
- `with_sample` (boolean, 可选) - 是否包含示例数据（仅分类导出支持）

**示例：**
```bash
# 导出资产模板
GET /api/admin/export/asset/template

# 导出分类模板（含示例数据）
GET /api/admin/export/category/template?with_sample=true
```

### 2. 导出数据

**接口地址：** `POST /api/admin/export/{type}/data`

**支持的类型：**
- `asset` - 资产（已实现）
- `category` - 分类（开发中）
- `entity` - 相关方（开发中）
- `user` - 用户（开发中）

**请求参数（以资产为例）：**
```json
{
  "name": "资产名称关键词",
  "asset_number": "资产编号关键词",
  "category_id": 1,
  "status": "normal",
  "administrator": "管理员姓名",
  "user": "使用人姓名",
  "supplier": "供应商名称",
  "brand": "品牌名称",
  "purchase_price_min": 1000,
  "purchase_price_max": 10000,
  "purchase_date_start": "2024-01-01",
  "purchase_date_end": "2024-12-31",
  "tag_ids": [1, 2, 3],
  "created_at_start": "2024-01-01",
  "created_at_end": "2024-12-31"
}
```

### 3. 获取支持的导出类型

**接口地址：** `GET /api/admin/export/types`

**响应示例：**
```json
{
  "data": [
    {
      "type": "asset",
      "name": "资产",
      "description": "资产导入模板，包含资产基本信息、分类、相关方等字段",
      "supports_sample": false
    },
    {
      "type": "category",
      "name": "分类",
      "description": "分类导入模板，支持层级结构",
      "supports_sample": true
    }
  ]
}
```

### 4. 导出历史

**接口地址：** `GET /api/admin/export/history`

**请求参数：**
- `type` (string, 可选) - 导出类型筛选
- `status` (string, 可选) - 状态筛选 (pending, processing, completed, failed)
- `created_at_start` (string, 可选) - 创建时间开始
- `created_at_end` (string, 可选) - 创建时间结束

**响应示例：**
```json
{
  "data": [
    {
      "id": 1,
      "type": "asset",
      "type_text": "资产",
      "filename": "资产导出_2024-01-01_10-30-00.xlsx",
      "status": "completed",
      "status_text": "已完成",
      "total_rows": 1500,
      "file_size": 2048576,
      "file_size_text": "2.0 MB",
      "can_download": true,
      "download_url": "/api/admin/export/download/1",
      "duration": 120,
      "duration_text": "2分钟",
      "created_at": "2024-01-01 10:30:00",
      "completed_at": "2024-01-01 10:32:00"
    }
  ],
  "current_page": 1,
  "last_page": 1,
  "per_page": 20,
  "total": 1
}
```

### 5. 下载导出文件

**接口地址：** `GET /api/admin/export/download/{exportTask}`

**说明：** 只能下载自己创建的导出文件

## 数据模型

### ExportTask 导出任务

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 主键ID |
| type | string | 导出类型 |
| filename | string | 文件名 |
| status | string | 状态 (pending, processing, completed, failed) |
| total_rows | int | 总行数 |
| filters | json | 过滤条件 |
| error_details | json | 错误详情 |
| file_size | bigint | 文件大小(字节) |
| file_path | string | 文件路径 |
| started_at | int | 开始时间戳 |
| completed_at | int | 完成时间戳 |
| created_by | int | 创建人ID |

## 扩展开发

### 1. 添加新的导出类型

1. 创建导出服务类继承 `BaseExportService`
2. 实现抽象方法：
   - `buildQuery()` - 构建查询
   - `getHeaders()` - 获取表头
   - `mapRow()` - 映射数据行
   - `getColumnWidths()` - 获取列宽
   - `applyCustomFilters()` - 应用自定义过滤条件

3. 在 `ExportController::createDataExportService()` 中添加新类型

### 2. 导出服务示例

```php
<?php

namespace App\Services;

use App\Models\YourModel;
use Illuminate\Database\Eloquent\Builder;

class YourExportService extends BaseExportService
{
    protected function buildQuery(): Builder
    {
        return YourModel::query()
            ->with(['relation1', 'relation2'])
            ->orderBy('created_at', 'desc');
    }

    protected function getHeaders(): array
    {
        return ['ID', '名称', '状态', '创建时间'];
    }

    protected function mapRow($row): array
    {
        return [
            $row->id,
            $row->name,
            $this->formatStatus($row->status, ['active' => '启用', 'inactive' => '禁用']),
            $this->formatDateTime($row->created_at),
        ];
    }

    protected function getColumnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 20,  // 名称
            'C' => 12,  // 状态
            'D' => 18,  // 创建时间
        ];
    }

    protected function applyCustomFilters(Builder $query): Builder
    {
        if ($this->request->has('name')) {
            $query->where('name', 'like', '%' . $this->request->input('name') . '%');
        }

        return $query;
    }
}
```

## 迁移指南

### ✅ 已完成迁移

所有旧的导入导出接口已经成功迁移到统一接口：

- ~~`/admin/assets/export-template`~~ → `/admin/export/asset/template` ✅
- ~~`/admin/categories/export-template`~~ → `/admin/export/category/template` ✅
- ~~`/admin/entities/export-template`~~ → `/admin/export/entity/template` ✅
- ~~`/admin/users/export-template`~~ → `/admin/export/user/template` ✅

**注意：** 旧接口已被移除，请使用新的统一接口。

### 前端调用示例

```javascript
// 导出模板
const exportTemplate = async (type, withSample = false) => {
  const params = withSample ? '?with_sample=true' : '';
  const response = await fetch(`/api/admin/export/${type}/template${params}`);
  
  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = response.headers.get('content-disposition').split('filename=')[1];
    a.click();
  }
};

// 导出数据
const exportData = async (type, filters = {}) => {
  const response = await fetch(`/api/admin/export/${type}/data`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(filters),
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = response.headers.get('content-disposition').split('filename=')[1];
    a.click();
  }
};

// 获取导出历史
const getExportHistory = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/admin/export/history?${params}`);
  return await response.json();
};
```

## 注意事项

1. 所有导出文件都会记录在导出历史中
2. 导出文件有大小限制（默认最大10000行）
3. 只能下载自己创建的导出文件
4. 导出过程中会记录详细的日志
5. 支持多种过滤条件和排序方式
6. 导出文件会自动添加时间戳避免重名
