<template>
  <div class="particle-wave-container">
    <canvas ref="canvasRef" class="particle-canvas"></canvas>
    <div class="gradient-overlay"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import gsap from 'gsap'

  const canvasRef = ref<HTMLCanvasElement>()

  interface Particle {
    x: number
    y: number
    baseY: number
    vx: number
    vy: number
    radius: number
    alpha: number
    color: string
  }

  class ParticleWaveEffect {
    private canvas: HTMLCanvasElement
    private ctx: CanvasRenderingContext2D
    private particles: Particle[] = []
    private mouseX = 0
    private mouseY = 0
    private animationId: number | null = null
    private time = 0
    private wavePhase = 0
    private isMobile = false
    private isLowPerformance = false

    // 配置参数（根据性能动态调整）
    private particleCount = 80
    private readonly colors = [
      'rgba(66, 153, 225, ', // 蓝色
      'rgba(129, 140, 248, ', // 紫色
      'rgba(237, 100, 166, ', // 粉色
      'rgba(72, 187, 120, ' // 绿色
    ]

    constructor(canvas: HTMLCanvasElement) {
      this.canvas = canvas
      this.ctx = canvas.getContext('2d')!

      this.detectPerformance()
      this.init()
      this.bindEvents()
    }

    private detectPerformance() {
      // 检测是否为移动设备
      this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      )

      // 检测设备性能
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight
      const pixelRatio = window.devicePixelRatio || 1

      // 根据屏幕大小和像素比判断性能
      if (this.isMobile || screenWidth * screenHeight * pixelRatio > 3000000) {
        this.isLowPerformance = true
        this.particleCount = 40 // 移动端减少粒子数量
      } else {
        this.particleCount = 80
      }
    }

    private init() {
      this.resize()
      this.createParticles()
      this.animate()
    }

    private resize() {
      const container = this.canvas.parentElement
      if (container) {
        this.canvas.width = container.offsetWidth
        this.canvas.height = container.offsetHeight
      }
    }

    private createParticles() {
      this.particles = []
      const width = this.canvas.width
      const height = this.canvas.height

      for (let i = 0; i < this.particleCount; i++) {
        const x = Math.random() * width
        const y = Math.random() * height

        this.particles.push({
          x,
          y,
          baseY: y,
          vx: (Math.random() - 0.5) * 0.5,
          vy: 0,
          radius: Math.random() * 3 + 1,
          alpha: Math.random() * 0.5 + 0.3,
          color: this.colors[Math.floor(Math.random() * this.colors.length)]
        })
      }
    }

    private bindEvents() {
      // 窗口大小调整
      window.addEventListener('resize', () => {
        this.detectPerformance() // 重新检测性能
        this.resize()
        this.createParticles()
      })

      // 只在非移动端启用鼠标交互
      if (!this.isMobile) {
        // 鼠标移动事件
        this.canvas.addEventListener('mousemove', (e) => {
          const rect = this.canvas.getBoundingClientRect()
          this.mouseX = e.clientX - rect.left
          this.mouseY = e.clientY - rect.top
        })

        // 鼠标离开
        this.canvas.addEventListener('mouseleave', () => {
          gsap.to(this, {
            mouseX: this.canvas.width / 2,
            mouseY: this.canvas.height / 2,
            duration: 1,
            ease: 'power2.out'
          })
        })
      } else {
        // 移动端默认鼠标位置在中心
        this.mouseX = this.canvas.width / 2
        this.mouseY = this.canvas.height / 2
      }
    }

    private drawParticle(particle: Particle) {
      const { ctx } = this

      // 绘制发光效果
      const gradient = ctx.createRadialGradient(
        particle.x,
        particle.y,
        0,
        particle.x,
        particle.y,
        particle.radius * 3
      )
      gradient.addColorStop(0, particle.color + particle.alpha + ')')
      gradient.addColorStop(1, particle.color + '0)')

      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.radius * 3, 0, Math.PI * 2)
      ctx.fill()

      // 绘制粒子核心
      ctx.fillStyle = particle.color + particle.alpha * 1.5 + ')'
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2)
      ctx.fill()
    }

    private drawConnections() {
      // 移动端和低性能设备跳过连线绘制
      if (this.isLowPerformance) return

      const { ctx, particles } = this

      particles.forEach((p1, i) => {
        particles.slice(i + 1).forEach((p2) => {
          const dx = p1.x - p2.x
          const dy = p1.y - p2.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 150) {
            const opacity = (1 - distance / 150) * 0.3
            ctx.strokeStyle = `rgba(129, 140, 248, ${opacity})`
            ctx.lineWidth = 0.5
            ctx.beginPath()
            ctx.moveTo(p1.x, p1.y)
            ctx.lineTo(p2.x, p2.y)
            ctx.stroke()
          }
        })
      })
    }

    private updateParticles() {
      const width = this.canvas.width
      const height = this.canvas.height

      this.wavePhase += 0.01

      this.particles.forEach((particle, index) => {
        // 波浪运动
        const waveOffset = Math.sin(this.wavePhase + index * 0.1) * 30
        const targetY = particle.baseY + waveOffset

        // 鼠标吸引力
        const dx = this.mouseX - particle.x
        const dy = this.mouseY - particle.y
        const distance = Math.sqrt(dx * dx + dy * dy)

        if (distance < 200) {
          const force = (1 - distance / 200) * 0.5
          particle.vx += (dx / distance) * force * 0.5
          particle.vy += (dy / distance) * force * 0.5
        }

        // 更新位置
        particle.x += particle.vx
        particle.y += (targetY - particle.y) * 0.05 + particle.vy

        // 应用阻尼
        particle.vx *= 0.98
        particle.vy *= 0.98

        // 边界处理
        if (particle.x < 0 || particle.x > width) {
          particle.vx = -particle.vx
          particle.x = Math.max(0, Math.min(width, particle.x))
        }

        if (particle.y < 0 || particle.y > height) {
          particle.vy = -particle.vy
          particle.y = Math.max(0, Math.min(height, particle.y))
        }

        // 动态透明度
        particle.alpha = 0.3 + Math.sin(this.time * 0.001 + index) * 0.2
      })
    }

    private animate() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

      // 更新粒子
      this.updateParticles()

      // 绘制连线
      this.drawConnections()

      // 绘制粒子
      this.particles.forEach((particle) => {
        this.drawParticle(particle)
      })

      this.time++
      this.animationId = requestAnimationFrame(() => this.animate())
    }

    destroy() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }

      window.removeEventListener('resize', () => {})
    }
  }

  let particleEffect: ParticleWaveEffect | null = null

  onMounted(() => {
    if (canvasRef.value) {
      particleEffect = new ParticleWaveEffect(canvasRef.value)
    }
  })

  onUnmounted(() => {
    if (particleEffect) {
      particleEffect.destroy()
    }
  })
</script>

<style lang="scss" scoped>
  .particle-wave-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #7e57c2 100%);

    .particle-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(
        ellipse at center,
        rgba(129, 140, 248, 0.1) 0%,
        rgba(30, 60, 114, 0.2) 50%,
        rgba(30, 60, 114, 0.4) 100%
      );
      z-index: 2;
      pointer-events: none;
      mix-blend-mode: screen;
    }
  }

  // 性能优化：移动端使用简化版本
  @media (max-width: 768px) {
    .particle-wave-container {
      .particle-canvas {
        opacity: 0.7;
      }
    }
  }

  // 深色模式适配
  .dark {
    .particle-wave-container {
      background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);

      .gradient-overlay {
        background: radial-gradient(
          ellipse at center,
          rgba(66, 68, 90, 0.1) 0%,
          rgba(15, 12, 41, 0.3) 50%,
          rgba(15, 12, 41, 0.5) 100%
        );
      }
    }
  }
</style>
