<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CategoryRequest;
use App\Http\Resources\Admin\CategoryResource;
use App\Models\Category;
use App\Services\CategoryService;
use Illuminate\Http\Request;

/**
 * @group 分类管理
 *
 * 系统分类管理接口
 */
class CategoryController extends Controller
{
    public function __construct(
        private CategoryService $categoryService
    ) {}

    /**
     * 获取分类树
     *
     * 获取所有分类的树形结构数据
     *
     * @queryParam status integer 状态筛选（1启用 0禁用） Example: 1
     */
    public function index(Request $request)
    {
        // 获取所有分类的扁平数组
        $query = Category::query();

        // 如果需要过滤状态
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // 按照parent_id和sort排序，数字越大越靠前
        $categories = $query->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        return CategoryResource::collection($categories);
    }

    /**
     * 创建分类
     *
     * 创建新的分类
     *
     * @bodyParam name string required 分类名称 Example: 电子设备
     * @bodyParam code string required 分类编码 Example: electronic
     * @bodyParam parent_id integer 父级ID，默认0 Example: 0
     * @bodyParam sort integer 排序值，默认0 Example: 0
     * @bodyParam status integer 状态，默认1 Example: 1
     * @bodyParam remark string 备注信息 Example: 电子设备分类
     */
    public function store(CategoryRequest $request)
    {
        $data = $request->validated();
        $category = $this->categoryService->create($data);

        return (new CategoryResource($category))->response()->setStatusCode(201);
    }

    /**
     * 更新分类
     *
     * 更新指定的分类信息
     *
     * @urlParam category integer required 分类ID Example: 1
     *
     * @bodyParam name string required 分类名称 Example: 电子设备
     * @bodyParam code string required 分类编码 Example: electronic
     * @bodyParam parent_id integer 父级ID Example: 0
     * @bodyParam sort integer 排序值 Example: 0
     * @bodyParam status integer 状态 Example: 1
     * @bodyParam remark string 备注信息 Example: 电子设备分类
     */
    public function update(CategoryRequest $request, Category $category)
    {
        $data = $request->validated();
        $category = $this->categoryService->update($category, $data);

        return new CategoryResource($category);
    }

    /**
     * 删除分类
     *
     * 删除指定的分类（分类下存在子分类时无法删除）
     *
     * @urlParam category integer required 分类ID Example: 1
     */
    public function destroy(Category $category)
    {
        $this->categoryService->delete($category);

        return response()->noContent();
    }

    /**
     * 获取子分类
     *
     * 获取指定分类的直接子分类列表
     *
     * @urlParam parentId integer required 父级分类ID，传0获取顶级分类 Example: 0
     */
    public function getChildren(int $parentId)
    {
        $children = $this->categoryService->getChildren($parentId);

        return CategoryResource::collection($children);
    }


}
