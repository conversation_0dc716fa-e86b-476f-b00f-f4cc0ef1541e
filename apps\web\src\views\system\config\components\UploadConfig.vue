<template>
  <div class="upload-config">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      style="max-width: 600px"
    >
      <ElFormItem label="存储方式" prop="storage_type">
        <ElSelect
          v-model="formData.storage_type"
          placeholder="请选择存储方式"
          style="width: 100%"
          clearable
        >
          <ElOption label="本地存储" value="local" />
          <ElOption label="阿里云OSS" value="aliyun" />
        </ElSelect>
      </ElFormItem>

      <!-- 阿里云配置项 -->
      <template v-if="formData.storage_type === 'aliyun'">
        <ElFormItem label="Access Key" prop="aliyun_access_key">
          <ElInput
            v-model="formData.aliyun_access_key"
            placeholder="请输入阿里云 Access Key"
            clearable
          />
        </ElFormItem>

        <ElFormItem label="Secret Key" prop="aliyun_secret_key">
          <ElInput
            v-model="formData.aliyun_secret_key"
            type="password"
            placeholder="请输入阿里云 Secret Key"
            show-password
            clearable
          />
        </ElFormItem>

        <ElFormItem label="Bucket" prop="aliyun_bucket">
          <ElInput
            v-model="formData.aliyun_bucket"
            placeholder="请输入 OSS Bucket 名称"
            clearable
          />
        </ElFormItem>

        <ElFormItem label="存储区域" prop="aliyun_region">
          <ElSelect
            v-model="formData.aliyun_region"
            placeholder="请选择存储区域"
            style="width: 100%"
          >
            <ElOption label="华东1（杭州）" value="oss-cn-hangzhou.aliyuncs.com" />
            <ElOption label="华东2（上海）" value="oss-cn-shanghai.aliyuncs.com" />
            <ElOption label="华北1（青岛）" value="oss-cn-qingdao.aliyuncs.com" />
            <ElOption label="华北2（北京）" value="oss-cn-beijing.aliyuncs.com" />
            <ElOption label="华北3（张家口）" value="oss-cn-zhangjiakou.aliyuncs.com" />
            <ElOption label="华北5（呼和浩特）" value="oss-cn-huhehaote.aliyuncs.com" />
            <ElOption label="华北6（乌兰察布）" value="oss-cn-wulanchabu.aliyuncs.com" />
            <ElOption label="华南1（深圳）" value="oss-cn-shenzhen.aliyuncs.com" />
            <ElOption label="华南2（河源）" value="oss-cn-heyuan.aliyuncs.com" />
            <ElOption label="华南3（广州）" value="oss-cn-guangzhou.aliyuncs.com" />
            <ElOption label="西南1（成都）" value="oss-cn-chengdu.aliyuncs.com" />
            <ElOption label="香港" value="oss-cn-hongkong.aliyuncs.com" />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="RAM角色ARN" prop="aliyun_sts_role_arn">
          <ElInput
            v-model="formData.aliyun_sts_role_arn"
            placeholder="格式: acs:ram::账号ID:role/角色名"
            clearable
          />
          <div style="margin-top: 5px; font-size: 12px; color: #909399">
            用于生成STS临时凭证，请在阿里云RAM控制台创建角色并授予OSS权限
          </div>
        </ElFormItem>
      </template>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
  // Vue 核心
  import { ref, onMounted, watch } from 'vue'

  // UI 框架
  import { ElForm, ElFormItem, ElInput, ElSelect, ElOption } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // Store
  import { useConfigStore } from '@/store/modules/config'

  // 类型
  import type { UploadConfig } from '@/types/api'

  const configStore = useConfigStore()
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = ref<UploadConfig>({
    storage_type: null,
    aliyun_access_key: '',
    aliyun_secret_key: '',
    aliyun_bucket: '',
    aliyun_region: '',
    aliyun_sts_role_arn: ''
  })

  // 动态验证规则
  const rules = ref<FormRules>({
    storage_type: [{ required: true, message: '请选择存储方式', trigger: 'change' }]
  })

  // 监听存储方式变化，动态设置验证规则
  watch(
    () => formData.value.storage_type,
    (newVal) => {
      if (newVal === 'aliyun') {
        rules.value = {
          storage_type: [{ required: true, message: '请选择存储方式', trigger: 'change' }],
          aliyun_access_key: [{ required: true, message: '请输入 Access Key', trigger: 'blur' }],
          aliyun_secret_key: [{ required: true, message: '请输入 Secret Key', trigger: 'blur' }],
          aliyun_bucket: [{ required: true, message: '请输入 Bucket 名称', trigger: 'blur' }],
          aliyun_region: [{ required: true, message: '请选择存储区域', trigger: 'change' }],
          aliyun_sts_role_arn: [
            { required: true, message: '请输入 RAM 角色 ARN', trigger: 'blur' },
            {
              pattern: /^acs:ram::\d+:role\/[\w-]+$/,
              message: 'ARN格式不正确，应为: acs:ram::账号ID:role/角色名',
              trigger: 'blur'
            }
          ]
        }
      } else {
        rules.value = {
          storage_type: [{ required: true, message: '请选择存储方式', trigger: 'change' }]
        }
      }
    }
  )

  // 加载配置
  const loadConfig = async () => {
    const config = await configStore.fetchConfig()
    formData.value = { ...config.upload }
  }

  onMounted(() => {
    loadConfig()
  })

  // 暴露获取表单数据的方法
  const getFormData = async () => {
    // 先验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return null

    // 处理数据
    const data: UploadConfig = {
      storage_type: formData.value.storage_type,
      aliyun_access_key: formData.value.aliyun_access_key,
      aliyun_secret_key: formData.value.aliyun_secret_key,
      aliyun_bucket: formData.value.aliyun_bucket,
      aliyun_region: formData.value.aliyun_region,
      aliyun_sts_role_arn: formData.value.aliyun_sts_role_arn
    }

    // 如果是本地存储，清空阿里云配置
    if (data.storage_type === 'local') {
      data.aliyun_access_key = ''
      data.aliyun_secret_key = ''
      data.aliyun_bucket = ''
      data.aliyun_region = ''
      data.aliyun_sts_role_arn = ''
    }

    return data
  }

  // 暴露方法给父组件
  defineExpose({
    getFormData
  })
</script>

<style lang="scss" scoped>
  .upload-config {
    :deep(.el-form-item__label) {
      font-weight: normal;
    }
  }
</style>
