<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

/**
 * 导出服务基类
 * 定义通用的导出逻辑和接口
 */
abstract class BaseExportService implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, ShouldAutoSize
{
    /**
     * 导出类型
     */
    protected string $type;

    /**
     * 请求参数
     */
    protected Request $request;

    /**
     * 导出数据
     */
    protected Collection $data;

    /**
     * 最大导出行数
     */
    protected int $maxRows = 10000;

    /**
     * 构造函数
     */
    public function __construct(string $type, Request $request = null)
    {
        $this->type = $type;
        $this->request = $request ?? request();
        $this->data = collect();
    }

    /**
     * 获取导出数据集合
     */
    public function collection(): Collection
    {
        if ($this->data->isEmpty()) {
            $this->data = $this->loadData();
        }

        return $this->data;
    }

    /**
     * 获取表头
     */
    public function headings(): array
    {
        return $this->getHeaders();
    }

    /**
     * 映射数据行
     */
    public function map($row): array
    {
        return $this->mapRow($row);
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet): array
    {
        $rowCount = $this->collection()->count() + 1; // +1 for header

        return [
            // 表头样式
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // 数据行样式
            "A1:Z{$rowCount}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    /**
     * 设置列宽
     */
    public function columnWidths(): array
    {
        return $this->getColumnWidths();
    }

    /**
     * 加载数据
     */
    protected function loadData(): Collection
    {
        try {
            Log::info("开始加载{$this->type}导出数据", [
                'type' => $this->type,
                'filters' => $this->request->all(),
                'user_id' => auth()->id(),
            ]);

            $query = $this->buildQuery();
            
            // 应用过滤条件
            $query = $this->applyFilters($query);
            
            // 限制导出数量
            $query = $query->limit($this->maxRows);
            
            $data = $query->get();

            Log::info("成功加载{$this->type}导出数据", [
                'type' => $this->type,
                'count' => $data->count(),
                'user_id' => auth()->id(),
            ]);

            return $data;

        } catch (\Exception $e) {
            Log::error("加载{$this->type}导出数据失败", [
                'type' => $this->type,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 应用过滤条件
     */
    protected function applyFilters(Builder $query): Builder
    {
        // 通用过滤条件
        if ($this->request->has('created_at_start')) {
            $query->where('created_at', '>=', $this->request->input('created_at_start'));
        }

        if ($this->request->has('created_at_end')) {
            $query->where('created_at', '<=', $this->request->input('created_at_end'));
        }

        if ($this->request->has('updated_at_start')) {
            $query->where('updated_at', '>=', $this->request->input('updated_at_start'));
        }

        if ($this->request->has('updated_at_end')) {
            $query->where('updated_at', '<=', $this->request->input('updated_at_end'));
        }

        // 调用子类的过滤方法
        return $this->applyCustomFilters($query);
    }

    /**
     * 格式化日期时间
     */
    protected function formatDateTime($datetime): string
    {
        if (!$datetime) {
            return '';
        }

        if (is_string($datetime)) {
            $datetime = \Carbon\Carbon::parse($datetime);
        }

        return $datetime->format('Y-m-d H:i:s');
    }

    /**
     * 格式化日期
     */
    protected function formatDate($date): string
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        return $date->format('Y-m-d');
    }

    /**
     * 格式化布尔值
     */
    protected function formatBoolean($value): string
    {
        if (is_null($value)) {
            return '';
        }

        return $value ? '是' : '否';
    }

    /**
     * 格式化状态
     */
    protected function formatStatus($status, array $statusMap = []): string
    {
        if (empty($statusMap)) {
            return $status ?? '';
        }

        return $statusMap[$status] ?? $status ?? '';
    }

    /**
     * 安全获取关联数据
     */
    protected function safeGet($object, string $key, $default = ''): string
    {
        if (!$object) {
            return $default;
        }

        $value = data_get($object, $key, $default);
        
        return is_string($value) ? $value : (string) $value;
    }

    /**
     * 生成导出文件名
     */
    public function generateFilename(): string
    {
        $typeNames = [
            'asset' => '资产',
            'category' => '分类',
            'entity' => '相关方',
            'user' => '用户',
        ];

        $typeName = $typeNames[$this->type] ?? $this->type;
        $timestamp = date('Y-m-d_H-i-s');

        return "{$typeName}导出_{$timestamp}.xlsx";
    }

    /**
     * 构建查询（子类实现）
     */
    abstract protected function buildQuery(): Builder;

    /**
     * 获取表头（子类实现）
     */
    abstract protected function getHeaders(): array;

    /**
     * 映射数据行（子类实现）
     */
    abstract protected function mapRow($row): array;

    /**
     * 获取列宽（子类实现）
     */
    abstract protected function getColumnWidths(): array;

    /**
     * 应用自定义过滤条件（子类可选实现）
     */
    protected function applyCustomFilters(Builder $query): Builder
    {
        return $query;
    }
}
