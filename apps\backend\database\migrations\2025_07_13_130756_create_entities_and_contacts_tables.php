<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建相关方表
        Schema::create('entities', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('相关方名称');
            $table->string('tax_number', 50)->nullable()->comment('税号');
            $table->string('entity_type', 20)->comment('相关方类型');
            $table->string('address', 255)->nullable()->comment('地址');
            $table->string('phone', 20)->nullable()->comment('联系电话');
            $table->string('keywords', 50)->nullable()->comment('特征词');
            $table->text('remark')->nullable()->comment('备注');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
            // $table->softDeletes();

            $table->index('entity_type');
            $table->index('created_at');
            $table->comment('相关方表');
        });

        // 创建相关方联系人表
        Schema::create('entity_contacts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('entity_id')->comment('相关方ID');
            $table->string('name', 50)->comment('联系人姓名');
            $table->string('phone', 20)->comment('联系电话');
            $table->string('position', 50)->nullable()->comment('职位');
            $table->string('department', 50)->nullable()->comment('部门');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 外键约束已移除，使用 Laravel ORM 关系管理
            $table->index('entity_id');
            $table->comment('相关方联系人表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('entity_contacts');
        Schema::dropIfExists('entities');
    }
};
