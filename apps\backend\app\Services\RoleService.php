<?php

namespace App\Services;

use App\Models\Role;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class RoleService
{
    /**
     * 获取角色分页列表
     */
    public function paginate(int $perPage = 20, ?string $search = null): LengthAwarePaginator
    {
        $query = Role::query();

        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }

        return $query->withCount('users')
            ->with('roleMenuPermissions')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * 创建角色
     */
    public function create(array $data): Role
    {
        return DB::transaction(function () use ($data) {
            return Role::create($data);
        });
    }

    /**
     * 更新角色
     */
    public function update(Role $role, array $data): Role
    {
        return DB::transaction(function () use ($role, $data) {
            $role->update($data);

            return $role->fresh();
        });
    }

    /**
     * 删除角色
     */
    public function delete(Role $role): void
    {
        // 检查是否有用户使用此角色
        if ($role->users()->exists()) {
            throw ValidationException::withMessages([
                'role' => '该角色下还有用户，无法删除',
            ]);
        }

        DB::transaction(function () use ($role) {
            // 删除角色（由于暂时没有权限功能，直接删除角色）
            $role->delete();
        });
    }

    /**
     * 获取角色下的用户
     */
    public function getUsersByRole(Role $role, int $perPage = 20): LengthAwarePaginator
    {
        return $role->users()
            ->with(['attachments'])
            ->paginate($perPage);
    }

    /**
     * 获取角色的菜单权限
     */
    public function getRoleMenuPermissions(Role $role): array
    {
        $permissions = $role->roleMenuPermissions()
            ->with(['menu', 'menuPermission'])
            ->get();

        $result = [];
        foreach ($permissions as $permission) {
            $menuId = $permission->menu_id;

            if (! isset($result[$menuId])) {
                $result[$menuId] = [
                    'menu_id' => $menuId,
                    'menu_name' => $permission->menu->name,
                    'menu_title' => $permission->menu->title,
                    'has_menu_access' => false,
                    'permissions' => [],
                ];
            }

            if ($permission->menu_permission_id) {
                $result[$menuId]['permissions'][] = [
                    'id' => $permission->menuPermission->id,
                    'title' => $permission->menuPermission->title,
                    'auth_mark' => $permission->menuPermission->auth_mark,
                ];
            } else {
                $result[$menuId]['has_menu_access'] = true;
            }
        }

        return array_values($result);
    }

    /**
     * 为角色分配菜单权限
     */
    public function assignMenuPermissions(Role $role, array $permissions): void
    {
        DB::transaction(function () use ($role, $permissions) {
            // 先删除现有权限
            $role->roleMenuPermissions()->delete();

            foreach ($permissions as $permission) {
                // 如果没有具体权限，只分配菜单访问权限
                if (empty($permission['permission_ids'])) {
                    $role->roleMenuPermissions()->create([
                        'menu_id' => $permission['menu_id'],
                        'menu_permission_id' => null,
                    ]);
                } else {
                    // 分配具体权限
                    foreach ($permission['permission_ids'] as $menuPermissionId) {
                        $role->roleMenuPermissions()->create([
                            'menu_id' => $permission['menu_id'],
                            'menu_permission_id' => $menuPermissionId,
                        ]);
                    }
                }
            }
        });
    }

    /**
     * 同步角色的菜单权限（覆盖式）
     */
    public function syncMenuPermissions(Role $role, array $permissions): void
    {
        DB::transaction(function () use ($role, $permissions) {
            // 删除现有权限
            $role->roleMenuPermissions()->delete();

            // 添加新权限
            foreach ($permissions as $permission) {
                $role->roleMenuPermissions()->create([
                    'menu_id' => $permission['menu_id'],
                    'menu_permission_id' => $permission['menu_permission_id'] ?? null,
                ]);
            }
        });
    }

    /**
     * 移除角色的菜单权限
     */
    public function removeMenuPermissions(Role $role, array $permissions): void
    {
        DB::transaction(function () use ($role, $permissions) {
            foreach ($permissions as $permission) {
                $role->roleMenuPermissions()
                    ->where('menu_id', $permission['menu_id'])
                    ->where('menu_permission_id', $permission['menu_permission_id'] ?? null)
                    ->delete();
            }
        });
    }
}
