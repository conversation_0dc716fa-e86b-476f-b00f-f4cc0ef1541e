// 字典管理相关类型定义

// 字典分类
export interface DictionaryCategory {
  id: string
  code: string
  name: string
  description: string
  item_count: number
  enabled: boolean
  sort?: number
  created_at: string
  updated_at?: string
}

// 字典分类表单
export interface DictionaryCategoryForm {
  id?: string
  code: string
  name: string
  description: string
  enabled: boolean
  sort?: number
}

// 字典项
export interface DictionaryItem {
  id: string
  category_id: string
  code: string
  value: string
  sort: number
  remark: string
  enabled: boolean
  color: string
  config: Record<string, any>
  created_at: string
  updated_at?: string
}

// 字典项表单
export interface DictionaryItemForm {
  id?: string
  category_id: string
  code: string
  value: string
  sort: number
  remark: string
  enabled: boolean
  color: string
  config: Record<string, any>
}

// 字典分类搜索参数
export interface DictionarySearchParams {
  name?: string
  code?: string
  enabled?: boolean
  current?: number
  size?: number
}

// 字典项搜索参数
export interface DictionaryItemSearchParams {
  category_id?: string
  code?: string
  value?: string
  enabled?: boolean
  current?: number
  size?: number
}

// 字典分页响应
export interface DictionaryPageResponse<T> {
  data: T[]
  total: number
  current: number
  size: number
}
