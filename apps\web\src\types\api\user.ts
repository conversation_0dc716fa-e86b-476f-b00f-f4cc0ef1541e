// 用户管理相关类型定义

import type { PaginatedResponse } from './pagination'

// 登录参数
export interface LoginParams {
  account: string
  password: string
}

// 登录响应
export interface LoginResponse {
  token: string
  user: UserListItem // 使用已有的 UserListItem 类型，它包含了所有需要的用户信息
}

// 用户信息
export interface UserInfo {
  userId: number
  userName: string
  roles: string[]
  buttons: string[]
  avatar?: string
  email?: string
  account?: string
}

// 用户列表数据
export type UserListData = PaginatedResponse<UserListItem>

// 用户列表项
export interface UserListItem {
  id: number | string
  nickname: string
  email: string | null
  account: string
  avatar: string | null
  avatar_id: number | null
  status: 'enable' | 'disable'
  status_label: string
  roles?: Array<{
    id: number
    name: string
    description?: string
  }>
  is_super_admin?: boolean // 超级管理员标识
  created_at: string
  updated_at: string
}

// 用户表单
export interface UserForm {
  nickname: string
  password?: string
  email?: string
  account: string
  avatar_id?: number | null
  status: 'enable' | 'disable'
}

// 用户搜索参数
export interface UserSearchParams {
  keyword?: string
  status?: 'enable' | 'disable'
  current?: number
  size?: number
  per_page?: number
}

// 分页参数
export interface PaginatingParams {
  /** 当前页码 */
  current: number
  /** 每页条数 */
  size: number
  /** 总条数 */
  total: number
}

// 通用搜索参数
export type PaginatingSearchParams = Pick<PaginatingParams, 'current' | 'size'>

// 启用状态
export type EnableStatus = 'enable' | 'disable'
