<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name 标签名称
 * @property string $category 标签分类
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Lifecycle> $lifecycles
 * @property-read int|null $lifecycles_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tag whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Tag extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'category',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 关联的生命周期
     */
    public function lifecycles(): BelongsToMany
    {
        return $this->belongsToMany(Lifecycle::class, 'lifecycle_tags', 'tag_id', 'lifecycle_id');
    }
}
