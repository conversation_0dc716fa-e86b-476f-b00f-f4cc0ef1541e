# Device Cloud SaaS 项目指南

本文档为 Claude Code 和开发人员提供项目概况和开发导航。

## 📌 项目概述

Device Cloud SaaS 是一个基于 Vue 3 + Laravel 12 的企业级 SaaS 管理系统，提供设备管理、资产管理、用户权限等核心功能。

### 技术栈
- **前端**：Vue 3.5 + TypeScript + Element Plus + Vite + Pinia
- **后端**：Laravel 12 + PHP 8.2 + MySQL 8.4.5 + Sanctum
- **容器**：Docker（开发环境）

## 📁 项目结构

```
device-cloud-saas/
├── apps/
│   ├── web/                # 前端应用（Vue 3）
│   │   ├── src/
│   │   │   ├── api/        # API 接口
│   │   │   ├── components/ # 组件（core/custom）
│   │   │   ├── composables/# 组合式函数
│   │   │   ├── store/      # Pinia 状态管理
│   │   │   ├── types/api/  # API 类型定义
│   │   │   └── views/      # 页面视图
│   │   │       └── development/ # 业务模块目录（历史命名）
│   │   └── package.json
│   └── backend/            # 后端应用（Laravel 12）
│       ├── app/
│       │   ├── Http/       # 控制器、请求、资源
│       │   ├── Models/     # Eloquent 模型
│       │   └── Services/   # 业务逻辑层
│       └── database/
│           └── migrations/ # 数据库迁移文件
├── docs/                   # 项目文档
│   ├── web-development-guide.md    # Web 端开发规范
│   └── backend-development-guide.md # 后端开发规范
└── CLAUDE.md              # 项目指南（本文档）
```

**前端目录说明**：
- `views/development/` 包含所有业务模块，名称保持历史原因
- 实际菜单结构由后端 MenuSeeder 动态配置
- 文件路径与菜单结构解耦，便于灵活调整

## 📚 开发文档

### 前端开发
**详细规范**：[`/docs/web-development-guide.md`](./docs/web-development-guide.md)

⚠️ 前端开发时必须严格遵循 Web 端开发规范，包含：
- 完整开发流程（5 步）
- Vue 3 组合式 API 最佳实践
- Element Plus 组件使用
- TypeScript 类型定义
- 代码质量检查

### 后端开发
**详细规范**：[`/docs/backend-development-guide.md`](./docs/backend-development-guide.md)

⚠️ 后端开发时必须严格遵循后端开发规范，包含：
- Laravel 12 开发模式
- RESTful API 设计
- Docker 容器使用
- 数据库迁移管理
- Service Layer 架构

## 🎯 功能模块

### ✅ 已完成功能

#### 系统管理
- **用户管理**：用户 CRUD、角色分配、头像上传
- **角色管理**：角色 CRUD、权限分配
- **菜单管理**：动态路由、权限控制、自动生成技术字段
- **字典管理**：系统配置字典、枚举值管理

#### 业务功能
- **分类管理**：无限级树形结构、拖拽排序
- **相关方管理**：多联系人、附件支持
- **生命周期管理**：项目跟踪、跟进记录
- **资产管理**：三级分类、地区选择、二维码展示

#### 基础能力
- **附件管理**：OSS/本地存储、MD5 秒传、多态关联
- **地区管理**：省市区三级联动（3,597 条数据）
- **二维码生成**：自定义样式、Base64 输出

### 🧰 核心特性

- **统一规范**：前后端 API 字段统一使用 `snake_case`
- **动态菜单**：菜单结构由后端 MenuSeeder 配置，前端自动渲染
- **路径解耦**：前端文件路径与菜单结构独立，通过路由名称关联
- **树形数据**：通用 `buildTree` 函数处理树形结构
- **附件处理**：`useAttachment` composable 智能处理附件
- **粘贴上传**：支持 Ctrl+V 粘贴文件上传
- **地区选择**：`RegionSelector` 组件省市区联动
- **权限控制**：基于 Laravel Sanctum 的 API 认证
- **考勤管理**：作为生命周期子功能，支持多态关联

### 💡 技术亮点

#### 前端技术
- **智能表格管理**：`useTable` composable 提供缓存策略、智能防抖、性能优化
- **高级文件上传**：
  - 阿里云 OSS 直传支持（STS 临时凭证）
  - MD5 秒传机制（避免重复上传）
  - 粘贴上传支持（Ctrl+V）
  - 大文件分片上传
- **性能优化**：
  - TableCache 缓存机制（5分钟缓存策略）
  - 智能防抖（createSmartDebounce）
  - 生命周期钩子（onSuccess、onError、onCacheHit）

#### 后端技术
- **附件系统**：
  - `HasAttachments` Trait 多态关联
  - 支持本地存储和阿里云 OSS
  - 批量附件处理和分类管理
- **中间件体系**：
  - `OperationLogMiddleware` 自动记录操作日志
  - `MenuPermissionMiddleware` 菜单权限检查
  - `ForceJsonResponse` 强制 JSON 响应
- **权限管理**：
  - 精细化权限控制（菜单级、操作级）
  - 权限与 API 路由绑定
  - 角色权限动态配置

#### 部署架构
- **容器化部署**：Docker（PHP 8.3 + Nginx）
- **开发容器**：`ty-php-8.3`、`ty-mysql-8.4.5`
- **生产容器**：`device-cloud-saas`（docker-compose）

## 📊 数据架构

数据表结构查看 Laravel 迁移文件：
```
apps/backend/database/migrations/
```

### 核心数据表
- `users` - 用户表
- `roles` - 角色表
- `menus` - 菜单表
- `permissions` - 权限表
- `categories` - 分类表（树形）
- `dictionary_categories/items` - 字典表
- `entities/entity_contacts` - 相关方表
- `attachments/attachment_relations` - 附件表（多态）
- `lifecycles/lifecycle_follow_ups` - 生命周期表
- `assets` - 资产表
- `regions` - 地区表

## 🚀 快速开始

### 环境要求
- Node.js 20+（使用 fnm 管理）
- PHP 8.2+（Docker 容器）
- MySQL 8.4.5（Docker 容器）
- pnpm（包管理器）

### 开发指引

1. **了解项目**：阅读本文档了解项目概况
2. **查看规范**：
   - 前端开发查看 [Web 端开发规范](./docs/web-development-guide.md)
   - 后端开发查看 [后端开发规范](./docs/backend-development-guide.md)
3. **遵循流程**：严格按照规范文档中的开发流程操作
4. **代码质量**：提交前运行相应的质量检查命令

### 架构说明

- **前端文件组织**：业务模块统一放在 `views/development/` 目录下（历史命名）
- **菜单配置**：通过后端 `MenuSeeder.php` 调整，无需移动前端文件
- **路由标识**：使用路由名称（如 `asset.index`）关联前后端，而非文件路径

## 🔍 API 文档

- **在线查看**：http://localhost/docs
- **OpenAPI 规范**：`apps/backend/storage/app/private/scribe/openapi.yaml`
- **Postman 集合**：`apps/backend/storage/app/private/scribe/collection.json`

## 📝 注意事项

- 所有技术实现细节请查看对应的开发规范文档
- 命令执行方式在各自的规范文档中说明
- 保持代码风格一致，遵循既定模式
- 使用 Context7 MCP 查询最新技术文档

---

**重要**：本文档仅提供项目概况，具体开发规范和技术细节请查看 `docs` 目录下的专门文档。