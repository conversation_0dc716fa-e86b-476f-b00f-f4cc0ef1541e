// 标签管理API接口
import type { TagListData, Tag, TagForm, TagSearchParams } from '@/types/api/tag'
import request from '@/utils/http'

/**
 * 获取标签列表（支持分页和搜索）
 */
export const getTagList = (params: TagSearchParams): Promise<TagListData> => {
  return request.get<TagListData>({
    url: '/api/admin/tags',
    params: {
      ...params,
      per_page: params.per_page || params.size || 20
    }
  })
}

/**
 * 创建新标签
 */
export const createTag = (data: TagForm): Promise<Tag> => {
  return request.post<Tag>({
    url: '/api/admin/tags',
    data
  })
}

/**
 * 更新标签
 */
export const updateTag = (id: number, data: TagForm): Promise<Tag> => {
  return request.put<Tag>({
    url: `/api/admin/tags/${id}`,
    data
  })
}

/**
 * 删除标签
 */
export const deleteTag = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/tags/${id}`
  })
}
