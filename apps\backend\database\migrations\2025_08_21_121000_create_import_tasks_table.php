<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	public function up(): void
	{
		Schema::create('import_tasks', function (Blueprint $table) {
			$table->id();
			$table->string('type', 50)->comment('导入类型：asset/category/entity/user');
			$table->string('file_path', 500)->comment('导入文件路径');
			$table->string('original_filename', 255)->comment('原始文件名');
			$table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending')->comment('导入状态');
			$table->integer('total_rows')->default(0)->comment('总行数');
			$table->integer('success_rows')->default(0)->comment('成功行数');
			$table->integer('failed_rows')->default(0)->comment('失败行数');
			$table->json('error_details')->nullable()->comment('错误详情');
			$table->text('summary')->nullable()->comment('导入摘要');
			$table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
			$table->bigInteger('started_at')->nullable()->comment('开始时间');
			$table->bigInteger('completed_at')->nullable()->comment('完成时间');
			$table->bigInteger('created_at')->nullable()->comment('创建时间');
			$table->bigInteger('updated_at')->nullable()->comment('更新时间');

			$table->index(['type']);
			$table->index(['status']);
			$table->index(['created_by']);
			$table->comment('统一导入任务表');
		});
	}

	public function down(): void
	{
		Schema::dropIfExists('import_tasks');
	}
};
