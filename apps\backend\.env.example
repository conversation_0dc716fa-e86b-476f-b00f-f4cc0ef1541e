APP_NAME=厂商云管理系统
APP_ENV=production
APP_KEY=base64:R/gob4xCGvjYFG0ofLvc69q3bqcNyFa2TftCxRu3mb4=
APP_DEBUG=true
APP_URL=http://cloud.tecev.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

# Scribe Documentation Auth Key
SCRIBE_AUTH_KEY=5|rQ0MLxDp2ThCzt2d87EQLfqqwwTlKoWfanrDS3oH7983500b

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=device_cloud_saas
DB_USERNAME=root
DB_PASSWORD=root

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# 附件上传配置
ATTACHMENT_DRIVER=local
ATTACHMENT_MAX_SIZE=10485760
ATTACHMENT_QUICK_UPLOAD=true

# 阿里云OSS配置
ALIOSS_ACCESS_KEY_ID=
ALIOSS_ACCESS_KEY_SECRET=
ALIOSS_BUCKET=
ALIOSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
ALIOSS_PREFIX=attachments

VITE_APP_NAME="${APP_NAME}"
