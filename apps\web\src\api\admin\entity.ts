import request from '@/utils/http'
import type {
  Entity,
  EntityForm,
  EntitySearchParams,
  EntityPageResponse,
  Contact,
  ContactForm,
  ContactPageResponse,
  Brand,
  BrandForm,
  BrandPageResponse
} from '@/types/api'

// ========== 相关方管理 ==========

/**
 * 获取相关方分页列表
 */
export const getEntityList = (params: EntitySearchParams = {}): Promise<EntityPageResponse> => {
  return request.get<EntityPageResponse>({
    url: '/api/admin/entities',
    params: {
      ...params,
      per_page: params.size || params.per_page || 20,
      page: params.current || params.page || 1
    }
  })
}

/**
 * 获取相关方详情
 */
export const getEntityDetail = (id: string): Promise<Entity> => {
  return request.get<Entity>({
    url: `/api/admin/entities/${id}`
  })
}

/**
 * 创建相关方
 */
export const createEntity = (data: EntityForm): Promise<Entity> => {
  return request.post<Entity>({
    url: '/api/admin/entities',
    data
  })
}

/**
 * 更新相关方
 */
export const updateEntity = (id: string, data: EntityForm): Promise<Entity> => {
  return request.put<Entity>({
    url: `/api/admin/entities/${id}`,
    data
  })
}

/**
 * 删除相关方
 */
export const deleteEntity = (id: string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/entities/${id}`
  })
}

// ========== 联系人管理 ==========

/**
 * 获取相关方的联系人列表
 */
export const getEntityContacts = (
  entityId: string,
  params: { current?: number; size?: number } = {}
): Promise<ContactPageResponse> => {
  return request
    .get<ContactPageResponse>({
      url: `/api/admin/entities/${entityId}/contacts`,
      params: {
        page: params.current,
        per_page: params.size
      }
    })
    .then((response: any) => {
      // 将后端响应格式转换为前端期望的格式
      const data = response.data.map((item: any) => ({
        ...item,
        createTime: item.createdAt || item.createTime
      }))

      return {
        data,
        total: response.meta.total || 0,
        current: response.meta.current_page || 1,
        size: response.meta.per_page || 10
      }
    })
}

/**
 * 创建联系人
 */
export const createContact = (entityId: string, data: ContactForm): Promise<Contact> => {
  return request.post<Contact>({
    url: `/api/admin/entities/${entityId}/contacts`,
    data: {
      name: data.name,
      phone: data.phone,
      position: data.position,
      department: data.department
    }
  })
}

/**
 * 更新联系人
 */
export const updateContact = (
  entityId: string,
  contactId: string,
  data: ContactForm
): Promise<Contact> => {
  return request.put<Contact>({
    url: `/api/admin/entities/${entityId}/contacts/${contactId}`,
    data: {
      name: data.name,
      phone: data.phone,
      position: data.position,
      department: data.department
    }
  })
}

/**
 * 删除联系人
 */
export const deleteContact = (entityId: string, contactId: string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/entities/${entityId}/contacts/${contactId}`
  })
}

// ========== 品牌管理 ==========

/**
 * 获取相关方的品牌列表
 */
export const getEntityBrands = (
  entityId: string,
  params: { current?: number; size?: number } = {}
): Promise<BrandPageResponse> => {
  return request
    .get<BrandPageResponse>({
      url: `/api/admin/entities/${entityId}/brands`,
      params: {
        page: params.current,
        per_page: params.size
      }
    })
    .then((response: any) => {
      // 将后端响应格式转换为前端期望的格式
      const data = response.data.map((item: any) => ({
        ...item,
        created_at: item.created_at || item.createTime
      }))

      return {
        data,
        total: response.meta.total || 0,
        current: response.meta.current_page || 1,
        size: response.meta.per_page || 10
      }
    })
}

/**
 * 创建品牌
 */
export const createBrand = (entityId: string, data: BrandForm): Promise<Brand> => {
  return request.post<Brand>({
    url: `/api/admin/entities/${entityId}/brands`,
    data: {
      name: data.name,
      logo_id: data.logo_id,
      description: data.description,
      sort_order: data.sort_order
    }
  })
}

/**
 * 更新品牌
 */
export const updateBrand = (entityId: string, brandId: string, data: BrandForm): Promise<Brand> => {
  return request.put<Brand>({
    url: `/api/admin/entities/${entityId}/brands/${brandId}`,
    data: {
      name: data.name,
      logo_id: data.logo_id,
      description: data.description,
      sort_order: data.sort_order
    }
  })
}

/**
 * 删除品牌
 */
export const deleteBrand = (entityId: string, brandId: string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/entities/${entityId}/brands/${brandId}`
  })
}
