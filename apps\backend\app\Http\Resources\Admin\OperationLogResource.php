<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OperationLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'account' => $this->user->account,
            'user_name' => $this->user_name,
            'menu_name' => $this->menu_name,
            'operation_type' => $this->operation_type,
            'operation_description' => $this->operation_description,
            'method' => $this->method,
            'path' => $this->path,
            'ip' => $this->ip,
            'params' => $this->params,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
