<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use OSS\Core\OssException;
use OSS\OssClient;

class OssServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        $this->app->singleton('oss', function ($app) {
            $accessKeyId = config('filesystems.disks.oss.key');
            $accessKeySecret = config('filesystems.disks.oss.secret');
            $endpoint = config('filesystems.disks.oss.endpoint');
            // $bucket = config('filesystems.disks.oss.bucket');
            try {
                return new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            } catch (OssException $e) {
                throw new \Exception($e->getMessage());
            }
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
