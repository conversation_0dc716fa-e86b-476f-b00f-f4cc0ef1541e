<template>
  <div class="user-select-transfer">
    <ElTransfer
      v-model="selectedIds"
      :data="transferData"
      :titles="['可选用户', '已选用户']"
      :render-content="renderFunc"
      :props="transferProps"
      filterable
      :filter-placeholder="'搜索用户'"
      :button-texts="['移除', '选择']"
      style="display: inline-block; text-align: left"
      @change="handleChange"
    />
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'UserSelectTransfer' })

  // Vue 核心
  import { ref, computed, h } from 'vue'

  // UI 框架
  import { ElTransfer, ElAvatar } from 'element-plus'
  import { UserFilled } from '@element-plus/icons-vue'
  import type { TransferDataItem } from 'element-plus'

  // 类型定义
  import type { UserOption } from '@/types/api/attendance'

  // Props
  const props = defineProps<{
    users: UserOption[]
  }>()

  // 使用 defineModel 简化 v-model
  const selectedIds = defineModel<number[]>({ default: () => [] })

  // Transfer 组件的数据格式
  const transferData = computed<TransferDataItem[]>(() => {
    return props.users.map((user) => ({
      key: user.id,
      label: user.nickname,
      disabled: false,
      // 存储完整的用户信息，供自定义渲染使用
      avatar: user.avatar
    }))
  })

  // Transfer 组件的字段映射
  const transferProps = {
    key: 'key',
    label: 'label',
    disabled: 'disabled'
  }

  // 自定义渲染函数
  // Element Plus Transfer 的 render-content 接收两个参数：(h, option)
  // 第一个参数是渲染函数，第二个参数才是数据对象
  const renderFunc = (_h: any, option: any) => {
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          padding: '4px 0'
        }
      },
      [
        h(ElAvatar, {
          src: option.avatar,
          size: 24,
          icon: UserFilled,
          style: {
            marginRight: '8px'
          }
        }),
        h(
          'span',
          {
            style: {
              fontSize: '14px',
              color: 'var(--el-text-color-primary)'
            }
          },
          option.label || '未知用户'
        )
      ]
    )
  }

  // 处理选择变化
  const handleChange = (value: Array<string | number>) => {
    selectedIds.value = value as number[]
  }
</script>

<style lang="scss" scoped>
  .user-select-transfer {
    width: 100%;

    :deep(.el-transfer) {
      .el-transfer-panel {
        width: 250px;

        .el-transfer-panel__header {
          padding: 12px 15px;
          background-color: var(--el-fill-color-light);

          .el-checkbox {
            .el-checkbox__label {
              font-weight: 500;
            }
          }
        }

        .el-transfer-panel__body {
          height: 300px;

          .el-transfer-panel__filter {
            padding: 15px;
            border-bottom: 1px solid var(--el-border-color-lighter);
          }

          .el-transfer-panel__list {
            padding: 0;

            .el-checkbox {
              display: block;
              padding: 8px 15px;
              margin: 0;

              &:hover {
                background-color: var(--el-fill-color-lighter);
              }

              .el-checkbox__input {
                margin-right: 8px;
              }

              .el-checkbox__label {
                flex: 1;
                font-size: 14px;
                line-height: 1.5;
              }
            }
          }
        }
      }

      .el-transfer__buttons {
        padding: 0 20px;

        .el-button {
          display: block;
          padding: 8px 12px;
          margin: 0 0 6px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>
