<template>
  <ElDialog v-model="visible" title="批量打印二维码" width="800px" :close-on-click-modal="false">
    <div class="qrcode-batch-print">
      <!-- 设置区域 -->
      <div class="settings-section">
        <ElForm :model="settings" label-width="100px" inline>
          <ElFormItem label="打印尺寸">
            <ElRadioGroup v-model="settings.size">
              <ElRadio value="small">小 (4×6)</ElRadio>
              <ElRadio value="medium">中 (3×4)</ElRadio>
              <ElRadio value="large">大 (2×3)</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="显示选项">
            <ElCheckbox v-model="settings.showName">显示资产名称</ElCheckbox>
            <ElCheckbox v-model="settings.showModel">显示型号</ElCheckbox>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 预览区域 -->
      <div class="preview-section">
        <div class="preview-header">
          <h4>预览 (共{{ assets.length }}个二维码)</h4>
          <ElButton
            type="primary"
            :loading="printLoading"
            @click="handlePrint"
            :disabled="qrcodeList.length === 0"
          >
            <ElIcon><Printer /></ElIcon>
            打印二维码
          </ElButton>
        </div>

        <!-- 加载状态 -->
        <div v-if="generating" class="loading-container">
          <ElSkeleton :rows="3" animated />
          <div class="loading-text"
            >正在生成二维码... ({{ generatedCount }}/{{ assets.length }})</div
          >
        </div>

        <!-- 预览网格 -->
        <div v-else class="preview-grid" :class="[`size-${settings.size}`]" id="print-content">
          <div v-for="item in qrcodeList" :key="item.id" class="qrcode-item">
            <div class="qrcode-container">
              <img :src="item.qrcode" :alt="`资产${item.id}二维码`" class="qrcode-image" />
            </div>
            <div class="qrcode-info">
              <div class="asset-id">ID: {{ item.id }}</div>
              <div v-if="settings.showName && item.name" class="asset-name">{{ item.name }}</div>
              <div v-if="settings.showModel && item.model" class="asset-model">{{
                item.model
              }}</div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!generating && qrcodeList.length === 0" class="empty-state">
          <ElEmpty description="暂无二维码数据" />
        </div>
      </div>
    </div>

    <template #footer>
      <ElButton @click="handleClose">关闭</ElButton>
      <ElButton
        type="primary"
        :loading="printLoading"
        @click="handlePrint"
        :disabled="qrcodeList.length === 0"
      >
        打印二维码
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'QrcodeBatchPrint' })

  // Vue 核心
  import { ref, reactive, computed, watch } from 'vue'

  // UI 框架
  import { ElMessage } from 'element-plus'
  import { Printer } from '@element-plus/icons-vue'

  // API
  import { generateQrCode } from '@/api/admin/qrcode'

  // 类型定义
  import type { Asset } from '@/types/api/asset'

  interface QrcodeItem {
    id: number
    name: string
    model: string
    qrcode: string
  }

  interface PrintSettings {
    size: 'small' | 'medium' | 'large'
    showName: boolean
    showModel: boolean
  }

  interface Props {
    visible: boolean
    assets: Asset[]
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
  }

  const props = withDefaults(defineProps<Props>(), {
    assets: () => []
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const settings = reactive<PrintSettings>({
    size: 'medium',
    showName: true,
    showModel: true
  })

  const generating = ref(false)
  const printLoading = ref(false)
  const generatedCount = ref(0)
  const qrcodeList = ref<QrcodeItem[]>([])

  /**
   * 生成二维码
   */
  const generateQrcodes = async () => {
    if (props.assets.length === 0) return

    generating.value = true
    generatedCount.value = 0
    qrcodeList.value = []

    try {
      // 并发生成二维码，限制并发数量
      const concurrency = 5 // 同时最多处理5个
      const results: QrcodeItem[] = []

      for (let i = 0; i < props.assets.length; i += concurrency) {
        const batch = props.assets.slice(i, i + concurrency)

        const batchPromises = batch.map(async (asset) => {
          try {
            const response = await generateQrCode({
              content: String(asset.id),
              size: 200, // 固定生成尺寸，显示时通过CSS控制
              margin: 2
            })

            generatedCount.value++

            return {
              id: asset.id,
              name: asset.name || '',
              model: asset.model || '',
              qrcode: response.qrcode
            }
          } catch (error) {
            console.error(`生成资产${asset.id}二维码失败:`, error)
            return null
          }
        })

        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults.filter((item): item is QrcodeItem => item !== null))
      }

      qrcodeList.value = results

      if (results.length < props.assets.length) {
        ElMessage.warning(
          `成功生成${results.length}个二维码，${props.assets.length - results.length}个生成失败`
        )
      } else {
        ElMessage.success(`成功生成${results.length}个二维码`)
      }
    } catch (error) {
      console.error('批量生成二维码失败:', error)
      ElMessage.error('批量生成二维码失败')
    } finally {
      generating.value = false
    }
  }

  /**
   * 打印处理
   */
  const handlePrint = () => {
    if (qrcodeList.value.length === 0) {
      ElMessage.warning('没有可打印的二维码')
      return
    }

    printLoading.value = true

    try {
      // 确保所有图片都已加载
      const images = document.querySelectorAll<HTMLImageElement>('#print-content .qrcode-image')
      const imagePromises = Array.from(images).map((img) => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve(true)
          } else {
            img.onload = () => resolve(true)
            img.onerror = () => resolve(true)
          }
        })
      })

      Promise.all(imagePromises).then(() => {
        // 创建打印窗口
        const printWindow = window.open('', '_blank')
        if (!printWindow) {
          ElMessage.error('无法打开打印窗口，请检查浏览器设置')
          return
        }

        // 获取打印内容
        const printContent = document.getElementById('print-content')?.innerHTML || ''

        // 构建打印页面HTML
        const printHTML = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <title>批量打印二维码</title>
            <style>
              ${getPrintStyles()}
            </style>
          </head>
          <body>
            <div class="print-wrapper">
              <div class="preview-grid size-${settings.size}">
                ${printContent}
              </div>
            </div>
          </body>
          </html>
        `

        printWindow.document.write(printHTML)
        printWindow.document.close()

        // 等待内容加载完成后打印
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 500)
      })
    } catch (error) {
      console.error('打印失败:', error)
      ElMessage.error('打印失败')
    } finally {
      printLoading.value = false
    }
  }

  /**
   * 获取打印样式
   */
  const getPrintStyles = () => {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        background: white;
      }

      .print-wrapper {
        width: 100%;
        padding: 10mm;
      }

      .preview-grid {
        display: grid;
        gap: 8mm;
        width: 100%;
      }

      .preview-grid.size-small {
        grid-template-columns: repeat(6, 1fr);
      }

      .preview-grid.size-medium {
        grid-template-columns: repeat(4, 1fr);
      }

      .preview-grid.size-large {
        grid-template-columns: repeat(3, 1fr);
      }

      .qrcode-item {
        page-break-inside: avoid;
        break-inside: avoid;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        text-align: center;
        background: white;
      }

      .qrcode-container {
        margin-bottom: 6px;
      }

      .qrcode-image {
        width: 100%;
        max-width: 80px;
        height: auto;
        display: block;
        margin: 0 auto;
      }

      .size-small .qrcode-image {
        max-width: 60px;
      }

      .size-large .qrcode-image {
        max-width: 100px;
      }

      .qrcode-info {
        font-size: 12px;
        color: #333;
        line-height: 1.4;
      }

      .asset-id {
        font-weight: bold;
        margin-bottom: 2px;
      }

      .asset-name {
        font-size: 10px;
        color: #666;
        margin-bottom: 1px;
        word-break: break-all;
      }

      .asset-model {
        font-size: 10px;
        color: #666;
        word-break: break-all;
      }

      @page {
        margin: 10mm;
        size: A4;
      }

      @media print {
        .preview-grid {
          gap: 6mm;
        }
        
        .qrcode-item {
          padding: 6px;
        }
      }
    `
  }

  /**
   * 关闭对话框
   */
  const handleClose = () => {
    visible.value = false
  }

  // 移除assets watch，避免与visible watch重复触发
  // 因为每次打开弹窗时assets都是最新的，只监听visible即可

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible && props.assets.length > 0) {
        generateQrcodes()
      }
    }
  )
</script>

<style lang="scss" scoped>
  .qrcode-batch-print {
    .settings-section {
      padding: 16px;
      margin-bottom: 20px;
      background: #f9f9f9;
      border-radius: 8px;

      :deep(.el-form-item) {
        margin-bottom: 12px;
      }

      :deep(.el-checkbox) {
        margin-right: 16px;
      }
    }

    .preview-section {
      .preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 12px;
        margin-bottom: 16px;
        border-bottom: 1px solid #ebeef5;

        h4 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }
      }

      .loading-container {
        padding: 20px;
        text-align: center;

        .loading-text {
          margin-top: 16px;
          font-size: 14px;
          color: #606266;
        }
      }

      .preview-grid {
        display: grid;
        gap: 12px;
        max-height: 400px;
        padding: 12px;
        overflow-y: auto;
        background: #fafafa;
        border: 1px solid #ebeef5;
        border-radius: 8px;

        &.size-small {
          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        }

        &.size-medium {
          grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        }

        &.size-large {
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        }

        .qrcode-item {
          padding: 12px;
          text-align: center;
          background: white;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--el-color-primary);
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
          }

          .qrcode-container {
            margin-bottom: 8px;

            .qrcode-image {
              display: block;
              width: 100%;
              max-width: 80px;
              height: auto;
              margin: 0 auto;
              border-radius: 4px;
            }
          }

          .qrcode-info {
            font-size: 12px;
            line-height: 1.5;
            color: #606266;

            .asset-id {
              margin-bottom: 4px;
              font-weight: 600;
              color: #303133;
            }

            .asset-name {
              margin-bottom: 2px;
              font-size: 11px;
              color: #409eff;
              word-break: break-all;
            }

            .asset-model {
              font-size: 11px;
              color: #909399;
              word-break: break-all;
            }
          }
        }
      }

      .empty-state {
        padding: 40px;
        text-align: center;
      }
    }
  }

  // 隐藏打印时不需要的元素
  @media print {
    .el-dialog__header,
    .el-dialog__footer,
    .settings-section,
    .preview-header {
      display: none !important;
    }
  }
</style>
