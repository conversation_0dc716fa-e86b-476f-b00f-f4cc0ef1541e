<template>
  <ElDrawer
    :model-value="visible"
    @update:model-value="handleVisibleChange"
    :title="`${entityName} - 品牌管理`"
    size="50%"
    direction="rtl"
    destroy-on-close
  >
    <div class="brand-drawer-content">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="showDialog('add')" v-ripple>
            <ElIcon><Plus /></ElIcon>
            新增品牌
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 品牌表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id', height: 'calc(100vh - 200px)' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- Logo列 -->
        <template #logo="{ row }">
          <ElAvatar
            :size="30"
            :src="row.logo"
            :alt="row.name"
            style="color: #909399; background-color: #f5f7fa"
          >
            {{ row.name?.charAt(0) || '?' }}
          </ElAvatar>
        </template>

        <!-- 描述列 -->
        <template #description="{ row }">
          <span>{{ row.description || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="edit" @click="showDialog('edit', row)" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 新增/编辑品牌对话框 -->
      <BrandFormDialog
        v-model:visible="dialogVisible"
        v-model:brand="selectedBrand"
        :entity-id="entityId"
        @success="handleDialogSuccess"
      />
    </div>
  </ElDrawer>
</template>

<script setup lang="ts">
  defineOptions({ name: 'BrandDrawer' })

  // Vue 核心
  import { ref, watch } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox, ElAvatar } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  // 工具库
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import BrandFormDialog from './BrandFormDialog.vue'

  // API
  import { getEntityBrands, deleteBrand } from '@/api/admin/entity'
  import type { Brand } from '@/types/api'

  interface Props {
    visible: boolean
    entityId: string
    entityName: string
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['update:visible', 'refresh'])

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedBrand = ref<Brand | null>(null)

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange
  } = useTable<Brand>({
    core: {
      apiFn: async (params: any) => {
        if (!props.entityId) {
          return { records: [], total: 0, current: 1, size: 20 }
        }

        const response = await getEntityBrands(props.entityId, {
          current: params.current,
          size: params.size
        })

        return {
          records: response.data,
          total: response.total,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10
      },
      columnsFactory: () => [
        {
          prop: 'logo',
          label: 'Logo',
          width: 80,
          useSlot: true
        },
        { prop: 'name', label: '品牌名称', width: 150 },
        {
          prop: 'description',
          label: '品牌描述',
          minWidth: 200,
          useSlot: true
        },
        { prop: 'sort_order', label: '排序', width: 80 },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 180,
          formatter: (row: any) => {
            return row.created_at || row.createTime
              ? formatDate(row.created_at || row.createTime, 'YYYY-MM-DD HH:mm:ss')
              : '-'
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: false
    }
  })

  // 处理对话框显示状态变化
  const handleVisibleChange = (val: boolean) => {
    emit('update:visible', val)
  }

  // 显示对话框
  const showDialog = (type: 'add' | 'edit', row?: Brand) => {
    if (type === 'edit' && row) {
      selectedBrand.value = row
    } else {
      selectedBrand.value = null
    }
    dialogVisible.value = true
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    const isAdd = !selectedBrand.value?.id
    if (isAdd) {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 删除品牌
  const handleDelete = async (row: Brand) => {
    try {
      await ElMessageBox.confirm(`确定要删除品牌"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteBrand(props.entityId, String(row.id))
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 监听对话框显示状态，加载数据
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal && props.entityId) {
        refreshAll()
      }
    }
  )

  // 监听 entityId 变化
  watch(
    () => props.entityId,
    (newVal) => {
      if (newVal && props.visible) {
        refreshAll()
      }
    }
  )
</script>

<style lang="scss" scoped>
  .brand-drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;

    .art-table {
      flex: 1;
    }
  }
</style>
