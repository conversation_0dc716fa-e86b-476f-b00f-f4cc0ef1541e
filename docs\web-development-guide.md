# Web端开发规范指南

⚠️ **重要提示**：开发前端功能时必须严格遵循本规范，确保代码质量和团队协作的一致性。

本文档为前端开发人员提供核心开发规范和最佳实践。

## 📋 目录

1. [快速开始](#快速开始)
2. [开发流程](#开发流程)
3. [核心规范](#核心规范)
4. [常见问题](#常见问题)
5. [最佳实践](#最佳实践)

## 快速开始

### 项目结构

```
apps/web/src/
├── api/         # API接口
├── components/  # 组件(core/custom)
├── composables/ # 组合式函数
├── store/       # Pinia状态管理
├── types/api/   # API类型定义
└── views/       # 页面视图
```

### 技术栈

- Vue 3 + TypeScript + Element Plus + Vite + Pinia
- HTTP请求: `@/utils/http` (基于axios)

### 核心约定

**API字段命名**: 统一使用 `snake_case`

```typescript
// ✅ 正确
interface User {
  user_name: string
  created_at: string
}
```

## 开发流程

### 1. 创建类型定义

```typescript
// src/types/api/tenant.ts
export interface Tenant {
  id: number
  name: string
  code: string
  category: string
  // ...
}

export interface TenantPageResponse {
  data: Tenant[]
  meta: {
    total: number
    current_page: number
    per_page: number
  }
}

// 记得在 types/api/index.ts 导出
export * from './tenant'
```

### 2. 创建API接口

```typescript
// src/api/admin/tenantApi.ts
import request from '@/utils/http'
import type { Tenant, TenantPageResponse } from '@/types/api'

export const getTenantList = (params: any): Promise<TenantPageResponse> => {
  return request.get<TenantPageResponse>({
    url: '/admin/tenants',
    params: {
      ...params,
      // useTable使用current/size，后端使用page/per_page
      page: params.current || 1,
      per_page: params.size || 20
    }
  })
}

// 注意：删除使用 request.del 而非 request.delete
export const deleteTenant = (id: number): Promise<void> => {
  return request.del<void>({ url: `/admin/tenants/${id}` })
}
```

### 3. 创建页面组件

#### 目录结构

```
src/views/system/tenant/
├── index.vue
└── components/
    ├── TenantFormDialog.vue
    └── UserManageDialog.vue
```

#### 列表页面核心代码

```vue
<template>
  <div class="tenant-page art-page-view">
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
    />
    <ElCard shadow="never" class="art-table-card">
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="handleAdd">新增</ElButton>
        </template>
      </ArtTableHeader>
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { useTable } from '@/composables/useTable'
  import type { Tenant } from '@/types/api'

  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData
  } = useTable<Tenant>({
    core: {
      apiFn: async (params: any) => {
        const response = await getTenantList(params)
        return {
          records: response.data,
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: { current: 1, size: 20 },
      columnsFactory: () => [
        { prop: 'id', label: 'ID', width: 80 },
        { prop: 'name', label: '租户名称' }
        // ...
      ]
    }
  })
</script>
```

#### useTable 高级配置

`useTable` composable 提供了丰富的配置选项来优化表格性能：

```typescript
const { ... } = useTable<T>({
  core: {
    apiFn: (params) => Promise<TableResponse>,
    apiParams: { current: 1, size: 20 },
    columnsFactory: () => Column[]
  },
  performance: {
    enableCache: true,        // 启用缓存
    cacheTime: 300000,       // 缓存时间（5分钟）
    debounceTime: 300,       // 防抖延迟
    maxCacheSize: 50,        // 最大缓存条数
    enableLog: false,        // 调试日志
    logLevel: 'error'        // 日志级别
  },
  hooks: {
    onSuccess: (data) => {},    // 成功回调
    onError: (error) => {},     // 错误回调
    onCacheHit: (data) => {},   // 缓存命中回调
    onLoading: (loading) => {}  // 加载状态回调
  }
})
```

**性能优化特性**：
- **TableCache 缓存机制**：智能缓存查询结果，减少 API 请求
- **智能防抖**：使用 `createSmartDebounce` 避免频繁请求
- **缓存失效策略**：自动管理缓存大小和过期时间
- **生命周期钩子**：精确控制数据加载流程

### 4. 配置路由

1. 更新 `routesAlias.ts` 添加路由别名
2. 更新 `asyncRoutes.ts` 添加路由配置
3. 更新 `zh.json` 添加中文翻译

```typescript
// src/router/routesAlias.ts
export const RoutesAlias = {
  // ...
  Tenant: () => import('@/views/system/tenant/index.vue')
}

// src/router/routes/asyncRoutes.ts
{
  path: 'tenant',
  name: 'Tenant',
  component: RoutesAlias.Tenant,
  meta: {
    title: 'menus.system.tenant',
    keepAlive: true
  }
}
```

### 5. 添加菜单（后端配置）

在 `MenuSeeder.php` 中添加菜单项，然后执行：

```bash
# 重置数据库、运行种子、生成枚举和文档（开发环境一键执行）
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan migrate:fresh --seed && php artisan dictionary:generate-enums && php artisan scribe:generate"
```

## 核心规范

### 1. 项目一致性优先

- 遵循现有规范，不为新特性破坏一致性
- 通过组合式函数优化，而非激进重构
- 重大改动需团队共识

### 2. 组件导入顺序

```vue
<script setup lang="ts">
  // 1. Vue核心
  import { ref, reactive } from 'vue'
  // 2. UI框架
  import { ElMessage } from 'element-plus'
  // 3. 内部hooks
  import { useTable } from '@/composables/useTable'
  // 4. 内部组件
  import { ArtTable } from '@/components'
  // 5. API
  import { getUsers } from '@/api/admin/userApi'
  // 6. 类型
  import type { User } from '@/types/api'
</script>
```

### 3. 字典数据获取

```typescript
// ✅ 使用 Pinia Store（有缓存）
import { useDictionaryStore } from '@/store/modules/dictionary'

const dictionaryStore = useDictionaryStore()
const tenantCategories = await dictionaryStore.fetchItemsByCode('tenant_category')

// ❌ 直接调用 API（无缓存）
const items = await getDictionaryByCode('tenant_category')
```

### 4. 下拉框初始值使用规范 ⚠️

**重要**：必须根据使用场景选择正确的初始值，否则会导致 API 请求异常！

#### 4.1 表单提交场景 - 使用 `null`

用于创建/编辑表单，需要将所有字段发送到后端：

```typescript
// ✅ 表单数据 - 使用 null
const formData = ref({
  name: '', // 文本框用空字符串
  category: null as string | null, // 下拉框用 null（会发送到后端）
  status: null as number | null, // 状态用 null（明确表示未选择）
  attachments: [] // 数组用空数组
})

// 提交时会发送: { name: '', category: null, status: null, attachments: [] }
```

#### 4.2 搜索过滤场景 - 使用 `undefined`

用于列表页搜索条件，只发送用户选择的过滤条件：

```typescript
// ✅ 搜索参数 - 使用 undefined
const searchParams = ref({
  keyword: '', // 文本搜索保持空字符串
  status: undefined as string | undefined, // 下拉框用 undefined（不会发送）
  category: undefined as string | undefined // 分类用 undefined（不会发送）
})

// 用户未选择时只发送: { keyword: '' }
// 用户选择后发送: { keyword: '', status: 'active' }
```

#### 4.3 为什么要区分？

1. **`undefined` 的特性**：

   - `JSON.stringify()` 会忽略值为 `undefined` 的字段
   - 搜索时不会发送无用的过滤条件，减少网络负载
   - 后端不需要处理空的过滤参数

2. **`null` 的必要性**：
   - 表单提交需要发送完整的数据结构
   - 某些字段可能需要从"有值"改为"无值"
   - 后端需要区分"未传递"和"传递了空值"

#### 4.4 实际示例对比

```typescript
// 搜索场景 - 只发送有值的条件
const search = { keyword: 'test', status: undefined, type: undefined }
// 实际发送: { keyword: 'test' } ✅ 精简

// 表单场景 - 发送所有字段
const form = { name: '测试', status: null, type: null }
// 实际发送: { name: '测试', status: null, type: null } ✅ 完整
```

### 5. 错误处理

```typescript
// ❌ 错误 - 不需要 try-catch，HTTP拦截器已统一处理
try {
  await deleteUser(id)
  ElMessage.success('删除成功')
} catch (error) {
  ElMessage.error('删除失败')
}

// ✅ 正确 - 直接调用，错误会被拦截器处理
await deleteUser(id)
ElMessage.success('删除成功')
refreshAll()

// ✅ 如果需要特殊处理，可以使用 .then/.catch
deleteUser(id)
  .then(() => {
    ElMessage.success('删除成功')
    refreshAll()
  })
  .catch(() => {
    // 这里通常不需要显示错误，拦截器已处理
    // 只在需要特殊逻辑时使用
  })
```

## 常见问题

### 1. 下拉框 placeholder 不显示

```typescript
// ❌ 错误 - 使用空字符串
const formData = ref({ category: '' })

// ✅ 正确 - 表单场景使用 null
const formData = ref({ category: null as string | null })

// ⚠️ 注意：搜索场景应使用 undefined
const searchParams = ref({ category: undefined as string | undefined })
```

### 2. 组件导入错误

```typescript
// ❌ 错误 - 路径不存在
import UploadAttachment from '@/components/core/upload/uploadAttachment/index.vue'

// ✅ 正确 - 使用正确的导入
import Attachment from '@/components/custom/upload/Attachment.vue'
```

### 3. HTTP 请求方法

```typescript
// @/utils/http 提供的方法
request.get() // GET 请求
request.post() // POST 请求
request.put() // PUT 请求
request.del() // DELETE 请求（注意是 del 不是 delete）
```

### 4. useTable 参数转换

```typescript
// useTable使用current/size，后端使用page/per_page
const response = await getTenantList({
  ...params,
  page: params.current, // 转换参数名
  per_page: params.size
})
```

### 5. 附件上传处理

```typescript
// 组件使用
<Attachment
  v-model="formData.license_attachments"  // 数组
  :attachments="currentAttachments"
  :limit="1"
/>

// 提交时转换
const submitData = {
  license_attachment_id: formData.value.license_attachments[0] || null
}
```

### 6. 搜索表单重置

```typescript
// ❌ 错误 - 下拉框重置为空字符串或 null
const handleReset = () => {
  searchFormState.value = {
    keyword: '',
    type: '', // 错误：使用空字符串
    status: null // 错误：搜索场景不应使用 null
  }
}

// ✅ 正确 - 搜索场景的下拉框重置为 undefined
const handleReset = () => {
  searchFormState.value = {
    keyword: '', // 文本框保持空字符串
    type: undefined, // 下拉框用 undefined
    status: undefined // 重置后不会发送到后端
  }
}
```

### 7. 下拉框初始值混淆（null vs undefined）⚠️

```typescript
// ❌ 错误 - 混淆使用场景
const searchParams = ref({
  status: null // 搜索参数会发送 { status: null } 到后端
})

const formData = ref({
  status: undefined // 表单提交时字段会被忽略，导致数据不完整
})

// ✅ 正确 - 根据场景使用
const searchParams = ref({
  status: undefined // 搜索：不选择时不发送此字段
})

const formData = ref({
  status: null // 表单：明确发送"未选择"状态
})

// 记住口诀：搜索用 undefined，表单用 null
```

### 8. API 响应格式

后端统一返回格式，前端需正确解析：

```typescript
// 分页响应
interface PageResponse<T> {
  data: T[]
  meta: {
    total: number
    current_page: number
    per_page: number
  }
}

// useTable 适配
const response = await getTenantList(params)
return {
  records: response.data,
  total: response.meta?.total || 0,
  current: params.current,
  size: params.size
}
```

## 时间处理规范（后端要求）

### 1. 时间数据格式约定

根据后端要求，前后端时间数据交互遵循以下规范：

- **列表显示**：后端返回时间戳（如：`1753760515`），前端使用 `formatDate()` 格式化显示
- **表单提交**：前端提交时间戳（如：`1753760515`）给后端
- **搜索时间范围**：拆分为两个独立字段
  - `start_time`: 开始时间，格式 `YYYY-MM-DD` 或 `YYYY-MM-DD HH:mm:ss`
  - `end_time`: 结束时间，格式 `YYYY-MM-DD` 或 `YYYY-MM-DD HH:mm:ss`

### 2. 列表时间显示处理

```typescript
// 使用已有的格式化函数
import { formatDate } from '@/utils/dataprocess/format'

// 在表格列配置中
{
  prop: 'created_at',
  label: '创建时间',
  width: 180,
  formatter: (row) => {
    // 注意：根据后端返回的时间戳单位调整（秒级需要 * 1000）
    return row.created_at ? formatDate(row.created_at * 1000, 'YYYY-MM-DD HH:mm:ss') : ''
  }
}
```

### 3. 表单时间提交处理

```typescript
// 表单数据定义
const formData = ref({
  publish_time: null as number | null  // 时间戳
})

// 使用 DatePicker，配置 valueFormat 返回时间戳
<ElDatePicker
  v-model="formData.publish_time"
  type="datetime"
  placeholder="选择发布时间"
  :value-format="'X'"  // 'X' 表示秒级时间戳，'x' 表示毫秒级
/>

// 提交时直接发送时间戳
const submitData = {
  publish_time: formData.value.publish_time
}
```

### 4. 搜索时间范围处理

```typescript
// 搜索表单配置
const searchFormItems: SearchFormItem[] = [
  {
    prop: 'dateRange',
    label: '时间范围',
    type: 'date',
    config: {
      type: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD', // 或 'YYYY-MM-DD HH:mm:ss'
      shortcuts: [
        {
          text: '最近7天',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setDate(start.getDate() - 6)
            return [start, end]
          }
        },
        {
          text: '最近30天',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setDate(start.getDate() - 29)
            return [start, end]
          }
        }
      ]
    }
  }
]

// API 调用时的参数转换
const response = await getOperationLogs({
  ...params,
  // 将 dateRange 数组拆分为两个字段
  ...(params.dateRange &&
    params.dateRange.length === 2 && {
      start_time: params.dateRange[0],
      end_time: params.dateRange[1]
    })
})
```

### 5. 完整示例（参考操作日志页面）

```typescript
// 搜索表单状态
const searchFormState = ref({
  keyword: '',
  dateRange: [] // 用于临时存储日期范围
})

// useTable 配置
const { searchData } = useTable<Log>({
  core: {
    apiFn: async (params: any) => {
      // 处理时间范围
      const searchParams = {
        page: params.current,
        per_page: params.size,
        keyword: params.keyword
      }

      // 拆分日期范围为两个字段
      if (params.dateRange && params.dateRange.length === 2) {
        searchParams.start_time = params.dateRange[0]
        searchParams.end_time = params.dateRange[1]
      }

      const response = await getLogList(searchParams)
      return {
        records: response.data,
        total: response.meta?.total || 0,
        current: params.current,
        size: params.size
      }
    }
  }
})
```

### 6. 注意事项

- **时间戳单位**：确认后端使用的是秒级还是毫秒级时间戳
  - 秒级时间戳：使用 `valueFormat="X"`，显示时需要 `* 1000`
  - 毫秒级时间戳：使用 `valueFormat="x"`，显示时直接使用
- **时区处理**：所有时间默认使用服务器时区
- **空值处理**：时间字段为空时，表单提交 `null`，搜索条件不发送该字段
- **日期格式**：使用 dayjs 的格式化标准（YYYY-MM-DD HH:mm:ss）

## 附件处理规范

项目中多个地方都需要处理附件上传和显示，为了避免重复代码和统一处理逻辑，我们提供了专门的 `useAttachment` composable。

### 1. 后端与前端的数据约定

- **后端返回**：附件完整对象，包含 `id`、`file_name`、`file_url` 等字段
- **前端提交**：只发送附件ID或ID数组
- **AttachmentUpload 组件**：需要完整的附件对象来显示文件信息

### 2. 使用 useAttachment Composable

#### 2.1 基本用法

```typescript
import {
  useAttachmentField,
  useSingleAttachment,
  useMultipleAttachments
} from '@/composables/useAttachment'

// 单个附件
const {
  attachmentIds, // 用于 v-model 绑定
  attachmentDetails, // 用于 :attachments 属性
  setAttachments, // 设置附件数据
  getSubmitValue // 获取提交数据
} = useSingleAttachment()

// 多个附件
const { attachmentIds, attachmentDetails, setAttachments, getSubmitValue } =
  useMultipleAttachments()
```

#### 2.2 完整示例

```vue
<template>
  <ElFormItem label="系统Logo" prop="system_logo">
    <Attachment
      v-model="logoAttachments"
      :attachments="currentLogoAttachments"
      :limit="1"
      :accept="'image/*'"
      list-type="picture-card"
    />
  </ElFormItem>
</template>

<script setup lang="ts">
  import { useSingleAttachment } from '@/composables/useAttachment'
  import Attachment from '@/components/custom/upload/Attachment.vue'

  // 使用 composable 管理附件
  const {
    attachmentIds: logoAttachments,
    attachmentDetails: currentLogoAttachments,
    setAttachments: setLogoAttachments,
    getSubmitValue: getLogoSubmitValue
  } = useSingleAttachment()

  // 从后端加载数据时
  const loadData = async () => {
    const data = await fetchData()

    // 智能处理后端返回的附件数据（对象或ID）
    setLogoAttachments(data.system_logo)
  }

  // 提交表单时
  const submitForm = async () => {
    const formData = {
      // 其他字段...
      system_logo: getLogoSubmitValue() // 自动返回 ID 或 null
    }

    await saveData(formData)
  }
</script>
```

### 3. 工具函数

#### 3.1 extractAttachmentIds

从附件对象数组中提取ID数组：

```typescript
import { extractAttachmentIds } from '@/composables/useAttachment'

// 处理各种格式的附件数据
const ids1 = extractAttachmentIds([{ id: 1, file_name: 'test.jpg' }]) // [1]
const ids2 = extractAttachmentIds({ id: 2, file_name: 'test.png' }) // [2]
const ids3 = extractAttachmentIds([1, 2, 3]) // [1, 2, 3]
const ids4 = extractAttachmentIds(null) // []
```

#### 3.2 processAttachmentField

智能处理附件字段，返回统一格式：

```typescript
import { processAttachmentField } from '@/composables/useAttachment'

const { ids, details } = processAttachmentField(data.attachments)
// ids: 用于表单提交的ID数组
// details: 用于组件显示的完整对象数组
```

### 4. 不同场景的使用方式

#### 4.1 单个附件（如系统Logo、用户头像）

```typescript
const {
  attachmentIds: avatarIds,
  attachmentDetails: avatarDetails,
  setAttachments: setAvatar,
  getSubmitValue: getAvatarId
} = useSingleAttachment()

// 表单绑定
<Attachment
  v-model="avatarIds"
  :attachments="avatarDetails"
  :limit="1"
/>

// 提交时获取单个ID
const avatarId = getAvatarId() // 返回 number | null
```

#### 4.2 多个附件（如证书、文档）

```typescript
const {
  attachmentIds: docIds,
  attachmentDetails: docDetails,
  setAttachments: setDocs,
  getSubmitValue: getDocIds
} = useMultipleAttachments()

// 表单绑定
<Attachment
  v-model="docIds"
  :attachments="docDetails"
  :limit="5"
/>

// 提交时获取ID数组
const docIds = getDocIds() // 返回 number[]
```

### 5. 迁移现有代码

如果你有类似这样的复杂附件处理代码：

```typescript
// ❌ 旧代码 - 复杂的类型判断和转换
let logoId: number | null = null
let logoDetail: AttachmentItem | null = null

if (config.system_logo) {
  if (typeof config.system_logo === 'object' && 'id' in config.system_logo) {
    logoId = (config.system_logo as any).id
    logoDetail = config.system_logo as any as AttachmentItem
  } else if (typeof config.system_logo === 'number') {
    logoId = config.system_logo
  }
}

logoAttachments.value = logoId ? [logoId] : []
logoAttachmentDetails.value = logoDetail ? [logoDetail] : []
```

可以简化为：

```typescript
// ✅ 新代码 - 使用 composable 简化
const { setAttachments } = useSingleAttachment()
setAttachments(config.system_logo) // 自动处理所有情况
```

### 6. 性能优化建议

- composable 内部已优化，避免不必要的响应式更新
- 使用 `clearAttachments()` 方法清空附件，而不是手动重置
- 在组件卸载时自动清理，无需手动处理

### 7. 常见问题

#### Q: 后端返回的是ID，但组件需要完整对象怎么办？

A: 使用 `setAttachments()` 方法，如果只有ID，composable 会创建基本的详情对象供组件显示。

#### Q: 如何处理上传成功后的回调？

A: 使用 `addAttachmentDetail()` 方法添加新上传的附件详情。

#### Q: 单个附件和多个附件有什么区别？

A: 单个附件的 `getSubmitValue()` 返回 `number | null`，多个附件返回 `number[]`。

## 实用工具和组件

### 1. useUpload 文件上传

`useUpload` composable 提供了强大的文件上传功能：

#### 基础用法

```typescript
import { useUpload } from '@/composables/useUpload'

const { uploadFile, uploadProgress, isUploading } = useUpload()

// 上传文件
const result = await uploadFile(file, {
  type: 'image',           // 文件类型
  maxSize: 10 * 1024 * 1024, // 最大10MB
  directory: 'avatars'     // 上传目录
})
```

#### 高级特性

**1. OSS 直传**

```typescript
// 获取 STS 临时凭证
const credentials = await getSTSCredentials()

// 使用 OSSUploader 直传
const uploader = new OSSUploader(credentials)
await uploader.upload(file, {
  onProgress: (percent) => {
    console.log(`上传进度: ${percent}%`)
  }
})
```

**2. MD5 秒传**

```typescript
// 计算文件 MD5
const md5 = await calculateMD5(file)

// 确认是否需要上传
const { need_upload, url } = await confirmUpload({
  md5,
  filename: file.name
})

if (!need_upload) {
  // 文件已存在，直接使用 url
  return url
}
```

**3. 粘贴上传**

```typescript
// 监听粘贴事件
onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

const handlePaste = async (e: ClipboardEvent) => {
  const items = e.clipboardData?.items
  if (!items) return
  
  for (const item of items) {
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        await uploadFile(file)
      }
    }
  }
}
```

**4. 分片上传**

```typescript
// 大文件分片上传
const uploader = new OSSUploader(credentials, {
  partSize: 1024 * 1024 * 5,  // 5MB per part
  parallel: 3,                 // 3 parts in parallel
  retryTimes: 3                // Retry 3 times on failure
})

await uploader.multipartUpload(largeFile, {
  onProgress: (percent, checkpoint) => {
    // 保存断点，支持断点续传
    localStorage.setItem('upload-checkpoint', JSON.stringify(checkpoint))
  }
})
```

**上传配置选项**：
- `maxSize`: 文件大小限制
- `accept`: 允许的文件类型
- `multiple`: 是否多选
- `autoUpload`: 是否自动上传
- `beforeUpload`: 上传前钩子
- `onSuccess`: 成功回调
- `onError`: 错误回调

### 2. 树形数据处理

```typescript
import { buildTree } from '@/utils/dataprocess/tree'

// 将扁平数组转换为树形结构
const flatData = [
  { id: 1, name: '系统管理', parent_id: null },
  { id: 2, name: '用户管理', parent_id: 1 }
]
const treeData = buildTree(flatData)
```

### 3. 地区选择器

```vue
<template>
  <RegionSelector v-model="formData.region_code" placeholder="请选择省市区" :filterable="true" />
</template>

<script setup lang="ts">
  import { RegionSelector } from '@/components/custom/region-selector'
</script>
```

### 4. 二维码生成

```typescript
import { generateQRCode } from '@/api/admin/qrcodeApi'

const response = await generateQRCode({
  content: 'https://example.com',
  size: 200
})
const qrImage = `data:image/png;base64,${response.image}`
```

### 5. 菜单管理技术要点

- **自动生成字段**：路由地址、权限标识等字段在前端禁用，后端自动生成
- **AUTO\_ 前缀**：`AUTO_MENU_`、`/auto_path_`、`AUTO_PERM_` 标识需开发人员后续修改

### 6. 角色权限管理关键点

- **ElTree 父子联动**：有具体权限时，只勾选权限节点，不勾选菜单节点
- **空权限处理**：`permission_ids` 可为空数组，表示只有菜单访问权限

### 7. Vite 路径别名

项目配置了以下路径别名，可在开发中直接使用：

```typescript
// vite.config.ts 中定义的别名
@views   -> src/views
@imgs    -> src/assets/img  
@icons   -> src/assets/icons
@utils   -> src/utils
@stores  -> src/store
@comps   -> src/components
@api     -> src/api
@types   -> src/types

// 使用示例
import { useTable } from '@/composables/useTable'
import { UserApi } from '@api/admin/user'
import type { User } from '@types/api/user'
import { buildTree } from '@utils/dataprocess/tree'
```

### 8. 常用命令速查

```bash
# 一键重置数据库、生成枚举和文档
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan migrate:fresh --seed && php artisan dictionary:generate-enums && php artisan scribe:generate"

# 清除所有缓存
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan optimize:clear"
```

## 最佳实践

### 1. Core 目录规范

**重要**: `components/core` 目录下的代码是核心框架代码，请勿修改！

```typescript
// ❌ 错误 - 不要修改 core 目录下的文件
// src/components/core/layouts/art-header-bar/index.vue

// ✅ 正确 - 只修改 custom 目录或 views 目录下的文件
// src/components/custom/region-selector/index.vue
```

如需扩展核心组件功能，应该：

1. 在 `components/custom` 创建新组件
2. 继承或包装核心组件
3. 在业务代码中使用自定义组件

### 2. TypeScript 类型安全

#### 处理可空类型

```typescript
// ❌ 错误 - 没有处理 null 的情况
if (item.parent_id === categoryId) {
  // parent_id 可能是 null
}

// ✅ 正确 - 明确处理 null
if (item.parent_id === categoryId || (item.parent_id !== null && ids.has(item.parent_id))) {
  // 安全的类型检查
}
```

#### API 字段映射

```typescript
// ❌ 错误 - 字段名不匹配
const params = {
  asset_category_id: searchForm.asset_category_id // API 期望的是 asset_category_ids
}

// ✅ 正确 - 正确映射字段
const params = {
  asset_category_ids: searchForm.asset_category_id ? [searchForm.asset_category_id] : undefined
}
```

### 3. API 调用规范

**重要**: API 调用不需要 try-catch，HTTP 拦截器已统一处理错误！

```typescript
// ❌ 错误 - 不要使用 try-catch 包裹 API 调用
try {
  const data = await getUserList()
  // ...
} catch (error) {
  ElMessage.error('获取失败') // 拦截器已处理，会重复提示
}

// ✅ 正确 - 直接调用
const data = await getUserList()
// 处理成功逻辑

// ✅ 正确 - 操作类 API 调用
await deleteUser(id)
ElMessage.success('删除成功')
refreshAll()

// ✅ 正确 - 需要特殊错误处理时
deleteUser(id).catch(() => {
  // 仅在需要特殊逻辑时使用
  // 错误提示已由拦截器处理
})
```

**异常情况**: 只有在非 API 调用的异步操作中才使用 try-catch：

```typescript
// ✅ 文件操作、本地存储等需要 try-catch
try {
  const file = await readFile()
  // ...
} catch {
  ElMessage.error('文件读取失败')
}
```

### 4. Element Plus 组件类型

```typescript
// Transfer 组件事件类型
import type { TransferKey, TransferDirection } from 'element-plus'

const handleChange = (
  value: TransferKey[],
  direction: TransferDirection,
  movedKeys: TransferKey[]
) => {
  // TransferKey 可能是 string | number
  const ids = value.map((key) => Number(key))
}

// Tag 组件类型断言
return h(ElTag, { type: tagType as any }, () => content)
```

### 5. 使用 defineModel 简化组件（Vue 3.3+）

```typescript
// ❌ 传统方式
const props = defineProps<{ visible: boolean }>()
const emit = defineEmits<{ (e: 'update:visible', value: boolean): void }>()
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// ✅ 使用 defineModel
const visible = defineModel<boolean>('visible', { required: true })
const tenant = defineModel<Tenant | null>('tenant', { default: null })

// 直接使用
visible.value = false // 自动触发 update:visible
```

### 2. 弹窗状态管理

```typescript
// composables/useDialogState.ts
export function useDialogState<T = any>() {
  const visible = ref(false)
  const data = ref<T | null>(null)
  const loading = ref(false)

  const open = (item?: T) => {
    data.value = item || null
    visible.value = true
  }

  const close = () => {
    visible.value = false
    setTimeout(() => {
      data.value = null
      loading.value = false
    }, 300)
  }

  return { visible, data, loading, open, close }
}

// 使用
const tenantDialog = useDialogState<Tenant>()
<TenantFormDialog
  v-model:visible="tenantDialog.visible.value"
  v-model:tenant="tenantDialog.data.value"
/>
```

### 3. 组合式函数复用

```typescript
// composables/useDeleteConfirm.ts
export const useDeleteConfirm = () => {
  const handleDelete = async (
    message: string,
    deleteFunc: () => Promise<void>,
    successCallback?: () => void
  ) => {
    try {
      await ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // API 调用不需要 try-catch
      await deleteFunc()
      ElMessage.success('删除成功')
      successCallback?.()
    } catch {
      // 只处理用户取消操作，不处理 API 错误
      // API 错误由拦截器统一处理
    }
  }
  return { handleDelete }
}
```

### 4. 性能优化要点

- 使用 `v-loading` 显示加载状态
- 合理使用 `keep-alive` 缓存页面
- 大数据列表考虑虚拟滚动

### 5. 代码质量检查

#### 检查命令

```bash
pnpm typecheck      # TypeScript 类型检查
pnpm fix           # ESLint 检查并自动修复
pnpm lint:prettier # Prettier 格式化
pnpm lint:stylelint # Stylelint 检查样式
```

#### 提交前检查清单

1. 运行所有代码质量检查
2. 确保没有 TypeScript 类型错误
3. 确保没有未使用的变量和导入
4. 确保代码格式符合规范

#### 常见 ESLint 错误

```typescript
// @typescript-eslint/no-unused-vars
// ❌ 错误 - 导入了但未使用
import { ElMessageBox } from 'element-plus'

// ✅ 正确 - 删除未使用的导入

// ❌ 错误 - error 参数未使用
} catch (error) {
  ElMessage.error('操作失败')
}

// ✅ 正确 - 省略未使用的参数
} catch {
  ElMessage.error('操作失败')
}
```

### 6. defineOptions 的使用

为组件设置名称，便于调试和 keep-alive：

```typescript
// ✅ 在 <script setup> 中设置组件名
defineOptions({ name: 'TenantManagement' })
```

## 常用命令

### 质量检查命令

在 `apps/web` 目录执行：

```bash
# TypeScript 类型检查
pnpm typecheck

# ESLint 自动修复
pnpm fix

# Prettier 格式检查
pnpm lint:prettier

# StyleLint 样式检查
pnpm lint:stylelint

# 运行所有检查
pnpm typecheck && pnpm fix && pnpm lint:prettier && pnpm lint:stylelint
```

### 开发命令

```bash
# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览生产构建
pnpm preview

# 安装依赖
pnpm install
```

## 查询文档

使用 Context7 MCP 查询技术文档：

```bash
# 1. 解析库ID
mcp__context7__resolve-library-id libraryName="vue"

# 2. 查询文档
mcp__context7__get-library-docs context7CompatibleLibraryID="/vuejs/core" topic="composition-api"
```

常用库ID：

- Vue 3: `/vuejs/core`
- Element Plus: `/element-plus/element-plus`
- Vite: `/vitejs/vite`
- TypeScript: `/microsoft/TypeScript`

---

遵循本指南进行开发，确保代码质量和团队协作的一致性。
