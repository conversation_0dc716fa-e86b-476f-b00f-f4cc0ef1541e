<template>
  <ElDialog
    v-model="visible"
    :title="isEdit ? '编辑联系人' : '新增联系人'"
    width="500px"
    align-center
    append-to-body
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <ElFormItem label="联系人" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入联系人姓名" />
      </ElFormItem>
      <ElFormItem label="联系方式" prop="phone">
        <ElInput v-model="formData.phone" placeholder="请输入手机号码" />
      </ElFormItem>
      <ElFormItem label="职位" prop="position">
        <ElInput v-model="formData.position" placeholder="请输入职位" />
      </ElFormItem>
      <ElFormItem label="所在部门" prop="department">
        <ElInput v-model="formData.department" placeholder="请输入所在部门" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleConfirm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createContact, updateContact } from '@/api/admin/entity'
  import type { Contact, ContactForm } from '@/types/api'

  // Props
  interface Props {
    entityId: string
  }
  const props = defineProps<Props>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>('visible', { default: false })
  const contact = defineModel<Contact | null>('contact', { default: null })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 计算属性判断是否编辑模式
  const isEdit = computed(() => !!contact.value?.id)

  // 表单实例
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = ref<ContactForm>({
    entity_id: props.entityId,
    name: '',
    phone: '',
    position: '',
    department: ''
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
    ],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
    department: [{ required: true, message: '请输入所在部门', trigger: 'blur' }]
  }

  // 监听 contact 变化，更新表单数据
  watch(
    () => contact.value,
    (newVal) => {
      if (newVal) {
        // 编辑模式
        formData.value = {
          id: newVal.id,
          entity_id: props.entityId,
          name: newVal.name,
          phone: newVal.phone,
          position: newVal.position,
          department: newVal.department
        }
      } else {
        // 新增模式，重置数据
        formData.value = {
          entity_id: props.entityId,
          name: '',
          phone: '',
          position: '',
          department: ''
        }
      }
    },
    { immediate: true }
  )

  // 处理取消
  const handleCancel = () => {
    visible.value = false
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      if (isEdit.value) {
        await updateContact(props.entityId, formData.value.id!, formData.value)
        ElMessage.success('编辑成功')
      } else {
        await createContact(props.entityId, formData.value)
        ElMessage.success('新增成功')
      }

      visible.value = false
      emit('success')
    } catch (error: any) {
      if (error !== false) {
        // 不是验证错误
        console.error('保存联系人失败:', error)
        ElMessage.error(error.message || '保存失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭后的处理
  const handleClosed = () => {
    // 重置表单
    formRef.value?.resetFields()
    // 清空编辑数据
    contact.value = null
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
