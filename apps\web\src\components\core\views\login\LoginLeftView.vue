<template>
  <div class="login-left-view">
    <ParticleWaveBackground />
    <img class="left-img" src="@imgs/login/lf_icon2.webp" />
  </div>
</template>

<script setup lang="ts">
  import ParticleWaveBackground from '@/components/core/effects/ParticleWaveBackground.vue'
</script>

<style lang="scss" scoped>
  .login-left-view {
    position: relative;
    box-sizing: border-box;
    width: 50vw;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background: transparent;

    .left-img {
      position: relative;
      z-index: 10;
      display: block;
      width: 500px;
      margin: auto;
      margin-top: 15vh;
    }

    @media only screen and (max-width: $device-notebook) {
      .left-img {
        width: 480px;
        margin-top: 10vh;
      }
    }

    @media only screen and (max-width: $device-ipad-pro) {
      display: none;
    }
  }
</style>
