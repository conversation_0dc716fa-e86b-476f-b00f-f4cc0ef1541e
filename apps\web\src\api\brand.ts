import request from '@/utils/http'

/**
 * 品牌相关API
 */

/**
 * 品牌信息接口
 */
export interface Brand {
  id: number
  name: string
  entity_id: number
  entity_name: string
  display_name: string
  description: string | null
  sort_order: number
}

/**
 * 获取品牌列表
 * @param keyword 搜索关键字
 */
export const getBrandList = (keyword?: string) => {
  const params = keyword ? { keyword } : {}
  return request.get<Brand[]>({
    url: '/api/admin/brands',
    params
  })
}
