<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'account' => ['required', 'exists:users,account', 'string', 'max:255'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Configure the rate limiting middleware.
     */
    public function middleware(): array
    {
        return [
            'throttle:login',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'account.required' => '用户名不能为空',
            'account.exists' => '账号或密码错误',
            'password.required' => '密码不能为空',
        ];
    }
}
