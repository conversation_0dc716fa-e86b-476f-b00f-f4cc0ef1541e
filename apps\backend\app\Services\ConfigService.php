<?php

namespace App\Services;

use App\Models\Attachment;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;

/**
 * 配置管理服务
 */
class ConfigService
{
    /**
     * 获取所有配置
     */
    public function getAllConfigs(): array
    {

        $configs = SystemConfig::all();

        $result = [
            'system' => [],
            'upload' => [],
        ];

        foreach ($configs as $config) {

            if ($config->key == 'system_logo') {
                if ($config->value) {
                    $attachment = Attachment::where('id', $config->value)->first();
                    $config->value = $attachment;
                }
            }

            $result[$config->group][$config->key] = $config->value;
        }

        return $result;
    }

    /**
     * 更新配置
     */
    public function update(array $data): void
    {
        // 更新配置
        foreach ($data as $group => $configs) {
            foreach ($configs as $key => $value) {
                if ($key == 'system_logo' && is_array($value) && isset($value['id'])) {
                    $value = $value['id'];
                }
                SystemConfig::set($key, $value, $group);
            }
        }

        // 如果更新了上传配置，同步到.env文件
        if (isset($data['upload'])) {
            $this->syncEnvFile($data['upload']);
        }

        $this->clearCache();
    }

    /**
     * 同步配置到.env文件
     */
    protected function syncEnvFile(array $uploadConfigs): void
    {
        // 阿里云OSS配置映射
        $envMapping = [
            'storage_type' => 'ATTACHMENT_DRIVER',
            'aliyun_access_key' => 'ALIOSS_ACCESS_KEY_ID',
            'aliyun_secret_key' => 'ALIOSS_ACCESS_KEY_SECRET',
            'aliyun_bucket' => 'ALIOSS_BUCKET',
            'aliyun_region' => 'ALIOSS_ENDPOINT',
            // 'aliyun_endpoint' => 'ALIOSS_ENDPOINT',
        ];

        // 读取.env文件内容
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        // 更新.env文件内容
        foreach ($envMapping as $configKey => $envKey) {
            if (isset($uploadConfigs[$configKey])) {
                $value = $uploadConfigs[$configKey];

                // 检查键是否存在于.env文件中
                if (preg_match("/^{$envKey}=(.*)$/m", $envContent)) {
                    // 更新现有键值
                    $envContent = preg_replace("/^{$envKey}=(.*)$/m", "{$envKey}={$value}", $envContent);
                } else {
                    // 添加新键值
                    $envContent .= "\n{$envKey}={$value}";
                }
            }
        }

        // 写入更新后的内容到.env文件
        file_put_contents($envFile, $envContent);

        // 清除配置缓存
        if (function_exists('exec')) {
            exec('php artisan config:clear');
        } else {
            Artisan::call('config:clear');
        }
    }

    /**
     * 初始化默认配置
     */
    public function initializeDefaultConfigs(): void
    {
        // 系统配置默认值
        $systemDefaults = [
            'system_name' => '厂商云管理系统',
            'system_logo' => null,
        ];

        // 上传配置默认值
        $uploadDefaults = [
            'storage_type' => 'local',
            'aliyun_access_key' => '',
            'aliyun_secret_key' => '',
            'aliyun_bucket' => '',
            'aliyun_region' => '',
            'aliyun_endpoint' => '',
        ];

        // 检查并设置默认配置
        foreach ($systemDefaults as $key => $value) {
            if (! SystemConfig::where('key', $key)->exists()) {
                SystemConfig::set($key, $value, 'system');
            }
        }

        foreach ($uploadDefaults as $key => $value) {
            if (! SystemConfig::where('key', $key)->exists()) {
                SystemConfig::set($key, $value, 'upload');
            }
        }
    }

    /**
     * 清除所有配置缓存
     */
    public function clearCache(): void
    {
        Cache::forget('system_configs::all');
    }
}
