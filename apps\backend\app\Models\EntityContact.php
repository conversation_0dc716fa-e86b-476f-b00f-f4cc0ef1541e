<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $entity_id 相关方ID
 * @property string $name 联系人姓名
 * @property string $phone 联系电话
 * @property string|null $position 职位
 * @property string|null $department 部门
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \App\Models\Entity $entity
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereUpdatedBy($value)
 *
 * @mixin \Eloquent
 */
class EntityContact extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'entity_id',
        'name',
        'phone',
        'position',
        'department',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class);
    }
}
