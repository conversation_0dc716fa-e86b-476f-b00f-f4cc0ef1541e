<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int|null $user_id 用户ID
 * @property string|null $user_name 用户名
 * @property int|null $menu_id 菜单ID
 * @property string|null $menu_name 菜单名称
 * @property string|null $operation_type 操作类型：create,update,delete,view,export,import等
 * @property string|null $operation_description 操作描述
 * @property string|null $target_type 操作目标类型：Asset,User,Menu等
 * @property int|null $target_id 操作目标ID
 * @property string|null $target_name 操作目标名称
 * @property string $method 请求方法
 * @property string $path 请求路径
 * @property string $ip IP地址
 * @property array<array-key, mixed>|null $headers 请求头
 * @property array<array-key, mixed>|null $params 请求参数
 * @property string|null $user_agent User-Agent
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property-read string $full_operation_description
 * @property-read string $operation_type_text
 * @property-read \App\Models\Menu|null $menu
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereHeaders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereMenuName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereOperationDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereOperationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereTargetName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationLog whereUserName($value)
 *
 * @mixin \Eloquent
 */
class OperationLog extends BaseModel
{
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'user_name',
        'menu_id',
        'menu_name',
        'operation_type',
        'operation_description',
        'target_type',
        'target_id',
        'target_name',
        'method',
        'path',
        'ip',
        'headers',
        'params',
        'user_agent',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'headers' => 'array',
        'params' => 'array',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联菜单
     */
    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    /**
     * 获取操作类型的中文描述
     */
    public function getOperationTypeTextAttribute(): string
    {
        $typeMap = [
            'create' => '创建',
            'update' => '更新',
            'delete' => '删除',
            'view' => '查看',
            'export' => '导出',
            'import' => '导入',
            'login' => '登录',
            'logout' => '登出',
            'download' => '下载',
            'upload' => '上传',
        ];

        return $typeMap[$this->operation_type] ?? $this->operation_type;
    }

    /**
     * 获取完整操作描述
     */
    public function getFullOperationDescriptionAttribute(): string
    {
        if ($this->operation_description) {
            return $this->operation_description;
        }

        $description = '';

        if ($this->user_name) {
            $description .= $this->user_name;
        }

        if ($this->operation_type) {
            $description .= $this->operation_type_text;
        }

        if ($this->target_name) {
            $description .= $this->target_type ? "了{$this->target_type}" : '了';
            $description .= $this->target_name;
        } elseif ($this->menu_name) {
            $description .= "了{$this->menu_name}";
        }

        return $description ?: '执行了操作';
    }
}
