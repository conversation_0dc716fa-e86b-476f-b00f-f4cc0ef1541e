/**
 * 数据格式化相关工具函数
 */
import dayjs from 'dayjs'

// 时间戳转时间
export function timestampToTime(timestamp: number = Date.now(), isMs: boolean = true): string {
  const date = new Date(isMs ? timestamp : timestamp * 1000)
  return date.toISOString().replace('T', ' ').slice(0, 19)
}

// 日期格式化
export function formatDate(date?: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''

  // 如果是数字类型且小于 10000000000（2001年的秒级时间戳），使用 unix 方法
  if (typeof date === 'number' && date < 10000000000) {
    return dayjs.unix(date).format(format)
  }

  return dayjs(date).format(format)
}

// 数字格式化（千位分隔符）
export function commafy(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 生成随机数
export function randomNum(min: number, max?: number): number {
  if (max === undefined) {
    max = min
    min = 0
  }
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 移除HTML标签
export function removeHtmlTags(str: string = ''): string {
  return str.replace(/<[^>]*>/g, '')
}
