<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $lifecycle_id 生命周期ID
 * @property int $tag_id 标签ID
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \App\Models\Lifecycle $lifecycle
 * @property-read \App\Models\Tag $tag
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereLifecycleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereTagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleTag whereUpdatedBy($value)
 *
 * @mixin \Eloquent
 */
class LifecycleTag extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'lifecycle_id',
        'tag_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取所属的生命周期
     */
    public function lifecycle(): BelongsTo
    {
        return $this->belongsTo(Lifecycle::class, 'lifecycle_id');
    }

    /**
     * 获取标签
     */
    public function tag(): BelongsTo
    {
        return $this->belongsTo(Tag::class, 'tag_id');
    }
}
