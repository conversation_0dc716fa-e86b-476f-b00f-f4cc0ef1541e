<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\BrandResource;
use App\Services\BrandService;
use Illuminate\Http\Request;

/**
 * @group 品牌管理
 */
class BrandController extends Controller
{
    public function __construct(
        private BrandService $brandService
    ) {}

    /**
     * 获取系统所有品牌列表
     *
     * @queryParam keyword string 搜索关键字 Example: 华为
     *
     * @apiResourceCollection App\Http\Resources\Admin\BrandResource
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword');
        $brands = $this->brandService->getAllBrands($keyword);

        return BrandResource::collection($brands);
    }
}