import request from '@/utils/http'
import type { LoginParams, LoginResponse } from '@/types/api/user'

// ===========================
// 认证相关接口
// ===========================

// 登录
export const login = (data: LoginParams): Promise<LoginResponse> => {
  return request.post<LoginResponse>({
    url: '/api/admin/login',
    data
  })
}

// 退出登录
export const logout = (): Promise<void> => {
  return request.post({
    url: '/api/admin/logout'
  })
}
