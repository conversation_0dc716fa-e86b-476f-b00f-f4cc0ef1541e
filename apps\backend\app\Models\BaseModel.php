<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static Builder<static>|BaseModel newModelQuery()
 * @method static Builder<static>|BaseModel newQuery()
 * @method static Builder<static>|BaseModel query()
 *
 * @mixin \Eloquent
 */
class BaseModel extends Model
{
    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    /**
     * 启动模型事件
     */
    protected static function boot()
    {
        parent::boot();
    }
}
