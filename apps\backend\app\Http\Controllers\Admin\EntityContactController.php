<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\EntityContactResource;
use App\Models\Entity;
use App\Models\EntityContact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * @group 相关方管理
 */
class EntityContactController extends Controller
{
    /**
     * 获取相关方的联系人列表
     *
     * @urlParam entity int required 相关方ID Example: 1
     *
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 10
     *
     * @apiResourceCollection App\Http\Resources\Admin\EntityContactResource
     *
     * @apiResourceModel App\Models\EntityContact paginate=10
     */
    public function index(Entity $entity, Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $contacts = $entity->contacts()->paginate($perPage);

        return EntityContactResource::collection($contacts);
    }

    /**
     * 创建联系人
     *
     * @urlParam entity int required 相关方ID Example: 1
     *
     * @bodyParam name string required 联系人姓名 Example: 张三
     * @bodyParam phone string required 联系电话 Example: 13800138000
     * @bodyParam position string 职位 Example: 总经理
     * @bodyParam department string 部门 Example: 管理部
     */
    public function store(Entity $entity, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|max:20',
            'position' => 'nullable|string|max:50',
            'department' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $contact = $entity->contacts()->create([
            'name' => $request->name,
            'phone' => $request->phone,
            'position' => $request->position,
            'department' => $request->department,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);

        return (new EntityContactResource($contact))->response()->setStatusCode(201);
    }

    /**
     * 更新联系人
     *
     * @urlParam entity int required 相关方ID Example: 1
     * @urlParam contact int required 联系人ID Example: 1
     *
     * @bodyParam name string required 联系人姓名 Example: 张三
     * @bodyParam phone string required 联系电话 Example: 13800138000
     * @bodyParam position string 职位 Example: 总经理
     * @bodyParam department string 部门 Example: 管理部
     */
    public function update(Entity $entity, EntityContact $contact, Request $request)
    {
        // 验证联系人是否属于该相关方
        if ($contact->entity_id !== $entity->id) {
            return $this->error('联系人不属于该相关方', 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|max:20',
            'position' => 'nullable|string|max:50',
            'department' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $contact->update([
            'name' => $request->name,
            'phone' => $request->phone,
            'position' => $request->position,
            'department' => $request->department,
            'updated_by' => auth()->id(),
        ]);

        return new EntityContactResource($contact);
    }

    /**
     * 删除联系人
     *
     * @urlParam entity int required 相关方ID Example: 1
     * @urlParam contact int required 联系人ID Example: 1
     */
    public function destroy(Entity $entity, EntityContact $contact)
    {
        // 验证联系人是否属于该相关方
        if ($contact->entity_id !== $entity->id) {
            return $this->error('联系人不属于该相关方', 403);
        }

        $contact->delete();

        return response()->noContent();
    }
}
