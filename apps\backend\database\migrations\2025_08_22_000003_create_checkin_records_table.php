<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkin_records', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('checkin_config_id')->comment('关联打卡配置ID');
            $table->unsignedBigInteger('user_id')->comment('打卡用户ID');
            $table->integer('checkin_time')->nullable()->comment('打卡时间');
            $table->tinyInteger('status')->default(0)->comment('状态：0-正常，1-正常，2-异常');
            $table->integer('location_range')->nullable()->comment('打卡位置范围，离打卡地点的距离，单位：米');
            $table->string('location', 255)->nullable()->comment('打卡地点');
            $table->decimal('latitude', 10, 8)->nullable()->comment('打卡位置经度');
            $table->decimal('longitude', 11, 8)->nullable()->comment('打卡位置纬度');
            $table->string('ip_address', 50)->nullable()->comment('打卡IP地址');
            $table->unsignedBigInteger('attachment_id')->nullable()->comment('打卡附件ID');
            $table->string('content', 255)->nullable()->comment('打卡内容');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            $table->foreign('checkin_config_id')->references('id')->on('checkin_configs')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('attachment_id')->references('id')->on('attachments')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkin_records');
    }
};
