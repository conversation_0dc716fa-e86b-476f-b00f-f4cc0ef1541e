<?php

namespace App\Http\Resources\Admin;

use App\Enums\AttachmentCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EntityBrandResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // 获取品牌Logo附件 - 从已加载的关系中筛选
        $logo = null;
        if ($this->relationLoaded('attachments')) {
            $logo = $this->attachments->filter(function ($attachment) {
                return $attachment->pivot->category === AttachmentCategory::LOGO->value;
            })->first();
        }

        return [
            'id' => $this->id,
            'entity_id' => $this->entity_id,
            'name' => $this->name,
            'logo' => $logo ? $logo->file_url : null,
            'logo_id' => $logo?->id,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
