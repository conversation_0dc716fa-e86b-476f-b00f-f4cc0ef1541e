<?php

namespace App\Services;

use App\Enums\AttachmentCategory;
use App\Models\Entity;
use App\Models\EntityBrand;
use App\Models\EntityContact;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class EntityService
{
    /**
     * 分页获取相关方列表
     */
    public function paginate(array $params = []): LengthAwarePaginator
    {
        // 优化查询：对于列表页面，使用 withCount 代替 with，减少数据传输
        $query = Entity::withCount(['contacts', 'attachments', 'brands']);

        // 搜索条件 - 分别处理每个字段
        if (! empty($params['name'])) {
            $query->where('name', 'like', "%{$params['name']}%");
        }

        if (! empty($params['tax_number'])) {
            $query->where('tax_number', 'like', "%{$params['tax_number']}%");
        }

        if (! empty($params['keywords'])) {
            $query->where('keywords', 'like', "%{$params['keywords']}%");
        }

        // 保留 keyword 参数的兼容性（用于通用搜索）
        if (! empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->where(function (Builder $q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('tax_number', 'like', "%{$keyword}%")
                    ->orWhere('keywords', 'like', "%{$keyword}%");
            });
        }

        // 相关方类型筛选
        if (! empty($params['entity_type'])) {
            $query->where('entity_type', $params['entity_type']);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        return $query->paginate($params['per_page'] ?? 20);
    }

    /**
     * 创建相关方
     */
    public function create(array $data): Entity
    {
        return DB::transaction(function () use ($data) {
            // 创建相关方
            $entity = Entity::create([
                'name' => $data['name'],
                'tax_number' => $data['tax_number'] ?? null,
                'entity_type' => $data['entity_type'],
                'address' => $data['address'] ?? null,
                'phone' => $data['phone'] ?? null,
                'keywords' => $data['keywords'] ?? null,
                'remark' => $data['remark'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // 创建联系人
            if (! empty($data['contacts'])) {
                foreach ($data['contacts'] as $contactData) {
                    $entity->contacts()->create([
                        'name' => $contactData['name'],
                        'phone' => $contactData['phone'],
                        'position' => $contactData['position'] ?? null,
                        'department' => $contactData['department'] ?? null,
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                    ]);
                }
            }

            // 创建品牌
            if (! empty($data['brands'])) {
                foreach ($data['brands'] as $brandData) {
                    // 处理品牌Logo附件ID
                    $logoId = $brandData['logo_id'] ?? null;

                    $brand = $entity->brands()->create([
                        'name' => $brandData['name'],
                        'description' => $brandData['description'] ?? null,
                        'sort_order' => $brandData['sort_order'] ?? 0,
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                    ]);

                    // 关联品牌Logo附件
                    if ($logoId) {
                        $attachmentData = [[
                            'id' => $logoId,
                            'category' => AttachmentCategory::LOGO->value,
                            'sort' => 0,
                            'description' => null,
                        ]];
                        $brand->attachFiles($attachmentData);
                    }
                }
            }

            // 处理附件关联
            if (! empty($data['attachments'])) {
                $attachmentData = [];
                foreach ($data['attachments'] as $index => $attachmentId) {
                    $attachmentData[] = [
                        'id' => $attachmentId,
                        'category' => AttachmentCategory::GENERAL->value,
                        'sort' => $index,
                        'description' => null,
                    ];
                }
                $entity->attachFiles($attachmentData);
            }

            return $entity->load(['contacts', 'brands.attachments', 'attachments']);
        });
    }

    /**
     * 更新相关方
     */
    public function update(Entity $entity, array $data): Entity
    {
        return DB::transaction(function () use ($entity, $data) {
            // 更新相关方信息
            $entity->update([
                'name' => $data['name'],
                'tax_number' => $data['tax_number'] ?? null,
                'entity_type' => $data['entity_type'],
                'address' => $data['address'] ?? null,
                'phone' => $data['phone'] ?? null,
                'keywords' => $data['keywords'] ?? null,
                'remark' => $data['remark'] ?? null,
                'updated_by' => auth()->id(),
            ]);

            // 处理联系人更新
            if (isset($data['contacts'])) {
                // 获取现有联系人ID
                $existingIds = $entity->contacts->pluck('id')->toArray();
                $keepIds = [];

                foreach ($data['contacts'] as $contactData) {
                    if (! empty($contactData['id']) && in_array($contactData['id'], $existingIds)) {
                        // 更新现有联系人
                        EntityContact::where('id', $contactData['id'])->update([
                            'name' => $contactData['name'],
                            'phone' => $contactData['phone'],
                            'position' => $contactData['position'] ?? null,
                            'department' => $contactData['department'] ?? null,
                            'updated_by' => auth()->id(),
                        ]);
                        $keepIds[] = $contactData['id'];
                    } else {
                        // 创建新联系人
                        $newContact = $entity->contacts()->create([
                            'name' => $contactData['name'],
                            'phone' => $contactData['phone'],
                            'position' => $contactData['position'] ?? null,
                            'department' => $contactData['department'] ?? null,
                            'created_by' => auth()->id(),
                            'updated_by' => auth()->id(),
                        ]);
                        $keepIds[] = $newContact->id;
                    }
                }

                // 删除不在更新列表中的联系人
                $deleteIds = array_diff($existingIds, $keepIds);
                if (! empty($deleteIds)) {
                    EntityContact::whereIn('id', $deleteIds)->delete();
                }
            }

            // 处理品牌更新
            if (isset($data['brands'])) {
                // 获取现有品牌ID
                $existingIds = $entity->brands->pluck('id')->toArray();
                $keepIds = [];

                foreach ($data['brands'] as $brandData) {
                    // 处理品牌Logo附件ID
                    $logoId = $brandData['logo_id'] ?? null;

                    if (! empty($brandData['id']) && in_array($brandData['id'], $existingIds)) {
                        // 更新现有品牌
                        $brand = EntityBrand::find($brandData['id']);
                        $brand->update([
                            'name' => $brandData['name'],
                            'description' => $brandData['description'] ?? null,
                            'sort_order' => $brandData['sort_order'] ?? 0,
                            'updated_by' => auth()->id(),
                        ]);

                        // 更新品牌Logo附件
                        if ($logoId !== null) {
                            if ($logoId) {
                                // 有新Logo，同步附件
                                $attachmentData = [[
                                    'id' => $logoId,
                                    'category' => AttachmentCategory::LOGO->value,
                                    'sort' => 0,
                                    'description' => null,
                                ]];
                                $brand->syncAttachments($attachmentData);
                            } else {
                                // 清空Logo，解除所有Logo类型的附件
                                $logoAttachments = $brand->attachments()
                                    ->wherePivot('category', AttachmentCategory::LOGO->value)
                                    ->pluck('attachments.id');
                                foreach ($logoAttachments as $attachmentId) {
                                    $brand->detachFile($attachmentId);
                                }
                            }
                        }

                        $keepIds[] = $brandData['id'];
                    } else {
                        // 创建新品牌
                        $newBrand = $entity->brands()->create([
                            'name' => $brandData['name'],
                            'description' => $brandData['description'] ?? null,
                            'sort_order' => $brandData['sort_order'] ?? 0,
                            'created_by' => auth()->id(),
                            'updated_by' => auth()->id(),
                        ]);

                        // 关联品牌Logo附件
                        if ($logoId) {
                            $attachmentData = [[
                                'id' => $logoId,
                                'category' => AttachmentCategory::LOGO->value,
                                'sort' => 0,
                                'description' => null,
                            ]];
                            $newBrand->attachFiles($attachmentData);
                        }

                        $keepIds[] = $newBrand->id;
                    }
                }

                // 删除不在更新列表中的品牌
                $deleteIds = array_diff($existingIds, $keepIds);
                if (! empty($deleteIds)) {
                    EntityBrand::whereIn('id', $deleteIds)->delete();
                }
            }

            // 处理附件更新
            if (isset($data['attachments'])) {
                $attachmentData = [];
                foreach ($data['attachments'] as $index => $attachmentId) {
                    $attachmentData[] = [
                        'id' => $attachmentId,
                        'category' => AttachmentCategory::GENERAL->value,
                        'sort' => $index,
                        'description' => null,
                    ];
                }
                $entity->syncAttachments($attachmentData);
            }

            return $entity->load(['contacts', 'brands.attachments', 'attachments']);
        });
    }

    /**
     * 删除相关方
     */
    public function delete(Entity $entity): bool
    {
        return DB::transaction(function () use ($entity) {
            // 由于外键级联删除，联系人会自动删除
            return $entity->delete();
        });
    }
}
