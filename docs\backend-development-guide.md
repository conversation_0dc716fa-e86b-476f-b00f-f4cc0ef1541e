# 后端开发规范指南

⚠️ **重要提示**：开发后端功能时必须严格遵循本规范，确保代码质量和团队协作的一致性。

本文档为后端开发人员提供 Laravel 12 开发规范和最佳实践。

## 📋 目录

1. [技术栈](#技术栈)
2. [Docker 环境](#docker-环境)
3. [开发规范](#开发规范)
4. [API 设计](#api-设计)
5. [常用命令](#常用命令)
6. [注意事项](#注意事项)

## 技术栈

- **框架**：Laravel 12
- **PHP 版本**：8.2+
- **数据库**：MySQL 8.4.5
- **认证**：Laravel Sanctum
- **API 文档**：Scribe

## Docker 环境

### 容器信息
- **PHP 容器**：`ty-php-8.3`
- **MySQL 容器**：`ty-mysql-8.4.5`
- **MySQL 账号**：`root / root`

### 命令执行模板

所有 Laravel 命令必须在 Docker 容器内执行：

```bash
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan [命令]"
```

## 开发规范

### ⚠️ 重要警告
- ❌ **禁止代码格式化**：永远不要使用 Pint 或任何其他代码格式化工具
- ❌ **禁止运行测试**：不需要执行 Pest 或其他测试命令

### 1. 文件组织

```
apps/backend/app/
├── Http/
│   ├── Controllers/Admin/  # Admin控制器
│   ├── Requests/Admin/     # Admin请求验证
│   └── Resources/          # API资源
├── Models/                 # Eloquent模型
└── Services/               # 业务逻辑
```

### 2. 控制器规范

控制器应保持精简，业务逻辑放在 Service 层：

```php
class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    public function index(UserIndexRequest $request)
    {
        return UserResource::collection(
            $this->userService->paginate($request->validated())
        );
    }

    public function store(UserStoreRequest $request)
    {
        $user = $this->userService->create($request->validated());
        return new UserResource($user);
    }

    public function update(UserUpdateRequest $request, User $user)
    {
        $user = $this->userService->update($user, $request->validated());
        return new UserResource($user);
    }

    public function destroy(User $user)
    {
        $this->userService->delete($user);
        return response()->noContent();
    }
}
```

### 3. FormRequest 规范

**必须使用 FormRequest 进行验证**，禁止在控制器内验证：

```php
class UserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'role_id' => 'required|exists:roles,id'
        ];
    }
    
    public function messages(): array
    {
        return [
            'name.required' => '用户名不能为空',
            'name.max' => '用户名不能超过50个字符',
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
            'email.unique' => '邮箱已被使用',
            'password.required' => '密码不能为空',
            'password.min' => '密码至少6个字符',
            'role_id.required' => '角色不能为空',
            'role_id.exists' => '选择的角色不存在'
        ];
    }
}
```

### 4. Service Layer 模式

Service 层负责处理业务逻辑、数据库事务等：

```php
class UserService
{
    public function paginate(array $filters)
    {
        $query = User::query()->with(['roles', 'department']);
        
        // 应用过滤条件
        if (!empty($filters['keyword'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', "%{$filters['keyword']}%")
                  ->orWhere('email', 'like', "%{$filters['keyword']}%");
            });
        }
        
        if (!empty($filters['role_id'])) {
            $query->where('role_id', $filters['role_id']);
        }
        
        // 排序
        $query->orderBy('created_at', 'desc');
        
        // 分页
        return $query->paginate($filters['per_page'] ?? 20);
    }
    
    public function create(array $data): User
    {
        return DB::transaction(function () use ($data) {
            // 创建用户
            $user = User::create($data);
            
            // 处理关联数据
            if (!empty($data['role_ids'])) {
                $user->roles()->sync($data['role_ids']);
            }
            
            // 处理附件
            if (!empty($data['avatar_id'])) {
                $this->attachmentService->associate($data['avatar_id'], $user);
            }
            
            return $user->fresh(['roles', 'avatar']);
        });
    }
    
    public function update(User $user, array $data): User
    {
        return DB::transaction(function () use ($user, $data) {
            $user->update($data);
            
            // 更新关联数据
            if (isset($data['role_ids'])) {
                $user->roles()->sync($data['role_ids']);
            }
            
            return $user->fresh(['roles']);
        });
    }
    
    public function delete(User $user): bool
    {
        return DB::transaction(function () use ($user) {
            // 清理关联数据
            $user->roles()->detach();
            
            // 删除用户
            return $user->delete();
        });
    }
}
```

### 5. API Resource 规范

使用 Resource 类格式化 API 响应：

```php
class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'avatar' => $this->when($this->avatar, function () {
                return [
                    'id' => $this->avatar->id,
                    'file_url' => $this->avatar->file_url,
                    'file_name' => $this->avatar->file_name
                ];
            }),
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'created_at' => $this->created_at?->timestamp,
            'updated_at' => $this->updated_at?->timestamp
        ];
    }
}
```

## API 设计

### RESTful 路由规范

```
GET    /api/admin/users         # 列表
GET    /api/admin/users/{id}    # 详情
POST   /api/admin/users         # 创建
PUT    /api/admin/users/{id}    # 更新
DELETE /api/admin/users/{id}    # 删除
```

### API 响应格式

- **成功响应**：直接返回资源，不包装 `{data: {...}}`
- **分页响应**：
```json
{
  "data": [...],
  "meta": {
    "total": 100,
    "current_page": 1,
    "per_page": 20,
    "last_page": 5
  }
}
```
- **错误响应**：
```json
{
  "message": "验证失败",
  "errors": {
    "email": ["邮箱已被使用"]
  }
}
```

### 命名规范

- **API 字段**：统一使用 `snake_case`
- **路由命名**：使用复数形式（users, products, orders）
- **控制器命名**：单数形式 + Controller（UserController）

## 常用命令

### 基础命令

```bash
# 数据库迁移
php artisan migrate

# 重置数据库并填充数据
php artisan migrate:fresh --seed

# 清除所有缓存
php artisan optimize:clear

# 生成 API 文档
php artisan scribe:generate

# 生成字典枚举
php artisan dictionary:generate-enums
```

### 创建命令

```bash
# 创建控制器
php artisan make:controller Admin/ProductController

# 创建模型（带迁移）
php artisan make:model Product -m

# 创建 FormRequest
php artisan make:request Admin/ProductRequest

# 创建 Resource
php artisan make:resource ProductResource

# 创建 Service
php artisan make:service ProductService

# 创建迁移
php artisan make:migration create_products_table

# 创建 Seeder
php artisan make:seeder ProductSeeder
```

### Docker 命令示例

```bash
# 开发环境容器：ty-php-8.3
# 生产环境容器：device-cloud-saas（docker-compose）

# 执行迁移
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan migrate"

# 重置数据库
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan migrate:fresh --seed"

# 生成 API 文档
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan scribe:generate"

# 清除缓存
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan optimize:clear"

# 进入容器
docker exec -it ty-php-8.3 bash
cd /var/www/html/company/device-cloud-saas/apps/backend
```

## 注意事项

### 开发流程

1. **查询文档**：先查询 Context7 MCP 获取 Laravel 12 最新文档
2. **代码模式**：检查现有代码模式，保持一致性
3. **业务逻辑**：使用 Service Layer 处理业务逻辑
4. **API 文档**：更改 API 后执行 `php artisan scribe:generate` 更新文档

### API 文档位置

- **OpenAPI 规范**：`apps/backend/storage/app/private/scribe/openapi.yaml`
- **Postman 集合**：`apps/backend/storage/app/private/scribe/collection.json`
- **在线查看**：http://localhost/docs
- **OpenAPI 下载**：http://localhost/docs.openapi
- **Postman 下载**：http://localhost/docs.postman

### 数据库规范

- 表名使用复数形式（users, products）
- 字段名使用 snake_case
- 外键命名：`{单数表名}_{id}`（如 user_id）
- 时间戳字段：created_at, updated_at, deleted_at

### 附件处理

#### HasAttachments Trait

模型使用 `HasAttachments` trait 实现多态关联：

```php
use App\Traits\HasAttachments;

class Product extends Model
{
    use HasAttachments;
    
    // 自动获得以下方法：
    // $product->attachFiles($attachmentData);        // 批量添加附件
    // $product->syncAttachments($attachmentData);     // 同步附件（删除旧的，添加新的）
    // $product->attachmentsByCategory($category);     // 按分类获取附件
    // $product->detachFile($attachmentId);            // 删除单个附件
    // $product->detachAllFiles();                     // 删除所有附件
}
```

在 Service 层使用：

```php
public function update($id, array $data): Product
{
    $product = Product::findOrFail($id);
    $product->update($data);
    
    // 处理附件
    if (isset($data['attachment_ids'])) {
        $product->syncAttachments($data['attachment_ids']);
    }
    
    // 处理多个分类附件
    if (isset($data['image_ids'])) {
        $product->syncAttachments($data['image_ids'], 'images');
    }
    if (isset($data['document_ids'])) {
        $product->syncAttachments($data['document_ids'], 'documents');
    }
    
    return $product->load('attachments');
}
```

**附件处理规范**：
- Service 层自动设置附件分类
- 前端提交纯 ID 数组，后端返回完整对象
- 支持多态关联，任何模型都可使用

### 中间件体系

项目实现了多个自定义中间件：

#### OperationLogMiddleware

自动记录所有 API 操作日志：

```php
// 自动记录以下信息：
// - 请求路径、方法、参数
// - 用户 ID 和 IP 地址
// - 响应状态和执行时间
// - 错误信息（如果有）

// 跳过敏感路径：
// - /api/login
// - /api/download/*
// - /api/docs/*
```

#### MenuPermissionMiddleware

菜单权限检查：

```php
// 检查用户是否有访问菜单的权限
// 权限与 API 路由绑定（route_name 字段）
// 精细化权限控制（菜单级、操作级）
```

#### ForceJsonResponse

强制 JSON 响应：

```php
// 确保所有 API 返回 JSON 格式
// 统一错误响应格式
```

中间件注册（`app/Http/Kernel.php`）：

```php
protected $middlewareGroups = [
    'api' => [
        \App\Http\Middleware\ForceJsonResponse::class,
        \App\Http\Middleware\OperationLogMiddleware::class,
        // ...
    ],
];

protected $routeMiddleware = [
    'menu.permission' => \App\Http\Middleware\MenuPermissionMiddleware::class,
    // ...
];
```

### 认证与权限

- 使用 Laravel Sanctum 进行 API 认证
- 通过中间件控制路由权限
- 权限标识格式：`module.action`（如 user.create）
- 权限与具体 API 路由绑定（route_name）
- 每个菜单可配置多个权限点（add、edit、delete 等）

### 性能优化

- 使用 Eager Loading 避免 N+1 查询
- 合理使用缓存（Redis）
- 大数据量使用分页或游标分页
- 使用队列处理耗时任务

---

遵循本指南进行开发，确保代码质量和团队协作的一致性。