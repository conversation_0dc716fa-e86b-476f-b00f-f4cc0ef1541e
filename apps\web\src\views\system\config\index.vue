<template>
  <div class="config-page art-page-view">
    <ElCard shadow="never" class="config-card">
      <template #header>
        <div class="card-header">
          <span>系统配置</span>
          <ElButton v-if="hasAuth('edit')" type="primary" size="small" @click="handleSaveAll"
            >保存配置</ElButton
          >
        </div>
      </template>

      <ElTabs v-model="activeTab">
        <ElTabPane label="系统配置" name="system">
          <SystemConfig ref="systemConfigRef" />
        </ElTabPane>
        <ElTabPane label="上传配置" name="upload">
          <UploadConfig ref="uploadConfigRef" />
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'ConfigManagement' })

  // Vue 核心
  import { ref } from 'vue'

  // UI 框架
  import { ElCard, ElButton, ElT<PERSON>s, ElTabPane, ElMessage } from 'element-plus'

  // 权限控制
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import SystemConfig from './components/SystemConfig.vue'
  import UploadConfig from './components/UploadConfig.vue'

  // 权限控制
  const { hasAuth } = useAuth()

  // 当前激活的标签页
  const activeTab = ref('system')

  // 子组件引用
  const systemConfigRef = ref<InstanceType<typeof SystemConfig>>()
  const uploadConfigRef = ref<InstanceType<typeof UploadConfig>>()

  // 导入 store
  import { useConfigStore } from '@/store/modules/config'

  const configStore = useConfigStore()

  // 保存所有配置
  const handleSaveAll = async () => {
    try {
      // 获取系统配置数据
      const systemData = await systemConfigRef.value?.getFormData()
      // 获取上传配置数据
      const uploadData = await uploadConfigRef.value?.getFormData()

      if (!systemData || !uploadData) {
        ElMessage.warning('请填写完整的配置信息')
        return
      }

      // 组合成完整的配置
      const allConfig = {
        system: systemData,
        upload: uploadData
      }

      // 调用统一的更新方法
      await configStore.updateAllConfigs(allConfig)
      ElMessage.success('配置保存成功')
    } catch (error) {
      ElMessage.error('保存失败，请检查表单')
    }
  }
</script>

<style lang="scss" scoped>
  .config-page {
    padding: 16px;

    .config-card {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      :deep(.el-tabs__content) {
        padding-top: 20px;
      }
    }
  }
</style>
