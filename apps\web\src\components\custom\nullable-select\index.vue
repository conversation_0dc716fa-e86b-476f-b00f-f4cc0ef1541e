<template>
  <ElSelect
    :model-value="modelValue ?? undefined"
    @update:model-value="handleUpdate"
    v-bind="$attrs"
  >
    <slot />
  </ElSelect>
</template>

<script setup lang="ts">
  /**
   * NullableSelect 组件
   *
   * 解决 Element Plus Select 组件不支持 v-model 绑定 null 值的 TypeScript 类型错误问题。
   *
   * 背景：
   * - Element Plus 的 Select 组件 TypeScript 类型定义不支持 null 值
   * - 但实际运行时是支持的，导致 TS 编译错误但运行正常
   * - 后端返回 null 值时需要额外处理
   *
   * 功能：
   * - 自动将 null 转换为 undefined 传给 ElSelect（避免 TS 错误）
   * - 将 undefined 转换回 null 传给父组件（保持数据一致性）
   * - 支持所有 ElSelect 的原生属性和事件
   *
   * 使用方式：
   * ```vue
   * <NullableSelect v-model="formData.user_id" placeholder="请选择用户">
   *   <ElOption
   *     v-for="user in userList"
   *     :key="user.id"
   *     :label="user.name"
   *     :value="user.id"
   *   />
   * </NullableSelect>
   * ```
   */

  interface Props {
    modelValue: string | number | boolean | null | undefined | Array<any>
  }

  defineProps<Props>()

  const emit = defineEmits<{
    'update:modelValue': [value: string | number | boolean | null | undefined | Array<any>]
  }>()

  // 处理更新事件，将 undefined 转换回 null
  const handleUpdate = (value: any) => {
    emit('update:modelValue', value ?? null)
  }
</script>

<script lang="ts">
  export default {
    name: 'NullableSelect',
    inheritAttrs: false
  }
</script>
