<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

/**
 * @property int $id
 * @property int $pid 父级ID
 * @property int $deep 层级深度：0省1市2区县
 * @property string $name 地区名称
 * @property string $pinyin_prefix 拼音首字母
 * @property string $pinyin 拼音全拼
 * @property string $ext_id 行政区划代码
 * @property string $ext_name 完整名称
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Region> $children
 * @property-read int|null $children_count
 * @property-read Region|null $parent
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region citiesOfProvince($provinceId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region districtsOfCity($cityId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region provinces()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region search($keyword)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereDeep($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereExtId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereExtName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePinyin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePinyinPrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Region extends Model
{
    protected $fillable = [
        'id',
        'pid',
        'deep',
        'name',
        'pinyin_prefix',
        'pinyin',
        'ext_id',
        'ext_name',
    ];

    protected $casts = [
        'id' => 'integer',
        'pid' => 'integer',
        'deep' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取子地区
     */
    public function children(): HasMany
    {
        return $this->hasMany(Region::class, 'pid', 'id');
    }

    /**
     * 获取父地区
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Region::class, 'pid', 'id');
    }

    /**
     * 获取所有祖先地区
     */
    public function ancestors()
    {
        $ancestors = collect();
        $region = $this;

        while ($region->parent) {
            $ancestors->push($region->parent);
            $region = $region->parent;
        }

        return $ancestors->reverse();
    }

    /**
     * 获取完整路径
     */
    public function getFullPath(): array
    {
        $path = $this->ancestors();
        $path->push($this);

        return $path->map(function ($region) {
            return [
                'code' => $region->ext_id,
                'name' => $region->ext_name,
            ];
        })->toArray();
    }

    /**
     * 构建树形结构（静态方法）
     */
    public static function buildTree($parentId = 0)
    {
        return Cache::remember("regions_tree_{$parentId}", 3600, function () use ($parentId) {
            $regions = self::where('pid', $parentId)->orderBy('id')->get();

            return $regions->map(function ($region) {
                $item = [
                    'value' => $region->ext_id,
                    'label' => $region->ext_name,
                    'id' => $region->id,
                    'pid' => $region->pid,
                    'deep' => $region->deep,
                ];

                // 递归获取子节点
                if ($region->deep < 2) { // 只到区县级
                    $children = self::buildTree($region->id);
                    if ($children->isNotEmpty()) {
                        $item['children'] = $children;
                    }
                }

                return $item;
            });
        });
    }

    /**
     * 根据代码获取地区
     */
    public static function findByCode($code)
    {
        return Cache::remember("region_code_{$code}", 3600, function () use ($code) {
            return self::where('ext_id', $code)->first();
        });
    }

    /**
     * 搜索地区
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
                ->orWhere('ext_name', 'like', "%{$keyword}%")
                ->orWhere('pinyin', 'like', "%{$keyword}%")
                ->orWhere('pinyin_prefix', 'like', "{$keyword}%");
        });
    }

    /**
     * 作用域：只查询省份
     */
    public function scopeProvinces($query)
    {
        return $query->where('deep', 0);
    }

    /**
     * 作用域：获取指定省份的所有城市
     */
    public function scopeCitiesOfProvince($query, $provinceId)
    {
        return $query->where('pid', $provinceId)->where('deep', 1);
    }

    /**
     * 作用域：获取指定城市的所有区县
     */
    public function scopeDistrictsOfCity($query, $cityId)
    {
        return $query->where('pid', $cityId)->where('deep', 2);
    }

    /**
     * 批量获取地区信息
     */
    public static function getByCodesWithCache(array $codes)
    {
        $regions = [];
        $uncachedCodes = [];

        // 先从缓存获取
        foreach ($codes as $code) {
            $cached = Cache::get("region_code_{$code}");
            if ($cached) {
                $regions[$code] = $cached;
            } else {
                $uncachedCodes[] = $code;
            }
        }

        // 批量查询未缓存的
        if (! empty($uncachedCodes)) {
            $uncachedRegions = self::whereIn('ext_id', $uncachedCodes)->get();
            foreach ($uncachedRegions as $region) {
                Cache::put("region_code_{$region->ext_id}", $region, 3600);
                $regions[$region->ext_id] = $region;
            }
        }

        return $regions;
    }
}
