<template>
  <div class="tag-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @search="handleSearch"
      @reset="handleReset"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="handleAdd">
            <ElIcon><Plus /></ElIcon>
            新增标签
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 标签表格 -->
      <ArtTable
        ref="tableRef"
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
      >
        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="edit" @click="handleEdit(row)" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 标签编辑对话框 -->
      <TagDialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :is-edit="isEdit"
        :tag-data="currentTag"
        @success="handleDialogSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Tag' })

  // Vue 核心
  import { ref, reactive } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
  import TagDialog from './components/TagDialog.vue'

  // API
  import { getTagList, deleteTag } from '@/api/admin/tag'

  // 类型定义
  import type { Tag, TagForm, TagSearchParams } from '@/types/api/tag'
  import type { SearchFormItem } from '@/types'

  // 搜索表单状态
  const searchFormState = ref<TagSearchParams>({
    name: '',
    category: ''
  })

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      label: '标签名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入标签名称'
      }
    },
    {
      label: '分类',
      prop: 'category',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入分类'
      }
    }
  ]

  // 对话框相关状态
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const isEdit = ref(false)
  const currentTag = ref<TagForm | undefined>()
  const tableRef = ref()

  // 使用 useTable 管理表格
  const {
    tableData,
    isLoading,
    columns,
    columnChecks,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove
  } = useTable<Tag>({
    core: {
      apiFn: async (params) => {
        // 构建搜索参数，合并分页参数和搜索条件
        const searchParams: TagSearchParams = {
          ...params // 包含 page 和 per_page
        }

        // 添加搜索条件，过滤空值
        if (searchFormState.value.name?.trim()) {
          searchParams.name = searchFormState.value.name.trim()
        }
        if (searchFormState.value.category?.trim()) {
          searchParams.category = searchFormState.value.category.trim()
        }

        const response = await getTagList(searchParams)

        // 返回 useTable 期望的格式
        return {
          records: response.data,
          total: response.meta.total,
          current: response.meta.current_page,
          size: response.meta.per_page
        }
      },
      columnsFactory: () => [
        {
          prop: 'id',
          label: 'ID',
          width: 80
        },
        {
          prop: 'name',
          label: '标签名称',
          minWidth: 150
        },
        {
          prop: 'category',
          label: '分类',
          minWidth: 120
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          fixed: 'right',
          useSlot: true
        }
      ]
    }
  })

  // 搜索功能
  const handleSearch = () => {
    refreshAll()
  }

  // 重置搜索
  const handleReset = () => {
    searchFormState.value.name = ''
    searchFormState.value.category = ''
    refreshAll()
  }

  // 新增标签
  const handleAdd = () => {
    dialogTitle.value = '新增标签'
    isEdit.value = false
    currentTag.value = {
      name: '',
      category: ''
    }
    dialogVisible.value = true
  }

  // 编辑标签
  const handleEdit = (row: Tag) => {
    dialogTitle.value = '编辑标签'
    isEdit.value = true
    currentTag.value = {
      id: row.id,
      name: row.name,
      category: row.category
    }
    dialogVisible.value = true
  }

  // 删除标签
  const handleDelete = async (row: Tag) => {
    try {
      await ElMessageBox.confirm(`确定要删除标签"${row.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteTag(row.id)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    if (isEdit.value) {
      refreshAfterUpdate()
    } else {
      refreshAfterCreate()
    }
  }
</script>

<style lang="scss" scoped></style>
