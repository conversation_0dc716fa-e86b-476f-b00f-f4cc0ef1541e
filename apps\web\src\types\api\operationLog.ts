/**
 * 操作日志相关类型定义
 */

/**
 * 操作日志项
 */
export interface OperationLog {
  id: number
  user_id: number | null
  user_name: string | null
  menu_id: number | null
  menu_name: string | null
  operation_type: string | null
  operation_type_text?: string
  operation_description: string | null
  target_type: string | null
  target_id: number | null
  target_name: string | null
  full_operation_description?: string
  method: string
  path: string
  ip: string
  headers: Record<string, any>
  params: Record<string, any>
  user_agent: string
  created_at: number
  updated_at: number
}

/**
 * 操作日志搜索参数
 */
export interface OperationLogSearchParams {
  user_name?: string
  menu_name?: string
  ip?: string
  method?: string
  path?: string
  start_time?: string
  end_time?: string
  page?: number
  per_page?: number
}

/**
 * 操作日志分页响应
 */
export interface OperationLogListData {
  data: OperationLog[]
  total: number
  current_page: number
  per_page: number
  last_page: number
}
