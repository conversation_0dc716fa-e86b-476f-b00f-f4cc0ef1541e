<?php

namespace App\Support;

use Carbon\Carbon;

/**
 * 时间字符串转时间戳
 */
if (! function_exists('string_to_timestamp')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function string_to_timestamp(?string $dateString)
    {
        if (empty($dateString)) {
            return null;
        }
        $dt = Carbon::parse($dateString, config('app.timezone'));

        return $dt->timestamp;
    }
}

/**
 * 时间戳格式化
 */
if (! function_exists('timestamp_to_format')) {
    /**
     * 将时间戳或日期字符串格式化为指定格式
     *
     * @param  int|string|null  $value  时间戳或可解析的日期字符串
     * @param  string  $date_to_format  目标格式，默认"Y-m-d H:i:s"
     * @return string|null 格式化后的日期字符串，无效输入返回null
     */
    function timestamp_to_format(int|string|null $value, string $date_to_format = 'Y-m-d H:i:s'): ?string
    {
        // 处理空值
        if (empty($value)) {
            return null;
        }

        try {
            // 直接使用Carbon解析值，自动识别时间戳或日期字符串
            return Carbon::parse($value, config('app.timezone'))->format($date_to_format);
        } catch (\Exception $e) {
            // 处理解析失败的情况
            return null;
        }
    }
}

/**
 * 判断一个值是否为有效的 Unix 时间戳
 *
 * @param  mixed  $value  待判断的值
 * @return bool
 */
if (! function_exists('is_timestamp')) {
    function is_timestamp($value): bool
    {
        // 1. 必须是数字（整数或带小数的数字，支持字符串形式的数字）
        if (! is_numeric($value)) {
            return false;
        }

        // 2. 转换为浮点数（处理整数、字符串数字、小数等情况）
        $timestamp = (float) $value;

        // 3. 时间戳范围校验（合理范围：1970年之前一点 到 2100年之后）
        // 注意：PHP 的 strtotime() 对过大的数值会返回 false
        if ($timestamp < -2147483648 || $timestamp > 2147483648 * 10) {
            return false;
        }

        // 4. 通过 date() 反向验证（将时间戳转回日期再生成时间戳，判断是否一致）
        return (string) date('U', $timestamp) === (string) floor($timestamp);
    }
}
