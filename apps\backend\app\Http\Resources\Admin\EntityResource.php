<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EntityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'tax_number' => $this->tax_number,
            'entity_type' => $this->entity_type,
            'address' => $this->address,
            'phone' => $this->phone,
            'keywords' => $this->keywords,
            'remark' => $this->remark,
            'contacts' => EntityContactResource::collection($this->whenLoaded('contacts')),
            'contacts_count' => $this->when(isset($this->contacts_count), $this->contacts_count),
            'brands' => EntityBrandResource::collection($this->whenLoaded('brands')),
            'brands_count' => $this->when(isset($this->brands_count), $this->brands_count),
            'attachments' => $this->whenLoaded('attachments'),
            'attachments_count' => $this->when(isset($this->attachments_count), $this->attachments_count),
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
