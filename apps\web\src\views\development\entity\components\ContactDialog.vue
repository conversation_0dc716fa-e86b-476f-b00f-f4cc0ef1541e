<template>
  <ElDrawer
    :model-value="visible"
    @update:model-value="handleVisibleChange"
    :title="`${entityName} - 联系人管理`"
    size="50%"
    direction="rtl"
    destroy-on-close
  >
    <div class="contact-drawer-content">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="showDialog('add')" v-ripple>
            <ElIcon><Plus /></ElIcon>
            新增联系人
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 联系人表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id', height: 'calc(100vh - 200px)' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="edit" @click="showDialog('edit', row)" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 新增/编辑联系人对话框 -->
      <ContactFormDialog
        v-model:visible="dialogVisible"
        v-model:contact="selectedContact"
        :entity-id="entityId"
        @success="handleDialogSuccess"
      />
    </div>
  </ElDrawer>
</template>

<script setup lang="ts">
  defineOptions({ name: 'ContactDialog' })

  // Vue 核心
  import { ref, watch } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  // 工具库
  import dayjs from 'dayjs'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import ContactFormDialog from './ContactFormDialog.vue'

  // API
  import { getEntityContacts, deleteContact } from '@/api/admin/entity'
  import type { Contact } from '@/types/api'

  interface Props {
    visible: boolean
    entityId: string
    entityName: string
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['update:visible', 'refresh'])

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedContact = ref<Contact | null>(null)

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange
  } = useTable<Contact>({
    core: {
      apiFn: async (params: any) => {
        if (!props.entityId) {
          return { records: [], total: 0, current: 1, size: 20 }
        }

        const response = await getEntityContacts(props.entityId, {
          current: params.current,
          size: params.size
        })

        return {
          records: response.data,
          total: response.total,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10
      },
      columnsFactory: () => [
        { prop: 'name', label: '联系人', width: 120 },
        { prop: 'phone', label: '联系方式', width: 150 },
        { prop: 'position', label: '职位', width: 150 },
        { prop: 'department', label: '所在部门', width: 150 },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 180,
          formatter: (row: any) => {
            return row.created_at || row.createTime
              ? dayjs(row.created_at || row.createTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: false
    }
  })

  // 处理对话框显示状态变化
  const handleVisibleChange = (val: boolean) => {
    emit('update:visible', val)
  }

  // 显示对话框
  const showDialog = (type: 'add' | 'edit', row?: Contact) => {
    if (type === 'edit' && row) {
      selectedContact.value = row
    } else {
      selectedContact.value = null
    }
    dialogVisible.value = true
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    const isAdd = !selectedContact.value?.id
    if (isAdd) {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 删除联系人
  const handleDelete = async (row: Contact) => {
    try {
      await ElMessageBox.confirm(`确定要删除联系人"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteContact(props.entityId, row.id)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 监听对话框显示状态，加载数据
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal && props.entityId) {
        refreshAll()
      }
    }
  )

  // 监听 entityId 变化
  watch(
    () => props.entityId,
    (newVal) => {
      if (newVal && props.visible) {
        refreshAll()
      }
    }
  )
</script>

<style lang="scss" scoped>
  .contact-drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;

    .art-table {
      flex: 1;
    }
  }
</style>
