<?php

namespace Tests\Feature;

use App\Models\Attachment;
use App\Models\ImportTask;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImportControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
        
        // 模拟队列
        Queue::fake();
        
        // 模拟存储
        Storage::fake('local');
    }

    /** @test */
    public function it_can_create_import_task_for_asset()
    {
        // 创建测试附件
        $attachment = $this->createTestAttachment();

        // 发送导入请求
        $response = $this->postJson("/api/admin/import/asset/{$attachment->id}");

        // 断言响应
        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'task_id',
                'type',
            ])
            ->assertJson([
                'type' => 'asset',
            ]);

        // 断言数据库中创建了任务
        $this->assertDatabaseHas('import_tasks', [
            'type' => 'asset',
            'file_path' => $attachment->file_path,
            'original_filename' => $attachment->file_name,
            'status' => ImportTask::STATUS_PENDING,
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_can_create_import_task_for_category()
    {
        $attachment = $this->createTestAttachment();

        $response = $this->postJson("/api/admin/import/category/{$attachment->id}");

        $response->assertStatus(201)
            ->assertJson(['type' => 'category']);

        $this->assertDatabaseHas('import_tasks', [
            'type' => 'category',
            'status' => ImportTask::STATUS_PENDING,
        ]);
    }

    /** @test */
    public function it_can_create_import_task_for_entity()
    {
        $attachment = $this->createTestAttachment();

        $response = $this->postJson("/api/admin/import/entity/{$attachment->id}");

        $response->assertStatus(201)
            ->assertJson(['type' => 'entity']);

        $this->assertDatabaseHas('import_tasks', [
            'type' => 'entity',
            'status' => ImportTask::STATUS_PENDING,
        ]);
    }

    /** @test */
    public function it_can_create_import_task_for_user()
    {
        $attachment = $this->createTestAttachment();

        $response = $this->postJson("/api/admin/import/user/{$attachment->id}");

        $response->assertStatus(201)
            ->assertJson(['type' => 'user']);

        $this->assertDatabaseHas('import_tasks', [
            'type' => 'user',
            'status' => ImportTask::STATUS_PENDING,
        ]);
    }

    /** @test */
    public function it_rejects_unsupported_import_type()
    {
        $attachment = $this->createTestAttachment();

        $response = $this->postJson("/api/admin/import/unsupported/{$attachment->id}");

        $response->assertStatus(422)
            ->assertJson([
                'message' => '不支持的导入类型',
            ]);
    }

    /** @test */
    public function it_can_get_import_task_status()
    {
        // 创建测试任务
        $task = ImportTask::factory()->create([
            'type' => 'asset',
            'status' => ImportTask::STATUS_COMPLETED,
            'total_rows' => 100,
            'success_rows' => 95,
            'failed_rows' => 5,
            'created_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/admin/import/asset/tasks/{$task->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'type',
                'type_text',
                'status',
                'status_text',
                'total_rows',
                'success_rows',
                'failed_rows',
                'progress_percent',
                'created_at',
                'updated_at',
            ])
            ->assertJson([
                'id' => $task->id,
                'type' => 'asset',
                'status' => ImportTask::STATUS_COMPLETED,
                'total_rows' => 100,
                'success_rows' => 95,
                'failed_rows' => 5,
            ]);
    }

    /** @test */
    public function it_returns_404_for_non_existent_task()
    {
        $response = $this->getJson("/api/admin/import/asset/tasks/999999");

        $response->assertStatus(404)
            ->assertJson([
                'message' => '导入任务不存在',
                'error_code' => 'IMPORT_TASK_NOT_FOUND',
            ]);
    }

    /** @test */
    public function it_can_get_import_task_list()
    {
        // 创建多个测试任务
        ImportTask::factory()->count(3)->create([
            'type' => 'asset',
            'created_by' => $this->user->id,
        ]);

        ImportTask::factory()->count(2)->create([
            'type' => 'category',
            'created_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/admin/import/asset/tasks");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'type',
                        'status',
                        'total_rows',
                        'success_rows',
                        'failed_rows',
                    ]
                ],
                'current_page',
                'last_page',
                'per_page',
                'total',
            ]);

        // 断言只返回asset类型的任务
        $data = $response->json('data');
        $this->assertCount(3, $data);
        foreach ($data as $task) {
            $this->assertEquals('asset', $task['type']);
        }
    }

    /** @test */
    public function it_validates_task_type_in_status_endpoint()
    {
        $task = ImportTask::factory()->create(['type' => 'asset']);

        // 尝试用错误的类型查询任务
        $response = $this->getJson("/api/admin/import/category/tasks/{$task->id}");

        $response->assertStatus(404);
    }

    /**
     * 创建测试附件
     */
    protected function createTestAttachment(): Attachment
    {
        // 创建测试Excel文件
        $file = UploadedFile::fake()->create('test.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        
        // 存储文件
        $path = $file->store('imports', 'local');

        return Attachment::factory()->create([
            'file_name' => 'test.xlsx',
            'file_path' => $path,
            'file_size' => 100,
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }
}
