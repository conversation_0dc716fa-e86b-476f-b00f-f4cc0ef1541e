<template>
  <ElDialog
    v-model="dialogVisible"
    title="操作日志详情"
    width="800px"
    align-center
    destroy-on-close
  >
    <div v-if="log" class="log-detail">
      <!-- 基本信息 -->
      <ElDescriptions :column="2" border>
        <ElDescriptionsItem label="用户名">
          {{ log.user_name || '未登录用户' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户ID">
          {{ log.user_id || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="菜单名称">
          {{ log.menu_name || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="操作类型">
          {{ log.operation_type_text || log.operation_type || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="操作描述" :span="2">
          {{ log.full_operation_description || log.operation_description || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="操作目标">
          {{ log.target_type || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="目标名称">
          {{ log.target_name || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="请求方法">
          <ElTag :type="getMethodTagType(log.method)" size="small">
            {{ log.method }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="IP地址">
          {{ log.ip }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="请求路径" :span="2">
          {{ log.path }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户代理" :span="2">
          {{ log.user_agent }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="操作时间" :span="2">
          {{ log.created_at ? formatDate(log.created_at, 'YYYY-MM-DD HH:mm:ss') : '-' }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <!-- 请求头信息 -->
      <div class="section-title">请求头信息</div>
      <div class="json-content">
        <pre>{{ formatJson(log.headers) }}</pre>
      </div>

      <!-- 请求参数 -->
      <div class="section-title">请求参数</div>
      <div class="json-content">
        <pre>{{ formatJson(log.params) }}</pre>
      </div>
    </div>

    <template #footer>
      <ElButton @click="dialogVisible = false">关闭</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { ElDialog, ElDescriptions, ElDescriptionsItem, ElTag, ElButton } from 'element-plus'
  import { formatDate } from '@/utils/dataprocess/format'
  import type { OperationLog } from '@/types/api'

  // Props
  const props = defineProps<{
    visible: boolean
    log: OperationLog | null
  }>()

  // Emits
  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
  }>()

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 获取请求方法标签类型
  const getMethodTagType = (method: string) => {
    const typeMap: Record<string, string> = {
      GET: 'info',
      POST: 'success',
      PUT: 'warning',
      DELETE: 'danger',
      PATCH: 'warning'
    }
    return (typeMap[method] || 'info') as any
  }

  // 格式化 JSON
  const formatJson = (data: any) => {
    try {
      if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) {
        return '无数据'
      }
      return JSON.stringify(data, null, 2)
    } catch {
      return String(data)
    }
  }
</script>

<style lang="scss" scoped>
  .log-detail {
    .section-title {
      margin: 20px 0 10px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .json-content {
      max-height: 300px;
      padding: 12px;
      overflow: auto;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      pre {
        margin: 0;
        font-family: Consolas, Monaco, 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
        color: #606266;
        word-wrap: break-word;
        white-space: pre-wrap;
      }
    }
  }
</style>
