<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_menu_permissions', function (Blueprint $table) {
            $table->comment('角色菜单权限关联表');
            $table->id();
            $table->unsignedBigInteger('role_id')->comment('角色ID');
            $table->unsignedBigInteger('menu_id')->comment('菜单ID');
            $table->unsignedBigInteger('menu_permission_id')->nullable()->comment('菜单权限ID，为空表示只有菜单访问权限');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            // 添加索引
            $table->index('role_id');
            $table->index('menu_id');
            $table->index('menu_permission_id');

            // 确保同一角色对同一菜单的同一权限不重复
            $table->unique(['role_id', 'menu_id', 'menu_permission_id'], 'role_menu_permission_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_menu_permissions');
    }
};
