import request from '@/utils/http'

// ========== 二维码生成 ==========

/**
 * 二维码生成请求参数
 */
export interface QrCodeGenerateParams {
  /** 二维码内容 */
  content: string
  /** 二维码大小（像素），默认: 200 */
  size?: number
  /** 前景色，默认: #000000 */
  color?: string
  /** 背景色，默认: #ffffff */
  background?: string
  /** 边距，默认: 10 */
  margin?: number
}

/**
 * 二维码生成响应
 */
export interface QrCodeResponse {
  /** Base64格式的二维码图片数据 */
  qrcode: string
  /** 二维码内容 */
  content: string
  /** 生成时间 */
  generated_at: string
}

/**
 * 生成二维码
 */
export const generateQrCode = (params: QrCodeGenerateParams): Promise<QrCodeResponse> => {
  return request.post<QrCodeResponse>({
    url: '/api/admin/qrcode/generate',
    data: {
      content: params.content,
      size: params.size || 200,
      color: params.color || '#000000',
      background: params.background || '#ffffff',
      margin: params.margin || 10
    }
  })
}
