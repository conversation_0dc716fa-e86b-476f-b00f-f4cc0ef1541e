<template>
  <ElDrawer
    v-model="visible"
    :title="drawerTitle"
    :size="'75%'"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="dictionary-item-container">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="searchFormState"
        :items="searchFormItems"
        :showExpand="true"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll" style="margin-top: 16px">
        <template #left>
          <ElButton type="primary" @click="showDialog('add')" :disabled="!category">
            <ElIcon><Plus /></ElIcon>
            新增字典项
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 颜色列 -->
        <template #color="{ row }">
          <div
            v-if="row.color"
            :style="{
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              backgroundColor: row.color,
              margin: '0 auto',
              border: '1px solid var(--el-border-color)'
            }"
          />
        </template>

        <!-- 状态列 -->
        <template #enabled="{ row }">
          <ElTag :type="row.enabled ? 'success' : 'danger'">
            {{ row.enabled ? '启用' : '禁用' }}
          </ElTag>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="edit" @click="showDialog('edit', row)" />
            <ArtButtonTable type="delete" @click="deleteItem(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 编辑对话框 -->
      <DictionaryItemFormDialog
        v-model:visible="dialogVisible"
        v-model:item="selectedItem"
        :category-id="category?.id || ''"
        @success="handleDialogSuccess"
      />
    </div>
  </ElDrawer>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import { useTable } from '@/composables/useTable'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import DictionaryItemFormDialog from './DictionaryItemFormDialog.vue'
  import dayjs from 'dayjs'
  import { getDictionaryItems, deleteDictionaryItem } from '@/api/admin/dictionary'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import type { SearchFormItem } from '@/types'
  import type { DictionaryCategory, DictionaryItem } from '@/types/api'

  interface Props {
    modelValue: boolean
    category: DictionaryCategory | null
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    refresh: []
  }>()

  const dictionaryStore = useDictionaryStore()

  // 抽屉显示控制
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 抽屉标题
  const drawerTitle = computed(() => {
    return props.category ? `字典项管理 - ${props.category.name}` : '字典项管理'
  })

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedItem = ref<DictionaryItem | null>(null)

  // 搜索表单状态
  const searchFormState = ref({
    code: '',
    value: '',
    enabled: ''
  })

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      prop: 'code',
      label: '字典编码',
      type: 'input',
      placeholder: '请输入字典编码'
    },
    {
      prop: 'value',
      label: '字典值',
      type: 'input',
      placeholder: '请输入字典值'
    },
    {
      prop: 'enabled',
      label: '是否启用',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ]

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<DictionaryItem>({
    core: {
      apiFn: async (params: any) => {
        if (!props.category) {
          return { records: [], total: 0, current: 1, size: 20 }
        }

        // 获取所有字典项（后端不再分页）
        const allItems = await getDictionaryItems({
          categoryId: props.category.id
        })

        // 按照sort排序，数值越大越靠前
        const sortedItems = allItems.sort((a, b) => (b.sort || 0) - (a.sort || 0))

        // 前端过滤
        let filteredData = sortedItems
        if (params.code) {
          filteredData = filteredData.filter((item) =>
            item.code.toLowerCase().includes(params.code.toLowerCase())
          )
        }
        if (params.value) {
          filteredData = filteredData.filter((item) =>
            item.value.toLowerCase().includes(params.value.toLowerCase())
          )
        }
        if (params.enabled !== '') {
          const enabled = params.enabled === '1'
          filteredData = filteredData.filter((item) => item.enabled === enabled)
        }

        // 前端分页
        const total = filteredData.length
        const start = (params.current - 1) * params.size
        const end = start + params.size
        const records = filteredData.slice(start, end)

        return { records, total, current: params.current, size: params.size }
      },
      apiParams: {
        current: 1,
        size: 20,
        code: '',
        value: '',
        enabled: ''
      },
      columnsFactory: () => [
        {
          prop: 'code',
          label: '字典编码',
          minWidth: 120
        },
        {
          prop: 'value',
          label: '字典值',
          minWidth: 150
        },
        {
          prop: 'color',
          label: '颜色',
          width: 80,
          align: 'center',
          useSlot: true
        },
        {
          prop: 'sort',
          label: '排序',
          width: 80,
          align: 'center'
        },
        {
          prop: 'enabled',
          label: '状态',
          width: 80,
          align: 'center',
          useSlot: true
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'config',
          label: '扩展配置',
          minWidth: 150,
          showOverflowTooltip: true,
          formatter: (row: DictionaryItem) => {
            return row.config ? JSON.stringify(row.config) : ''
          }
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 160,
          formatter: (row: DictionaryItem) => {
            return row.created_at ? dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: false
    }
  })

  // 搜索相关方法
  const handleReset = () => {
    searchFormState.value = {
      code: '',
      value: '',
      enabled: ''
    }
    resetSearch()
  }

  const handleSearch = () => {
    searchState.code = searchFormState.value.code
    searchState.value = searchFormState.value.value
    searchState.enabled = searchFormState.value.enabled
    searchData()
  }

  // 对话框相关方法
  const showDialog = (type: 'add' | 'edit', row?: DictionaryItem) => {
    if (type === 'add') {
      selectedItem.value = null
    } else if (row) {
      selectedItem.value = row
    }
    dialogVisible.value = true
  }

  const handleDialogSuccess = async () => {
    // 刷新表格数据
    const isAdd = !selectedItem.value?.id
    if (isAdd) {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }

    // 更新store缓存
    if (props.category) {
      await dictionaryStore.refreshCategory(props.category.code)
    }
    // 通知父组件刷新
    emit('refresh')
  }

  const deleteItem = (row: DictionaryItem) => {
    ElMessageBox.confirm('确定要删除该字典项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await deleteDictionaryItem(row.id)
      ElMessage.success('删除成功')

      // 刷新表格数据
      refreshAfterRemove()
      // 更新store缓存
      if (props.category) {
        await dictionaryStore.refreshCategory(props.category.code)
      }
      // 通知父组件刷新
      emit('refresh')
    })
  }

  // 关闭抽屉
  const handleClose = () => {
    visible.value = false
  }

  // 监听分类变化，重新加载数据
  watch(
    () => props.category,
    () => {
      if (visible.value && props.category) {
        refreshAll()
      }
    }
  )

  // 监听抽屉显示状态
  watch(visible, (val) => {
    if (val && props.category) {
      refreshAll()
    }
  })
</script>

<style lang="scss" scoped>
  .dictionary-item-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;

    .art-table {
      flex: 1;
      margin-top: 20px;
    }
  }
</style>
