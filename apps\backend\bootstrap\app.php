<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/status',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // 注册自定义中间件别名
        $middleware->alias([
            'api.auth' => \App\Http\Middleware\ApiAuthenticate::class,
        ]);

        // API路由组中间件
        $middleware->append(
            [
                \App\Http\Middleware\ForceJsonResponse::class,
                \App\Http\Middleware\OperationLogMiddleware::class,  // 添加操作日志中间件
            ]
        );
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // API路由始终返回JSON
        $exceptions->shouldRenderJsonWhen(function ($request, $throwable) {
            return $request->is('api/*');
        });
    })->create();
