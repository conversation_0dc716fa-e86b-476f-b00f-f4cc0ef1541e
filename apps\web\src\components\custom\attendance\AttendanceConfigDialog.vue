<template>
  <ElDialog
    v-model="visible"
    :title="title || (type === 'add' ? '新增考勤配置' : '编辑考勤配置')"
    width="80%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <!-- 地理位置配置 -->
      <ElDivider content-position="left">地理位置配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="打卡地点" prop="location">
            <ElInput v-model="formData.location" placeholder="请输入打卡地点名称" clearable>
              <template #append>
                <ElButton @click="showMapSelector">选择位置</ElButton>
              </template>
            </ElInput>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="打卡范围" prop="location_range">
            <ElInput
              v-model.number="formData.location_range"
              placeholder="请输入打卡范围"
              type="number"
              min="1"
              max="10000"
            >
              <template #suffix>
                <span style="color: #909399">米</span>
              </template>
            </ElInput>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="经度" prop="longitude">
            <ElInputNumber
              v-model="formData.longitude"
              placeholder="经度"
              :precision="6"
              :min="-180"
              :max="180"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="纬度" prop="latitude">
            <ElInputNumber
              v-model="formData.latitude"
              placeholder="纬度"
              :precision="6"
              :min="-90"
              :max="90"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 时间配置 -->
      <ElDivider content-position="left">时间配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="打卡时间" prop="checkin_time">
            <ElTimePicker
              v-model="checkinTimeValue"
              placeholder="请选择打卡时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 拍照配置 -->
      <ElDivider content-position="left">拍照配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="需要拍照打卡" prop="is_photo">
            <ElSwitch v-model="isPhotoValue" active-text="需要" inactive-text="不需要" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 人员配置 -->
      <ElDivider content-position="left">人员配置</ElDivider>

      <ElFormItem label="参与人员" prop="user_ids">
        <UserSelectTransfer v-model="formData.user_ids" :users="filteredUserOptions" />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit" :loading="submitting"> 确定 </ElButton>
      </span>
    </template>

    <!-- 地图选择器 -->
    <MapSelector
      v-model="mapSelectorVisible"
      :init-location="{
        address: formData.location,
        longitude: formData.longitude,
        latitude: formData.latitude
      }"
      @confirm="handleMapConfirm"
    />
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AttendanceConfigDialog' })

  // Vue 核心
  import { ref, reactive, watch, computed } from 'vue'

  // UI 框架
  import {
    ElMessage,
    ElButton,
    ElInputNumber,
    ElTimePicker,
    ElRadioGroup,
    ElRadio,
    ElDivider,
    ElSwitch
  } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // 组件
  import UserSelectTransfer from './UserSelectTransfer.vue'
  import MapSelector from '@/components/custom/map-selector/index.vue'

  // API
  import { getUserOptions, timeToTimestamp, timestampToTime } from '@/api/admin/attendance'

  // 类型定义
  import type { CheckinConfig, UserOption } from '@/types/api/attendance'
  import type { LocationInfo } from '@/components/custom/map-selector'

  // Props
  const props = defineProps<{
    type: 'add' | 'edit'
    data?: CheckinConfig | null
    availableUserIds?: number[] // 可选人员ID列表
    title?: string // 自定义标题
  }>()

  // Emits - 返回纯表单数据，不包含 attachable_type 和 attachable_id
  const emit = defineEmits<{
    success: [
      data: {
        location: string
        latitude: number
        longitude: number
        location_range: number
        checkin_time: number
        user_ids: number[]
        is_photo: number
      }
    ]
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 表单相关
  const formRef = ref<FormInstance>()
  const submitting = ref(false)

  // 表单数据 - 不包含 attachable_type 和 attachable_id
  const formData = reactive({
    location: '',
    latitude: 0,
    longitude: 0,
    location_range: 500,
    checkin_time: 0,
    user_ids: [] as number[],
    is_photo: 0
  })

  // 辅助计算属性 - 用于时间选择器
  const checkinTimeValue = computed({
    get: () => (formData.checkin_time ? timestampToTime(formData.checkin_time) : '08:30'),
    set: (val: string) => {
      formData.checkin_time = timeToTimestamp(val)
    }
  })

  // 辅助计算属性 - 用于拍照开关
  const isPhotoValue = computed({
    get: () => formData.is_photo === 1,
    set: (val: boolean) => {
      formData.is_photo = val ? 1 : 0
    }
  })

  // 表单规则
  const rules: FormRules = {
    location: [{ required: true, message: '请输入打卡地点', trigger: 'blur' }],
    latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
    longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
    location_range: [{ required: true, message: '请输入打卡范围', trigger: 'blur' }],
    checkin_time: [{ required: true, message: '请选择打卡时间', trigger: 'change' }],
    user_ids: [{ required: true, message: '请选择参与人员', trigger: 'change' }]
  }

  // 数据源
  const userOptions = ref<UserOption[]>([])

  // 过滤后的用户选项
  const filteredUserOptions = computed(() => {
    if (!props.availableUserIds?.length) {
      return userOptions.value // 不限制，显示所有用户
    }
    // 只显示允许的用户
    return userOptions.value.filter((u) => props.availableUserIds!.includes(u.id))
  })

  // 地图选择器
  const mapSelectorVisible = ref(false)

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      location: '',
      latitude: 0,
      longitude: 0,
      location_range: 500,
      checkin_time: timeToTimestamp('08:30'),
      user_ids: [],
      is_photo: 0
    })
  }

  // 监听props变化
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        // 赋值表单数据，排除 attachable_type 和 attachable_id
        Object.assign(formData, {
          location: newData.location || '',
          latitude: newData.latitude || 0,
          longitude: newData.longitude || 0,
          location_range: newData.location_range || 500,
          checkin_time: newData.checkin_time || timeToTimestamp('08:30'),
          user_ids: newData.users?.map((u) => u.id) || [],
          is_photo: newData.is_photo || 0
        })
      } else {
        resetForm()
      }
    },
    { immediate: true }
  )

  // 监听弹窗显示状态
  watch(visible, async (newVal) => {
    if (newVal) {
      await loadUserOptions()

      // 不再默认全选，让用户自己选择需要的人员
      // if (props.availableUserIds?.length && props.type === 'add') {
      //   formData.user_ids = [...props.availableUserIds]
      // }
    }
  })

  // 加载用户选项
  const loadUserOptions = async () => {
    try {
      const response = await getUserOptions()
      userOptions.value = response.data || []
    } catch (error) {
      console.error('加载用户列表失败:', error)
    }
  }

  // 显示地图选择器
  const showMapSelector = () => {
    mapSelectorVisible.value = true
  }

  // 处理地图选择回调
  const handleMapConfirm = (location: LocationInfo) => {
    formData.location = location.address
    formData.latitude = location.latitude
    formData.longitude = location.longitude
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    resetForm()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitting.value = true

      // 返回纯表单数据，由外部组装完整数据
      const submitData = {
        location: formData.location,
        latitude: formData.latitude,
        longitude: formData.longitude,
        location_range: formData.location_range,
        checkin_time: formData.checkin_time,
        user_ids: formData.user_ids,
        is_photo: formData.is_photo
      }

      emit('success', submitData)
      handleClose()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      submitting.value = false
    }
  }
</script>

<style lang="scss" scoped>
  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
</style>
