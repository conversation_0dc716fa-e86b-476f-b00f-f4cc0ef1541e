/**
 * 配置管理相关类型定义
 */

// 系统配置
export interface SystemConfig {
  system_name: string
  system_logo: number | null
}

// 上传配置
export interface UploadConfig {
  storage_type: 'local' | 'aliyun' | null
  aliyun_access_key: string
  aliyun_secret_key: string
  aliyun_bucket: string
  aliyun_region: string
  aliyun_sts_role_arn?: string // RAM角色ARN（用于STS临时凭证）
}

// 配置组合类型
export interface AppConfig {
  system: SystemConfig
  upload: UploadConfig
}

// 配置更新请求（已废弃，保留向后兼容）
export interface UpdateSystemConfigRequest {
  system_name: string
  system_logo: number | null
}

export interface UpdateUploadConfigRequest {
  storage_type: 'local' | 'aliyun' | null
  aliyun_access_key?: string
  aliyun_secret_key?: string
  aliyun_bucket?: string
  aliyun_region?: string
  aliyun_sts_role_arn?: string
}

// 统一的配置更新请求
export interface UpdateConfigsRequest {
  configs: AppConfig
}
