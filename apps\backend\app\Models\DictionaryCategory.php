<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $code 分类编码
 * @property string $name 分类名称
 * @property string|null $description 分类描述
 * @property int $sort 排序
 * @property bool $is_enabled 是否启用
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DictionaryItem> $enabledItems
 * @property-read int|null $enabled_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DictionaryItem> $items
 * @property-read int|null $items_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory enabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereIsEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory withoutTrashed()
 *
 * @mixin \Eloquent
 */
class DictionaryCategory extends Model
{
    use HasFactory, SoftDeletes;

    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'sort',
        'is_enabled',
        'created_by',
        'updated_by',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'sort' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取字典项
     */
    public function items(): HasMany
    {
        return $this->hasMany(DictionaryItem::class, 'category_id');
    }

    /**
     * 获取启用的字典项
     */
    public function enabledItems(): HasMany
    {
        return $this->items()->where('is_enabled', true)->orderBy('sort');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort')->orderBy('id');
    }
}
