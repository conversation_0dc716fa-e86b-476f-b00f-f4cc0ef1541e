/**
 * 统一的批量导入导出 API
 * 支持类型: asset | user | category | entity
 */

import request from '@/utils/http'

/**
 * 导入任务接口
 */
export interface ImportTask {
  id: number
  type: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  original_filename: string
  total_rows: number
  success_rows: number
  failed_rows: number
  error_details?: Array<{
    row: number
    error: string
    data: any
  }>
  summary?: string
  started_at?: string
  completed_at?: string
  created_at: string
}

/**
 * 支持的导入导出类型
 */
export type ImportExportType = 'asset' | 'user' | 'category' | 'entity'

/**
 * 导出模板
 * @param type 导出类型
 * @param params 额外参数（如category支持with_sample）
 */
export const exportTemplate = (
  type: ImportExportType,
  params?: Record<string, any>
): Promise<Blob> => {
  return request.get<Blob>({
    url: `/api/admin/export/${type}/template`,
    params,
    responseType: 'blob'
  })
}

/**
 * 批量导入数据
 * @param type 导入类型
 * @param attachmentId 附件ID
 */
export const importData = (
  type: ImportExportType,
  attachmentId: number
): Promise<{
  message: string
  task_id: number
  type: string
}> => {
  return request.post<{
    message: string
    task_id: number
    type: string
  }>({
    url: `/api/admin/import/${type}/${attachmentId}`
  })
}

/**
 * 获取导入任务状态
 * @param type 导入类型
 * @param taskId 任务ID
 */
export const getImportTaskStatus = (
  type: ImportExportType,
  taskId: number
): Promise<ImportTask> => {
  return request.get<ImportTask>({
    url: `/api/admin/import/${type}/tasks/${taskId}`
  })
}

/**
 * 获取导入任务列表
 * @param type 导入类型
 */
export const getImportTaskList = (
  type: ImportExportType
): Promise<{
  data: ImportTask[]
  meta: {
    total: number
    per_page: number
    current_page: number
    last_page: number
  }
}> => {
  return request.get<{
    data: ImportTask[]
    meta: {
      total: number
      per_page: number
      current_page: number
      last_page: number
    }
  }>({
    url: `/api/admin/import/${type}/tasks`
  })
}

/**
 * 批量导出数据
 * @param type 导出类型
 * @param params 查询参数
 */
export const exportData = (type: ImportExportType, params?: Record<string, any>): Promise<Blob> => {
  return request.get<Blob>({
    url: `/api/admin/export/${type}/data`,
    params,
    responseType: 'blob'
  })
}

/**
 * 获取支持的导入导出类型列表
 */
export const getImportExportTypes = (): Promise<{
  data: Array<{
    type: string
    name: string
    description: string
    supports_sample?: boolean
  }>
}> => {
  return request.get<{
    data: Array<{
      type: string
      name: string
      description: string
      supports_sample?: boolean
    }>
  }>({
    url: '/api/admin/export/types'
  })
}

/**
 * 创建导出文件名
 * @param type 导出类型
 * @param suffix 文件后缀（'模板' | '数据'）
 */
export const generateFilename = (type: ImportExportType, suffix: string = '模板'): string => {
  const typeNames: Record<ImportExportType, string> = {
    asset: '资产',
    category: '分类',
    entity: '相关方',
    user: '用户'
  }

  const typeName = typeNames[type] || type
  const timestamp = new Date().toISOString().slice(0, 10)

  return `${typeName}导入${suffix}_${timestamp}.xlsx`
}

/**
 * 下载 Blob 文件
 * @param blob Blob 对象
 * @param filename 文件名
 */
export const downloadBlob = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
