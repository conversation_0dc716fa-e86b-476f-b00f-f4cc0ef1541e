<?php

namespace Database\Seeders;

use App\Services\ConfigService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据 - 先禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('system_configs')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $configService = new ConfigService;

        $configService->initializeDefaultConfigs();

        $this->command->info('系统配置初始化成功！');

    }
}
