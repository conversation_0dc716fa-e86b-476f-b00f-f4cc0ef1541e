// 用户管理相关接口
import type { UserListData, UserListItem, UserForm, UserSearchParams } from '@/types/api'
import request from '@/utils/http'

// 获取用户列表
export const getUserList = (params: UserSearchParams): Promise<UserListData> => {
  return request.get<UserListData>({
    url: '/api/admin/users',
    params: {
      ...params,
      per_page: params.per_page || params.size || 20
    }
  })
}

// 创建用户
export const createUser = (data: UserForm): Promise<UserListItem> => {
  return request.post<UserListItem>({
    url: '/api/admin/users',
    data
  })
}

// 更新用户
export const updateUser = (id: number | string, data: UserForm): Promise<UserListItem> => {
  return request.put<UserListItem>({
    url: `/api/admin/users/${id}`,
    data
  })
}

// 删除用户
export const deleteUser = (id: number | string): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/users/${id}`
  })
}

// 获取用户详情
export const getUserDetail = (id: number | string): Promise<UserListItem> => {
  return request.get<UserListItem>({
    url: `/api/admin/users/${id}`
  })
}
