import type { PaginatedResponse } from './pagination'

/**
 * 角色列表项
 */
export interface RoleListItem {
  /** 角色ID */
  id: number
  /** 角色名称 */
  name: string
  /** 角色描述 */
  description?: string
  /** 用户数量 */
  users_count?: number
  /** 菜单权限列表 */
  menus?: RoleMenuPermission[]
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 角色表单
 */
export interface RoleForm {
  /** 角色名称 */
  name: string
  /** 角色描述 */
  description?: string
}

/**
 * 角色搜索参数
 */
export interface RoleSearchParams {
  /** 搜索关键词 */
  search?: string
  /** 每页条数 */
  per_page?: number
  /** 当前页 */
  page?: number
}

/**
 * 角色列表响应数据
 */
export type RoleListData = PaginatedResponse<RoleListItem>

/**
 * 用户角色分配参数
 */
export interface UserRoleAssignParams {
  /** 角色ID数组 */
  role_ids: number[]
}

/**
 * 角色菜单权限
 */
export interface RoleMenuPermission {
  /** 菜单ID */
  menu_id: number
  /** 权限ID数组 */
  permission_ids?: number[]
}

/**
 * 角色权限分配参数
 */
export interface RolePermissionAssignParams {
  /** 菜单权限列表 */
  menus: RoleMenuPermission[]
}

/**
 * 角色权限响应数据
 */
export interface RolePermissionResponse {
  /** 角色ID */
  role_id: number
  /** 菜单权限列表 */
  menus: RoleMenuPermission[]
}
