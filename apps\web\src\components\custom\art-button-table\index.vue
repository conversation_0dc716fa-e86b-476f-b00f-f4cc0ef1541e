<!-- 表格按钮 - 自定义版本，支持插槽和更多自定义选项 -->
<template>
  <div
    :class="['btn-text', buttonClass]"
    :style="{ backgroundColor: buttonBgColor, color: iconColor }"
    @click="handleClick"
  >
    <!-- 优先使用插槽内容 -->
    <template v-if="$slots.default">
      <slot></slot>
    </template>
    <!-- 如果没有插槽，使用 content prop 或默认图标 -->
    <template v-else>
      <!-- 如果有 content，显示图标和文字 -->
      <template v-if="content">
        <!-- 如果图标是组件 -->
        <component v-if="isIconComponent" :is="icon" class="icon-component" />
        <!-- 如果图标是字符串 -->
        <i v-else-if="iconContent" class="iconfont-sys" v-html="iconContent"></i>
        <span v-if="content" class="btn-content">{{ content }}</span>
      </template>
      <!-- 如果没有 content，只显示图标 -->
      <template v-else>
        <component v-if="isIconComponent" :is="icon" class="icon-component" />
        <i v-else-if="iconContent" class="iconfont-sys" v-html="iconContent"></i>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { BgColorEnum } from '@/enums/appEnum'
  import type { Component } from 'vue'

  defineOptions({ name: 'ArtButtonTable' })

  interface Props {
    /** 按钮类型 */
    type?:
      | 'add'
      | 'edit'
      | 'delete'
      | 'more'
      | 'view'
      | 'detail'
      | 'followUp'
      | 'primary'
      | 'success'
      | 'warning'
      | 'danger'
      | 'info'
      | 'setting'
    /** 按钮文字内容 */
    content?: string
    /** 按钮图标 */
    icon?: string | Component
    /** 按钮样式类 */
    iconClass?: BgColorEnum
    /** icon 颜色 */
    iconColor?: string
    /** 按钮背景色 */
    buttonBgColor?: string
  }

  const props = withDefaults(defineProps<Props>(), {})

  const emit = defineEmits<{
    (e: 'click'): void
  }>()

  // 默认按钮配置
  const defaultButtons = {
    add: { icon: '&#xe602;', color: BgColorEnum.PRIMARY },
    edit: { icon: '&#xe642;', color: BgColorEnum.SECONDARY },
    delete: { icon: '&#xe783;', color: BgColorEnum.ERROR },
    view: { icon: '&#xe689;', color: BgColorEnum.INFO },
    detail: { icon: '&#xe689;', color: BgColorEnum.INFO },
    followUp: { icon: '&#xe6eb;', color: BgColorEnum.SUCCESS },
    setting: { icon: '&#xe755;', color: BgColorEnum.WARNING },
    more: { icon: '&#xe6df;', color: '' },
    // 新增的类型
    primary: { icon: '', color: BgColorEnum.PRIMARY },
    success: { icon: '', color: BgColorEnum.SUCCESS },
    warning: { icon: '', color: BgColorEnum.WARNING },
    danger: { icon: '', color: BgColorEnum.ERROR },
    info: { icon: '', color: BgColorEnum.INFO }
  } as const

  // 判断图标是否为组件
  const isIconComponent = computed(() => {
    return props.icon && typeof props.icon === 'object'
  })

  // 获取图标内容
  const iconContent = computed(() => {
    // 如果是 Component 类型的图标，返回空（在模板中通过 component 渲染）
    if (isIconComponent.value) return ''
    return props.icon || (props.type ? defaultButtons[props.type]?.icon : '') || ''
  })

  // 获取按钮样式类
  const buttonClass = computed(() => {
    return props.iconClass || (props.type ? defaultButtons[props.type]?.color : '') || ''
  })

  const handleClick = () => {
    emit('click')
  }
</script>

<style scoped lang="scss">
  .btn-text {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    min-width: 34px;
    height: 34px;
    padding: 0 10px;
    margin-right: 10px;
    font-size: 13px;
    line-height: 34px;
    color: #666;
    cursor: pointer;
    background-color: rgba(var(--art-gray-200-rgb), 0.7);
    border-radius: 6px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: rgba(var(--art-gray-300-rgb), 0.5);
    }

    // 文字内容样式
    .btn-content {
      font-size: 12px;
      white-space: nowrap;
    }

    // 图标样式
    .iconfont-sys {
      font-size: 16px;
    }

    // 组件图标样式
    .icon-component {
      font-size: 14px;
    }

    // 当有文字内容时，调整最小宽度
    &:has(.btn-content) {
      min-width: auto;
    }
  }
</style>
