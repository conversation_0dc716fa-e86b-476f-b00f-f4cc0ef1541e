<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ProcessAssetImportQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'asset:process-import-queue 
                            {--queue=default : 队列名称}
                            {--timeout=60 : 超时时间（秒）}
                            {--memory=128 : 内存限制（MB）}
                            {--tries=3 : 重试次数}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理资产导入队列任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $queue = $this->option('queue');
        $timeout = $this->option('timeout');
        $memory = $this->option('memory');
        $tries = $this->option('tries');

        $this->info('开始处理资产导入队列任务...');
        $this->info("队列: {$queue}");
        $this->info("超时时间: {$timeout}秒");
        $this->info("内存限制: {$memory}MB");
        $this->info("重试次数: {$tries}次");

        // 运行队列工作进程
        $exitCode = Artisan::call('queue:work', [
            '--queue' => $queue,
            '--timeout' => $timeout,
            '--memory' => $memory,
            '--tries' => $tries,
            '--verbose' => true,
        ]);

        if ($exitCode === 0) {
            $this->info('队列处理完成');
        } else {
            $this->error('队列处理失败');
        }

        return $exitCode;
    }
}
