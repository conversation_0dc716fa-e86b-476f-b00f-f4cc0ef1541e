<template>
  <ElDialog
    v-model="visible"
    :title="isEdit ? '编辑相关方' : '新相关方关方'"
    width="60%"
    align-center
    destroy-on-close
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="相关方名称" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入相关方名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="税号" prop="tax_number">
            <ElInput v-model="formData.tax_number" placeholder="请输入税号" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="单位地址" prop="address">
            <ElInput v-model="formData.address" placeholder="请输入单位地址" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="电话号码" prop="phone">
            <ElInput v-model="formData.phone" placeholder="请输入电话号码" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="相关方类型" prop="entity_type">
            <ElSelect v-model="formData.entity_type" placeholder="请选择相关方类型" clearable>
              <ElOption
                v-for="item in entityTypes"
                :key="item.code"
                :label="item.value"
                :value="item.code"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="特征词" prop="keywords">
            <ElInput v-model="formData.keywords" placeholder="请输入特征词" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <!-- 附件列表 -->
      <ElFormItem label="附件">
        <AttachmentUpload
          v-model="formData.attachments"
          :attachments="currentAttachments"
          :limit="10"
          :max-size="10"
          button-text="上传附件"
          tip-text="最多上传10个文件，单个文件不超过10MB"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleConfirm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createEntity, updateEntity, getEntityDetail } from '@/api/admin/entity'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { AttachmentUpload } from '@/components/custom/upload'
  import type { Entity, EntityForm } from '@/types/api'

  // Props
  interface Props {
    entityId?: string
  }
  const props = defineProps<Props>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>('visible', { default: false })
  const entity = defineModel<Entity | null>('entity', { default: null })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 计算属性判断是否编辑模式
  const isEdit = computed(() => !!entity.value?.id)

  // 表单实例
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const currentAttachments = ref<any[]>([])

  // 相关方类型数据
  const entityTypes = ref<any[]>([])

  // 表单数据
  const formData = ref<EntityForm>({
    name: '',
    tax_number: '',
    address: '',
    phone: '',
    entity_type: null,
    keywords: '',
    attachments: []
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [{ required: true, message: '请输入相关方名称', trigger: 'blur' }],
    tax_number: [{ required: true, message: '请输入税号', trigger: 'blur' }],
    address: [{ required: true, message: '请输入单位地址', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入电话号码', trigger: 'blur' }],
    entity_type: [{ required: true, message: '请选择相关方类型', trigger: 'change' }]
  }

  // 填充表单数据
  const fillFormData = (data: any) => {
    formData.value = {
      id: String(data.id),
      name: data.name,
      tax_number: data.tax_number,
      address: data.address,
      phone: data.phone,
      entity_type: data.entity_type || data.type || null,
      keywords: data.keywords || '',
      attachments: []
    }

    // 设置当前附件列表
    currentAttachments.value = data.attachments || []

    // 设置附件ID数组
    if (data.attachments && data.attachments.length > 0) {
      formData.value.attachments = data.attachments.map((file: any) => Number(file.id))
    } else {
      formData.value.attachments = []
    }
  }

  // 重置表单数据
  const resetFormData = () => {
    formData.value = {
      name: '',
      tax_number: '',
      address: '',
      phone: '',
      entity_type: null,
      keywords: '',
      attachments: []
    }
    currentAttachments.value = []
  }

  // 加载相关方类型
  const loadEntityTypes = async () => {
    try {
      const dictionaryStore = useDictionaryStore()
      const items = await dictionaryStore.fetchItemsByCode('entity_type')
      entityTypes.value = items.map((item) => ({
        code: item.code,
        value: item.value,
        color: item.color || '#909399'
      }))
    } catch (error) {
      console.error('获取相关方类型失败:', error)
      ElMessage.error('获取相关方类型失败')
    }
  }

  // 监听 visible 变化，加载类型数据
  watch(visible, async (val) => {
    if (val) {
      // 每次打开弹窗都重新加载实体类型，确保数据最新
      await loadEntityTypes()
    }
  })

  // 监听 entity 变化，更新表单数据
  watch(
    () => entity.value,
    async (newVal) => {
      if (newVal) {
        // 编辑模式
        if (props.entityId) {
          // 如果传入了 entityId，获取详情
          try {
            const detail = await getEntityDetail(props.entityId)
            fillFormData(detail)
          } catch (error) {
            console.error('获取相关方详情失败:', error)
            ElMessage.error('获取相关方详情失败')
          }
        } else {
          // 直接使用传入的数据
          fillFormData(newVal)
        }
      } else {
        // 新增模式，重置数据
        resetFormData()
      }
    },
    { immediate: true }
  )

  // 处理取消
  const handleCancel = () => {
    visible.value = false
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 直接使用 formData，不需要映射
      const submitData = { ...formData.value }

      if (isEdit.value) {
        await updateEntity(String(formData.value.id!), submitData)
        ElMessage.success('编辑成功')
      } else {
        await createEntity(submitData)
        ElMessage.success('新增成功')
      }

      visible.value = false
      emit('success')
    } catch (error: any) {
      if (error !== false) {
        // 不是验证错误
        console.error('保存相关方失败:', error)
        ElMessage.error(error.message || '保存失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭后的处理
  const handleClosed = () => {
    // 重置表单
    formRef.value?.resetFields()
    // 清空编辑数据
    entity.value = null
    // 重置附件
    currentAttachments.value = []
  }
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-bottom: 18px;
  }
</style>
