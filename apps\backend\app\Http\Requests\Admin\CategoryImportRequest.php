<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CategoryImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'max:10240', // 最大10MB
                'mimes:jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar',
            ],
        ];
    }

    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'file.required' => '请选择要上传的文件',
            'file.file' => '上传内容必须是文件',
            'file.max' => '文件大小不能超过10MB',
            'file.mimes' => '不支持的文件类型',
        ];
    }
}
