<template>
  <div class="dictionary-page art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      :showExpand="true"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard shadow="never" class="art-table-card" style="margin-top: 0">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton v-if="hasAuth('add')" type="primary" @click="showDialog('add')">
            <ElIcon><Plus /></ElIcon>
            新增分类
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 字典分类表格 -->
      <ArtTable
        ref="tableRef"
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id', height: '600px' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 状态列 -->
        <template #enabled="{ row }">
          <ElTag :type="row.enabled ? 'success' : 'danger'">
            {{ row.enabled ? '启用' : '禁用' }}
          </ElTag>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="setting" @click="openItemDrawer(row)" />
            <ArtButtonTable v-if="hasAuth('edit')" type="edit" @click="showDialog('edit', row)" />
            <ArtButtonTable v-if="hasAuth('delete')" type="delete" @click="deleteCategory(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 编辑对话框 -->
      <CategoryFormDialog
        v-model:visible="dialogVisible"
        v-model:category="selectedCategoryForEdit"
        @success="handleDialogSuccess"
      />

      <!-- 字典项管理抽屉 -->
      <DictionaryItemDrawer
        v-model="itemDrawerVisible"
        :category="selectedCategory"
        @refresh="refreshAll"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Dictionary' })

  // Vue 核心
  import { ref } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import DictionaryItemDrawer from './components/DictionaryItemDrawer.vue'
  import CategoryFormDialog from './components/CategoryFormDialog.vue'

  // 工具
  import { formatDate } from '@/utils/dataprocess/format'

  // API
  import { getDictionaryCategories, deleteDictionaryCategory } from '@/api/admin/dictionary'
  import { useDictionaryStore } from '@/store/modules/dictionary'

  // 类型定义
  import type { SearchFormItem } from '@/types'
  import type { DictionaryCategory } from '@/types/api'

  const dictionaryStore = useDictionaryStore()

  // 权限控制
  const { hasAuth } = useAuth()

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedCategoryForEdit = ref<DictionaryCategory | null>(null)
  const tableRef = ref()

  // 字典项抽屉
  const itemDrawerVisible = ref(false)
  const selectedCategory = ref<DictionaryCategory | null>(null)

  // 搜索表单状态
  const searchFormState = ref({
    name: '',
    code: '',
    enabled: null
  })

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      prop: 'code',
      label: '分类编码',
      type: 'input',
      placeholder: '请输入分类编码'
    },
    {
      prop: 'name',
      label: '分类名称',
      type: 'input',
      placeholder: '请输入分类名称'
    },
    {
      prop: 'enabled',
      label: '是否启用',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ]

  // 使用 useTable Hook
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<DictionaryCategory>({
    core: {
      apiFn: async (params: any) => {
        // 转换搜索参数
        const searchParams = {
          name: params.name || undefined,
          code: params.code || undefined,
          is_enabled:
            params.enabled !== undefined && params.enabled !== '' ? params.enabled : undefined
        }

        // 调用API获取数据
        const data = await getDictionaryCategories(searchParams)

        // 按照sort排序，数值越大越靠前
        const sortedData = [...data].sort((a, b) => (b.sort || 0) - (a.sort || 0))

        // 前端分页（后端暂时不支持分页）
        const total = sortedData.length
        const start = (params.current - 1) * params.size
        const end = start + params.size
        const records = sortedData.slice(start, end)

        return {
          records,
          total,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 20,
        name: '',
        code: '',
        enabled: undefined
      },
      columnsFactory: () => [
        {
          prop: 'code',
          label: '分类编码',
          minWidth: 150
        },
        {
          prop: 'name',
          label: '分类名称',
          minWidth: 150
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'sort',
          label: '排序',
          width: 80,
          align: 'center'
        },
        {
          prop: 'item_count',
          label: '字典项数量',
          width: 120,
          align: 'center'
        },
        {
          prop: 'enabled',
          label: '状态',
          width: 80,
          align: 'center',
          useSlot: true
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 180,
          formatter: (row: DictionaryCategory) => {
            return row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 160,
          fixed: 'right',
          useSlot: true
        }
      ]
    }
  })

  // 搜索相关方法
  const handleReset = () => {
    searchFormState.value = {
      name: '',
      code: '',
      enabled: null
    }
    resetSearch()
  }

  const handleSearch = () => {
    // 直接更新 searchState 的各个属性
    searchState.name = searchFormState.value.name
    searchState.code = searchFormState.value.code
    searchState.enabled =
      searchFormState.value.enabled === null ? undefined : searchFormState.value.enabled
    searchData()
  }

  // 对话框相关方法
  const showDialog = (type: 'add' | 'edit', row?: DictionaryCategory) => {
    if (type === 'edit' && row) {
      selectedCategoryForEdit.value = row
    } else {
      selectedCategoryForEdit.value = null
    }
    dialogVisible.value = true
  }

  // 对话框成功回调
  const handleDialogSuccess = async () => {
    // 根据是否有编辑数据判断刷新策略
    if (selectedCategoryForEdit.value) {
      refreshAfterUpdate()
    } else {
      refreshAfterCreate()
    }
    // 更新store缓存
    await dictionaryStore.updateAfterChange()
  }

  const deleteCategory = (row: DictionaryCategory) => {
    ElMessageBox.confirm('确定要删除该字典分类吗？删除后其下的字典项也会被删除。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await deleteDictionaryCategory(row.id)
      ElMessage.success('删除成功')

      refreshAfterRemove()
      // 更新store缓存
      await dictionaryStore.updateAfterChange()
    })
  }

  // 打开字典项管理抽屉
  const openItemDrawer = (category: DictionaryCategory) => {
    selectedCategory.value = category
    itemDrawerVisible.value = true
  }
</script>

<style lang="scss" scoped></style>
