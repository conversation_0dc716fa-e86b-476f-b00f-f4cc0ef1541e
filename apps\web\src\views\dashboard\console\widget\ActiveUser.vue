<template>
  <div class="card art-custom-card">
    <ArtBarChart
      class="chart"
      barWidth="50%"
      height="13.7rem"
      :showAxisLine="false"
      :data="deviceStatusData"
      :xAxisData="deviceStatusLabels"
      :colors="deviceStatusColors"
    />
    <div class="text">
      <h3 class="box-title">设备状态分布</h3>
      <p class="subtitle"
        >总设备：<span class="text-primary">{{ totalDevices }}台</span></p
      >
      <p class="subtitle">实时监控设备在线状态和运行情况，确保设备正常运行</p>
    </div>
    <div class="list">
      <div v-for="(item, index) in deviceStatusList" :key="index">
        <p :style="{ color: item.color }">{{ item.num }}台</p>
        <p class="subtitle">{{ item.name }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'

  // 设备状态数据
  const deviceStatusData = [892, 256, 32, 78]
  const deviceStatusLabels = ['在线', '离线', '故障', '维护中']
  const deviceStatusColors = ['#52c41a', '#d9d9d9', '#ff4d4f', '#faad14']

  // 设备状态列表
  const deviceStatusList = [
    { name: '在线设备', num: 892, color: '#52c41a' },
    { name: '离线设备', num: 256, color: '#d9d9d9' },
    { name: '故障设备', num: 32, color: '#ff4d4f' },
    { name: '维护中', num: 78, color: '#faad14' }
  ]

  // 计算总设备数量
  const totalDevices = computed(() => {
    return deviceStatusData.reduce((sum, count) => sum + count, 0)
  })
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 420px;
    padding: 16px;

    .chart {
      box-sizing: border-box;
      width: 100%;
      height: 220px;
      padding: 10px;
      border-radius: calc(var(--custom-radius) / 2 + 4px) !important;
    }

    .text {
      margin-left: 3px;

      h3 {
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }

      p {
        margin-top: 5px;
        font-size: 14px;

        &:last-of-type {
          height: 42px;
          margin-top: 5px;
        }
      }
    }

    .list {
      display: flex;
      justify-content: space-between;
      margin-left: 3px;

      > div {
        flex: 1;

        p {
          font-weight: 400;

          &:first-of-type {
            font-size: 24px;
            color: var(--art-gray-900);
          }

          &:last-of-type {
            font-size: 13px;
          }
        }
      }
    }
  }

  .dark {
    .card {
      .chart {
        background: none;
      }
    }
  }

  @media screen and (max-width: $device-phone) {
    .dark {
      .card {
        .chart {
          padding: 15px 0 0 !important;
        }
      }
    }
  }
</style>
