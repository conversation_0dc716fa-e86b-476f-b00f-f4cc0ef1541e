import OSS from 'ali-oss'
import SparkMD5 from 'spark-md5'

/**
 * STS凭证类型
 */
export interface STSCredentials {
  AccessKeyId: string
  AccessKeySecret: string
  SecurityToken: string
  Expiration: string
}

/**
 * OSS配置类型
 */
export interface OSSConfig {
  region: string
  bucket: string
  endpoint: string
  prefix?: string
}

/**
 * 上传进度回调
 */
export type ProgressCallback = (percent: number, checkpoint?: any) => void

/**
 * OSS上传工具类
 * 支持STS临时凭证上传、MD5计算、分片上传等功能
 */
export class OSSUploader {
  private client: OSS | null = null
  private config: OSSConfig | null = null

  /**
   * 计算文件MD5值
   * @param file 文件对象
   * @returns MD5哈希值
   */
  async calculateMD5(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const spark = new SparkMD5.ArrayBuffer()
      const reader = new FileReader()

      // 分块读取文件以避免内存溢出
      const chunkSize = 2 * 1024 * 1024 // 2MB chunks
      const chunks = Math.ceil(file.size / chunkSize)
      let currentChunk = 0

      const loadNext = () => {
        const start = currentChunk * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        reader.readAsArrayBuffer(file.slice(start, end))
      }

      reader.onload = (e) => {
        if (e.target?.result) {
          spark.append(e.target.result as ArrayBuffer)
          currentChunk++

          if (currentChunk < chunks) {
            loadNext()
          } else {
            resolve(spark.end())
          }
        }
      }

      reader.onerror = (error) => {
        reject(new Error('MD5计算失败: ' + error))
      }

      loadNext()
    })
  }

  /**
   * 初始化OSS客户端
   * @param credentials STS临时凭证
   * @param config OSS配置
   */
  initClient(credentials: STSCredentials, config: OSSConfig) {
    this.config = config

    // 处理 region 格式：官方SDK需要 "oss-cn-guangzhou" 格式
    let region = config.region
    if (region.includes('.aliyuncs.com')) {
      // 从 oss-cn-guangzhou.aliyuncs.com 提取 oss-cn-guangzhou
      region = region.replace('.aliyuncs.com', '')
      console.log('Extracted region from endpoint:', config.region, '->', region)
    } else if (!region.startsWith('oss-')) {
      // 如果是 cn-guangzhou 格式，添加 oss- 前缀
      region = 'oss-' + region
      console.log('Added oss- prefix to region:', config.region, '->', region)
    }

    console.log('Creating OSS client with:', {
      region: region,
      bucket: config.bucket,
      hasCredentials: !!credentials.AccessKeyId,
      hasToken: !!credentials.SecurityToken
    })

    this.client = new OSS({
      region: region,
      accessKeyId: credentials.AccessKeyId,
      accessKeySecret: credentials.AccessKeySecret,
      stsToken: credentials.SecurityToken,
      bucket: config.bucket,
      secure: true,
      refreshSTSToken: async () => {
        // 这里可以实现token刷新逻辑
        return {
          accessKeyId: credentials.AccessKeyId,
          accessKeySecret: credentials.AccessKeySecret,
          stsToken: credentials.SecurityToken
        }
      }
    })

    console.log('OSS client initialized successfully')
  }

  /**
   * 生成文件路径
   * @param filename 文件名
   * @returns 完整的OSS对象路径
   */
  generateObjectKey(filename: string): string {
    const prefix = this.config?.prefix || 'attachments/'
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)

    // 获取文件扩展名
    const ext = filename.split('.').pop() || ''

    // 生成唯一文件名
    const uniqueName = `${timestamp}_${random}.${ext}`

    // 返回完整路径
    return `${prefix}${year}/${month}/${day}/${uniqueName}`
  }

  /**
   * 上传文件
   * @param file 文件对象
   * @param onProgress 进度回调
   * @returns OSS对象路径
   */
  async upload(file: File, onProgress?: ProgressCallback): Promise<string> {
    if (!this.client) {
      throw new Error('OSS客户端未初始化，请先调用initClient方法')
    }

    const objectKey = this.generateObjectKey(file.name)
    console.log('Starting upload:', {
      filename: file.name,
      size: file.size,
      objectKey: objectKey
    })

    try {
      // 判断文件大小，选择上传方式
      if (file.size > 100 * 1024 * 1024) {
        // 大于100MB使用分片上传
        console.log('Using multipart upload for large file')
        return await this.multipartUpload(file, objectKey, onProgress)
      } else {
        // 小文件直接上传
        console.log('Using simple put for small file')
        const result = await this.client.put(objectKey, file, {
          progress: (p: number) => {
            const percent = Math.floor(p * 100)
            console.log('Upload progress:', percent + '%')
            onProgress?.(percent)
          }
        })

        console.log('Upload completed:', result.name)
        return result.name
      }
    } catch (error: any) {
      console.error('文件上传失败:', error)
      throw new Error(`上传失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 分片上传（大文件）
   * @param file 文件对象
   * @param objectKey OSS对象路径
   * @param onProgress 进度回调
   * @returns OSS对象路径
   */
  async multipartUpload(
    file: File,
    objectKey: string,
    onProgress?: ProgressCallback
  ): Promise<string> {
    if (!this.client) {
      throw new Error('OSS客户端未初始化')
    }

    try {
      const result = await this.client.multipartUpload(objectKey, file, {
        parallel: 4, // 并行上传的分片数
        partSize: 1024 * 1024, // 每个分片1MB
        progress: (p: number, checkpoint: any) => {
          // 保存checkpoint用于断点续传
          if (checkpoint) {
            localStorage.setItem(`upload-checkpoint-${file.name}`, JSON.stringify(checkpoint))
          }
          onProgress?.(Math.floor(p * 100), checkpoint)
        }
      })

      // 上传完成，清除checkpoint
      localStorage.removeItem(`upload-checkpoint-${file.name}`)

      return result.name
    } catch (error: any) {
      // 如果上传失败，可以从checkpoint恢复
      const checkpoint = localStorage.getItem(`upload-checkpoint-${file.name}`)
      if (checkpoint && error.status === 0) {
        // 网络中断，可以从断点续传
      }
      throw error
    }
  }

  /**
   * 断点续传
   * @param file 文件对象
   * @param checkpoint 断点信息
   * @param onProgress 进度回调
   * @returns OSS对象路径
   */
  async resumeUpload(file: File, checkpoint: any, onProgress?: ProgressCallback): Promise<string> {
    if (!this.client) {
      throw new Error('OSS客户端未初始化')
    }

    try {
      const result = await this.client.multipartUpload(checkpoint.name, file, {
        checkpoint,
        progress: (p: number, cp: any) => {
          if (cp) {
            localStorage.setItem(`upload-checkpoint-${file.name}`, JSON.stringify(cp))
          }
          onProgress?.(Math.floor(p * 100), cp)
        }
      })

      // 上传完成，清除checkpoint
      localStorage.removeItem(`upload-checkpoint-${file.name}`)

      return result.name
    } catch (error) {
      console.error('断点续传失败:', error)
      throw error
    }
  }

  /**
   * 取消上传
   * @param name 文件名或OSS对象名
   */
  async cancelUpload(name: string) {
    if (!this.client) {
      return
    }

    try {
      await this.client.cancel()
      // 清除checkpoint
      localStorage.removeItem(`upload-checkpoint-${name}`)
    } catch (error) {
      console.error('取消上传失败:', error)
    }
  }

  /**
   * 获取文件URL
   * @param objectKey OSS对象路径
   * @param expires 过期时间（秒）
   * @returns 访问URL
   */
  getObjectUrl(objectKey: string, expires?: number): string {
    if (!this.client) {
      throw new Error('OSS客户端未初始化')
    }

    // 如果需要签名URL（私有bucket）
    if (expires) {
      return this.client.signatureUrl(objectKey, { expires })
    }

    // 公共读bucket直接返回URL
    const { bucket, endpoint } = this.config!
    return `https://${bucket}.${endpoint}/${objectKey}`
  }

  /**
   * 销毁客户端
   */
  destroy() {
    this.client = null
    this.config = null
  }
}
