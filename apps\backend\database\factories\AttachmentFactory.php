<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Attachment>
 */
class AttachmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $extensions = ['pdf', 'doc', 'docx', 'jpg', 'png', 'xlsx'];
        $extension = $this->faker->randomElement($extensions);
        
        return [
            'file_name' => $this->faker->word() . '.' . $extension,
            'file_path' => 'uploads/' . $this->faker->uuid() . '.' . $extension,
            'file_size' => $this->faker->numberBetween(1024, 10485760), // 1KB to 10MB
            'mime_type' => $this->getMimeType($extension),
            'storage_type' => 'local',
            'category' => 'document',
            'md5' => $this->faker->md5(),
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
    
    private function getMimeType(string $extension): string
    {
        return match($extension) {
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'jpg' => 'image/jpeg',
            'png' => 'image/png',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            default => 'application/octet-stream',
        };
    }
}
