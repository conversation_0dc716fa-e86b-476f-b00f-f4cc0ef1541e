<?php

namespace App\Services;

use App\Models\Asset;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

/**
 * 资产导出服务
 */
class AssetExportService extends BaseExportService
{
    /**
     * 构造函数
     */
    public function __construct(Request $request = null)
    {
        parent::__construct('asset', $request);
    }

    /**
     * 构建查询
     */
    protected function buildQuery(): Builder
    {
        return Asset::query()
            ->with([
                'category',
                'creator',
                'updater',
                'tags',
            ])
            ->orderBy('created_at', 'desc');
    }

    /**
     * 获取表头
     */
    protected function getHeaders(): array
    {
        return [
            'ID',
            '资产名称',
            '资产编号',
            '资产分类',
            '资产状态',
            '采购价格',
            '当前价值',
            '采购日期',
            '启用日期',
            '预计报废日期',
            '存放位置',
            '管理员',
            '使用人',
            '供应商',
            '品牌',
            '型号',
            '序列号',
            '保修期至',
            '标签',
            '备注',
            '创建人',
            '创建时间',
            '更新人',
            '更新时间',
        ];
    }

    /**
     * 映射数据行
     */
    protected function mapRow($asset): array
    {
        return [
            $asset->id,
            $asset->name,
            $asset->asset_number,
            $this->safeGet($asset->category, 'name'),
            $this->formatStatus($asset->status, [
                'normal' => '正常',
                'maintenance' => '维护中',
                'scrapped' => '已报废',
                'lost' => '丢失',
            ]),
            $asset->purchase_price ? number_format($asset->purchase_price, 2) : '',
            $asset->current_value ? number_format($asset->current_value, 2) : '',
            $this->formatDate($asset->purchase_date),
            $this->formatDate($asset->enable_date),
            $this->formatDate($asset->expected_scrap_date),
            $asset->location,
            $asset->administrator,
            $asset->user,
            $asset->supplier,
            $asset->brand,
            $asset->model,
            $asset->serial_number,
            $this->formatDate($asset->warranty_until),
            $asset->tags->pluck('name')->implode(', '),
            $asset->remark,
            $this->safeGet($asset->creator, 'name'),
            $this->formatDateTime($asset->created_at),
            $this->safeGet($asset->updater, 'name'),
            $this->formatDateTime($asset->updated_at),
        ];
    }

    /**
     * 获取列宽
     */
    protected function getColumnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 20,  // 资产名称
            'C' => 15,  // 资产编号
            'D' => 15,  // 资产分类
            'E' => 12,  // 资产状态
            'F' => 12,  // 采购价格
            'G' => 12,  // 当前价值
            'H' => 12,  // 采购日期
            'I' => 12,  // 启用日期
            'J' => 15,  // 预计报废日期
            'K' => 15,  // 存放位置
            'L' => 12,  // 管理员
            'M' => 12,  // 使用人
            'N' => 15,  // 供应商
            'O' => 12,  // 品牌
            'P' => 15,  // 型号
            'Q' => 15,  // 序列号
            'R' => 12,  // 保修期至
            'S' => 20,  // 标签
            'T' => 25,  // 备注
            'U' => 12,  // 创建人
            'V' => 18,  // 创建时间
            'W' => 12,  // 更新人
            'X' => 18,  // 更新时间
        ];
    }

    /**
     * 应用自定义过滤条件
     */
    protected function applyCustomFilters(Builder $query): Builder
    {
        // 按资产名称搜索
        if ($this->request->has('name')) {
            $query->where('name', 'like', '%' . $this->request->input('name') . '%');
        }

        // 按资产编号搜索
        if ($this->request->has('asset_number')) {
            $query->where('asset_number', 'like', '%' . $this->request->input('asset_number') . '%');
        }

        // 按分类筛选
        if ($this->request->has('category_id')) {
            $query->where('category_id', $this->request->input('category_id'));
        }

        // 按状态筛选
        if ($this->request->has('status')) {
            $query->where('status', $this->request->input('status'));
        }

        // 按管理员筛选
        if ($this->request->has('administrator')) {
            $query->where('administrator', 'like', '%' . $this->request->input('administrator') . '%');
        }

        // 按使用人筛选
        if ($this->request->has('user')) {
            $query->where('user', 'like', '%' . $this->request->input('user') . '%');
        }

        // 按供应商筛选
        if ($this->request->has('supplier')) {
            $query->where('supplier', 'like', '%' . $this->request->input('supplier') . '%');
        }

        // 按品牌筛选
        if ($this->request->has('brand')) {
            $query->where('brand', 'like', '%' . $this->request->input('brand') . '%');
        }

        // 按采购价格范围筛选
        if ($this->request->has('purchase_price_min')) {
            $query->where('purchase_price', '>=', $this->request->input('purchase_price_min'));
        }

        if ($this->request->has('purchase_price_max')) {
            $query->where('purchase_price', '<=', $this->request->input('purchase_price_max'));
        }

        // 按采购日期范围筛选
        if ($this->request->has('purchase_date_start')) {
            $query->where('purchase_date', '>=', $this->request->input('purchase_date_start'));
        }

        if ($this->request->has('purchase_date_end')) {
            $query->where('purchase_date', '<=', $this->request->input('purchase_date_end'));
        }

        // 按标签筛选
        if ($this->request->has('tag_ids')) {
            $tagIds = $this->request->input('tag_ids');
            if (is_array($tagIds) && !empty($tagIds)) {
                $query->whereHas('tags', function ($q) use ($tagIds) {
                    $q->whereIn('tags.id', $tagIds);
                });
            }
        }

        return $query;
    }
}
