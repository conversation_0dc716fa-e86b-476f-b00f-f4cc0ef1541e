<?php

namespace Database\Factories;

use App\Models\ImportTask;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ImportTask>
 */
class ImportTaskFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ImportTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['asset', 'category', 'entity', 'user'];
        $statuses = [
            ImportTask::STATUS_PENDING,
            ImportTask::STATUS_PROCESSING,
            ImportTask::STATUS_COMPLETED,
            ImportTask::STATUS_FAILED,
        ];

        $totalRows = $this->faker->numberBetween(10, 1000);
        $successRows = $this->faker->numberBetween(0, $totalRows);
        $failedRows = $totalRows - $successRows;

        return [
            'type' => $this->faker->randomElement($types),
            'file_path' => 'imports/' . $this->faker->uuid() . '.xlsx',
            'original_filename' => $this->faker->word() . '.xlsx',
            'status' => $this->faker->randomElement($statuses),
            'total_rows' => $totalRows,
            'success_rows' => $successRows,
            'failed_rows' => $failedRows,
            'error_details' => $failedRows > 0 ? $this->generateErrorDetails($failedRows) : null,
            'summary' => $this->faker->sentence(),
            'created_by' => User::factory(),
            'started_at' => $this->faker->optional()->unixTime(),
            'completed_at' => $this->faker->optional()->unixTime(),
            'created_at' => time(),
            'updated_at' => time(),
        ];
    }

    /**
     * Indicate that the import task is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ImportTask::STATUS_PENDING,
            'started_at' => null,
            'completed_at' => null,
            'total_rows' => 0,
            'success_rows' => 0,
            'failed_rows' => 0,
            'error_details' => null,
            'summary' => null,
        ]);
    }

    /**
     * Indicate that the import task is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ImportTask::STATUS_PROCESSING,
            'started_at' => time(),
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the import task is completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $totalRows = $this->faker->numberBetween(10, 1000);
            $successRows = $this->faker->numberBetween(0, $totalRows);
            $failedRows = $totalRows - $successRows;

            return [
                'status' => ImportTask::STATUS_COMPLETED,
                'started_at' => time() - 3600, // 1 hour ago
                'completed_at' => time(),
                'total_rows' => $totalRows,
                'success_rows' => $successRows,
                'failed_rows' => $failedRows,
                'error_details' => $failedRows > 0 ? $this->generateErrorDetails($failedRows) : null,
                'summary' => "导入完成：总计 {$totalRows} 条记录，成功 {$successRows} 条，失败 {$failedRows} 条",
            ];
        });
    }

    /**
     * Indicate that the import task has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ImportTask::STATUS_FAILED,
            'started_at' => time() - 1800, // 30 minutes ago
            'completed_at' => time(),
            'total_rows' => 0,
            'success_rows' => 0,
            'failed_rows' => 0,
            'error_details' => [
                'error' => $this->faker->sentence(),
                'timestamp' => date('Y-m-d H:i:s'),
            ],
            'summary' => null,
        ]);
    }

    /**
     * Indicate that the import task is for assets.
     */
    public function asset(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'asset',
        ]);
    }

    /**
     * Indicate that the import task is for categories.
     */
    public function category(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'category',
        ]);
    }

    /**
     * Indicate that the import task is for entities.
     */
    public function entity(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'entity',
        ]);
    }

    /**
     * Indicate that the import task is for users.
     */
    public function user(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'user',
        ]);
    }

    /**
     * Generate sample error details.
     */
    private function generateErrorDetails(int $count): array
    {
        $errors = [];
        for ($i = 0; $i < min($count, 10); $i++) { // Limit to 10 sample errors
            $errors[] = [
                'row' => $this->faker->numberBetween(2, 1000),
                'error' => $this->faker->sentence(),
                'data' => [
                    'name' => $this->faker->word(),
                    'value' => $this->faker->word(),
                ],
            ];
        }
        return $errors;
    }
}
