// 标签管理相关类型定义

import type { PaginatedResponse, PaginatedSearchParams } from './pagination'

// 标签基础数据结构
export interface Tag {
  id: number
  name: string
  category: string
  created_at: number
}

// 标签列表数据（分页响应）
export type TagListData = PaginatedResponse<Tag>

// 标签表单结构
export interface TagForm {
  id?: number
  name: string
  category: string
}

// 标签搜索参数
export interface TagSearchParams extends PaginatedSearchParams {
  name?: string
  category?: string
  // 支持旧的分页参数格式（兼容useTable）
  current?: number
  size?: number
}
