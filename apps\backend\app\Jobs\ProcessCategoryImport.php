<?php

namespace App\Jobs;

use App\Models\ImportTask;
use App\Services\CategoryImportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessCategoryImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

	protected ImportTask $importTask;

	public function __construct(ImportTask $importTask)
    {
        $this->importTask = $importTask;
    }

    public function handle(CategoryImportService $importService): void
    {
        try {
            Log::info('开始处理分类导入任务', ['task_id' => $this->importTask->id]);
            $this->importTask->markAsProcessing();
            $result = $importService->processImport($this->importTask);
            $this->importTask->markAsCompleted($result);
            Log::info('分类导入任务完成', [
                'task_id' => $this->importTask->id,
                'total_rows' => $result['total_rows'] ?? 0,
                'success_rows' => $result['success_rows'] ?? 0,
                'failed_rows' => $result['failed_rows'] ?? 0,
            ]);
        } catch (\Exception $e) {
            Log::error('分类导入任务失败', [
                'task_id' => $this->importTask->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->importTask->markAsFailed([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'errors' => isset($result) ? ($result['errors'] ?? []) : [],
            ]);
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('分类导入Job失败', [
            'task_id' => $this->importTask->id,
            'error' => $exception->getMessage(),
        ]);
        $this->importTask->markAsFailed([
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
