/**
 * 附件处理 Composable
 * 统一处理前后端附件数据转换和管理
 */

import { ref, type Ref } from 'vue'
import type { AttachmentItem } from '@/types/api'

/**
 * 从附件对象数组中提取ID数组
 * @param attachments 附件对象数组或单个附件对象
 * @returns ID数组
 */
export function extractAttachmentIds(attachments: any): number[] {
  if (!attachments) return []

  // 如果是数组
  if (Array.isArray(attachments)) {
    return attachments
      .filter((item) => item && typeof item === 'object' && item.id)
      .map((item) => Number(item.id))
  }

  // 如果是单个对象
  if (typeof attachments === 'object' && attachments.id) {
    return [Number(attachments.id)]
  }

  // 如果已经是ID或ID数组
  if (typeof attachments === 'number') {
    return [attachments]
  }

  if (Array.isArray(attachments) && attachments.every((item) => typeof item === 'number')) {
    return attachments
  }

  return []
}

/**
 * 智能处理附件字段，统一返回格式
 * @param value 附件字段值（可能是对象、ID、数组等）
 * @returns { ids: number[], details: AttachmentItem[] }
 */
export function processAttachmentField(value: any): {
  ids: number[]
  details: AttachmentItem[]
} {
  if (!value) {
    return { ids: [], details: [] }
  }

  // 如果是数组
  if (Array.isArray(value)) {
    const ids: number[] = []
    const details: AttachmentItem[] = []

    value.forEach((item) => {
      if (typeof item === 'object' && item.id) {
        // 对象数组，提取ID和详情
        ids.push(Number(item.id))
        details.push(item as AttachmentItem)
      } else if (typeof item === 'number') {
        // ID数组
        ids.push(item)
      }
    })

    return { ids, details }
  }

  // 如果是单个对象
  if (typeof value === 'object' && value.id) {
    return {
      ids: [Number(value.id)],
      details: [value as AttachmentItem]
    }
  }

  // 如果是单个ID
  if (typeof value === 'number') {
    return {
      ids: [value],
      details: []
    }
  }

  return { ids: [], details: [] }
}

/**
 * 附件字段管理 Hook
 * @param initialValue 初始值
 * @param isMultiple 是否支持多个附件，默认 true
 * @returns 附件管理方法和响应式数据
 */
export function useAttachmentField(initialValue?: any, isMultiple = true) {
  // 附件ID数组（用于提交给后端）
  const attachmentIds = ref<number[]>([])

  // 附件详情数组（用于AttachmentUpload组件显示）
  const attachmentDetails = ref<AttachmentItem[]>([])

  /**
   * 设置附件数据
   * @param value 附件数据（对象、ID、数组等）
   */
  const setAttachments = (value: any) => {
    const { ids, details } = processAttachmentField(value)

    if (isMultiple) {
      attachmentIds.value = ids
      attachmentDetails.value = details
    } else {
      // 单个附件只取第一个
      attachmentIds.value = ids.slice(0, 1)
      attachmentDetails.value = details.slice(0, 1)
    }
  }

  /**
   * 清空附件
   */
  const clearAttachments = () => {
    attachmentIds.value = []
    attachmentDetails.value = []
  }

  /**
   * 添加附件详情（当上传新附件时）
   * @param attachment 新上传的附件详情
   */
  const addAttachmentDetail = (attachment: AttachmentItem) => {
    if (isMultiple) {
      attachmentDetails.value.push(attachment)
    } else {
      attachmentDetails.value = [attachment]
    }
  }

  /**
   * 移除附件详情
   * @param attachmentId 要移除的附件ID
   */
  const removeAttachmentDetail = (attachmentId: number) => {
    const index = attachmentDetails.value.findIndex((item) => Number(item.id) === attachmentId)
    if (index > -1) {
      attachmentDetails.value.splice(index, 1)
    }
  }

  /**
   * 获取用于提交的数据
   * @returns 单个附件返回ID或null，多个附件返回ID数组
   */
  const getSubmitValue = () => {
    if (isMultiple) {
      return attachmentIds.value
    } else {
      return attachmentIds.value[0] || null
    }
  }

  // 初始化
  if (initialValue !== undefined) {
    setAttachments(initialValue)
  }

  return {
    // 响应式数据
    attachmentIds: attachmentIds as Ref<number[]>,
    attachmentDetails: attachmentDetails as Ref<AttachmentItem[]>,

    // 方法
    setAttachments,
    clearAttachments,
    addAttachmentDetail,
    removeAttachmentDetail,
    getSubmitValue
  }
}

/**
 * 单个附件管理 Hook（useAttachmentField 的简化版本）
 * @param initialValue 初始值
 * @returns 单个附件管理方法和响应式数据
 */
export function useSingleAttachment(initialValue?: any) {
  return useAttachmentField(initialValue, false)
}

/**
 * 多个附件管理 Hook（useAttachmentField 的别名）
 * @param initialValue 初始值
 * @returns 多个附件管理方法和响应式数据
 */
export function useMultipleAttachments(initialValue?: any) {
  return useAttachmentField(initialValue, true)
}
