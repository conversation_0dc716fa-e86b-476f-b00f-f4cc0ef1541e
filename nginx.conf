server {
    listen 80;
    server_name cloud.tecev.com;
    access_log logs/cloud.tecev.com_access.log;
    error_log logs/cloud.tecev.com_error.log;
    root /tcyl/cloud/apps/backend/public;

    gzip on;
    gzip_min_length 10k;
    gzip_buffers 4 16k;
    gzip_comp_level 5;
    gzip_types text/plain
        application/javascript
        application/x-javascript
        text/css
        application/xml
        text/javascript
        application/x-httpd-php
        image/jpeg
        image/gif
        image/png
        application/json;
    gzip_vary off;
    gzip_disable "MSIE [1-8]\.";
    client_max_body_size 100m;

    # 前端应用路径
    location /web {
        alias /tcyl/cloud/apps/backend/public/web;
        try_files $uri $uri/ /web/index.html;
    }

    # Laravel API 路由
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Laravel 的主要路由处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP 处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9060;
        fastcgi_index index.php;
        # 重要：使用容器内的路径
        fastcgi_param SCRIPT_FILENAME /www/public$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问 .ht 文件
    location ~ /\.ht {
        deny all;
    }

    # 静态资源缓存
    location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}

server {
    listen 443 ssl;
    server_name cloud.tecev.com;
    access_log logs/cloud.tecev.com_access.log;
    error_log logs/cloud.tecev.com_error.log;
    root /tcyl/cloud/apps/backend/public;

    ssl_certificate cert/kyx.tecev.com/fullchain.pem;
    ssl_certificate_key cert/kyx.tecev.com/privkey.pem;

    ssl_session_timeout 5m;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    add_header Strict-Transport-Security "max-age=31536000";

    gzip on;
    gzip_min_length 10k;
    gzip_buffers 4 16k;
    gzip_comp_level 5;
    gzip_types text/plain
        application/javascript
        application/x-javascript
        text/css
        application/xml
        text/javascript
        application/x-httpd-php
        image/jpeg
        image/gif
        image/png
        application/json;
    gzip_vary off;
    gzip_disable "MSIE [1-8]\.";

    client_max_body_size 100m;

    # 前端应用路径
    location /web {
        alias /tcyl/cloud/apps/backend/public/web;
        try_files $uri $uri/ /web/index.html;
    }

    # Laravel API 路由
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Laravel 的主要路由处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP 处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9060;
        fastcgi_index index.php;
        # 重要：使用容器内的路径
        fastcgi_param SCRIPT_FILENAME /www/public$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问 .ht 文件
    location ~ /\.ht {
        deny all;
    }

    # 静态资源缓存
    location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}