<template>
  <ElDialog
    v-model="visible"
    title="跟进信息管理"
    width="80%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <div class="follow-up-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <ElButton type="primary" @click="showAddDialog" v-ripple>新增跟进</ElButton>
      </div>

      <!-- 跟进信息表格 -->
      <ElTable
        ref="tableRef"
        :data="tableData"
        row-key="id"
        v-loading="loading"
        style="margin-top: 10px"
      >
        <ElTableColumn prop="id" label="ID" width="80" />
        <ElTableColumn prop="date" label="日期" width="120">
          <template #default="{ row }">
            {{ row.date ? formatDate(row.date, 'YYYY-MM-DD') : '-' }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="person_name" label="人员" width="120" />
        <ElTableColumn prop="content" label="内容" min-width="180" show-overflow-tooltip />
        <ElTableColumn prop="tags" label="标签" width="200">
          <template #default="{ row }">
            <div v-if="row.tags && row.tags.length > 0" class="tags-cell">
              <ElTag v-for="tag in row.tags" :key="tag.id" size="small" type="info">
                {{ tag.name }}
              </ElTag>
            </div>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="created_at" label="添加日期" width="120">
          <template #default="{ row }">
            {{ row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD') : '-' }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="attachments" label="附件数" width="100">
          <template #default="{ row }">
            <ElTag v-if="row.attachments && row.attachments.length">
              {{ row.attachments.length }}
            </ElTag>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <ElButton link type="primary" @click="showEditDialog(row)">编辑</ElButton>
            <ElButton link type="danger" @click="handleDelete(row)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 新增/编辑跟进对话框 -->
      <ElDialog
        v-model="followUpDialogVisible"
        :title="followUpDialogType === 'add' ? '新增跟进' : '编辑跟进'"
        width="60%"
        append-to-body
        destroy-on-close
      >
        <ElForm
          ref="followUpFormRef"
          :model="followUpFormData"
          :rules="followUpRules"
          label-width="100px"
        >
          <ElFormItem label="日期" prop="date">
            <ElDatePicker
              v-model="followUpFormData.date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              :value-format="'X'"
            />
          </ElFormItem>
          <ElFormItem label="人员" prop="person_id">
            <NullableSelect
              v-model="followUpFormData.person_id"
              :placeholder="loadingUsers ? '加载中...' : '请选择人员（限协助人员）'"
              style="width: 100%"
              clearable
              :loading="loadingUsers"
            >
              <ElOption
                v-for="person in assistantsList"
                :key="person.id"
                :label="person.nickname || person.nickName || person.realName"
                :value="person.id"
              />
            </NullableSelect>
          </ElFormItem>
          <ElFormItem label="内容" prop="content">
            <ElInput
              v-model="followUpFormData.content"
              type="textarea"
              :rows="4"
              placeholder="请输入跟进内容"
            />
          </ElFormItem>
          <ElFormItem label="相关文件">
            <AttachmentUpload
              v-model="followUpFormData.attachments"
              :attachments="followUpAttachmentDetails"
              :limit="5"
            />
          </ElFormItem>
          <!-- 标签选择区域 -->
          <ElFormItem label="关联标签">
            <TagSelector
              v-model="followUpFormData.tag_ids"
              :tags="availableTags"
              placeholder="当前生命周期未关联任何标签"
            />
          </ElFormItem>
        </ElForm>
        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="followUpDialogVisible = false">取消</ElButton>
            <ElButton type="primary" @click="handleFollowUpSubmit">确定</ElButton>
          </span>
        </template>
      </ElDialog>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'FollowUpDialog' })

  // Vue 核心
  import { ref, reactive, watch, computed } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // API
  import {
    createFollowUp,
    updateFollowUp,
    deleteFollowUp,
    getLifecycleDetail
  } from '@/api/admin/asset'
  import type { LifecycleFollowUp, Lifecycle, FollowUpFormData } from '@/types/api/lifecycle'
  import type { Tag } from '@/types/api/tag'
  import { getUserList } from '@/api/admin/user'

  // 组件
  import AttachmentUpload from '@/components/custom/upload/Attachment.vue'
  import NullableSelect from '@/components/custom/nullable-select/index.vue'
  import TagSelector from '@/components/custom/tag-selector/index.vue'

  // Props
  const props = defineProps<{
    lifecycleId: number
    lifecycle?: Lifecycle | null // 生命周期完整数据，包含标签信息
  }>()

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 表格相关
  const loading = ref(false)
  const tableRef = ref()
  const tableData = ref<LifecycleFollowUp[]>([])

  // 跟进对话框相关
  const followUpDialogVisible = ref(false)
  const followUpDialogType = ref<'add' | 'edit'>('add')
  const followUpFormRef = ref<FormInstance>()
  const followUpFormData = reactive<LifecycleFollowUp>({
    lifecycle_id: null,
    date: '',
    person_id: null,
    content: '',
    attachments: [],
    // 统一使用 tag_ids 字段
    tag_ids: [] as number[]
  })

  // 跟进表单规则
  const followUpRules: FormRules = {
    date: [{ required: true, message: '请选择日期', trigger: 'change' }],
    person_id: [{ required: true, message: '请选择人员', trigger: 'change' }],
    content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
  }

  // 数据源
  const userList = ref<any[]>([])
  const assistantsList = ref<any[]>([])
  const lifecycleInfo = ref<Lifecycle | null>(null)
  const followUpAttachmentDetails = ref<any[]>([])
  const loadingUsers = ref(false)

  // 标签选择相关
  const availableTags = ref<Tag[]>([]) // 可选标签列表（从生命周期标签中筛选）

  // 加载可选标签 - 只能选择当前生命周期已有的标签
  const loadAvailableTags = () => {
    // 从生命周期的 tags 字段获取标签（后端返回的是对象数组）
    if (props.lifecycle && props.lifecycle.tags && Array.isArray(props.lifecycle.tags)) {
      availableTags.value = [...props.lifecycle.tags]
    } else {
      availableTags.value = []
    }
  }

  // 监听弹窗显示状态
  watch(
    () => visible.value,
    async (newVal) => {
      if (newVal && props.lifecycleId) {
        // 加载生命周期信息和跟进列表
        await loadLifecycleInfo(props.lifecycleId)
        fetchData()
        // 根据生命周期数据设置可选标签
        loadAvailableTags()
      }
    }
  )

  // 监听生命周期数据变化，更新可选标签
  watch(
    () => props.lifecycle,
    () => {
      loadAvailableTags()
    },
    { immediate: true }
  )

  // 加载用户列表
  const loadUserList = async () => {
    loadingUsers.value = true
    try {
      const response = await getUserList({ current: 1, size: 1000 })
      userList.value = response?.data || []
    } finally {
      loadingUsers.value = false
    }
  }

  // 加载生命周期信息
  const loadLifecycleInfo = async (id: number) => {
    const response = await getLifecycleDetail(id)
    lifecycleInfo.value = response
    // 清空协助人员列表，等到需要时再加载
    assistantsList.value = []
  }

  // 获取数据
  const fetchData = async () => {
    if (!props.lifecycleId) return

    loading.value = true
    // 从生命周期详情中获取跟进数据
    if (lifecycleInfo.value && lifecycleInfo.value.follow_ups) {
      tableData.value = lifecycleInfo.value.follow_ups || []
    } else {
      tableData.value = []
    }
    loading.value = false
  }

  // 显示新增对话框
  const showAddDialog = () => {
    followUpDialogType.value = 'add'
    resetFollowUpForm()
    followUpFormData.lifecycle_id = props.lifecycleId
    followUpDialogVisible.value = true

    // 异步加载用户列表并过滤协助人员
    loadUserList().then(() => {
      if (
        lifecycleInfo.value &&
        lifecycleInfo.value.assistants &&
        lifecycleInfo.value.assistants.length > 0
      ) {
        // 从用户列表中获取协助人员的完整信息
        assistantsList.value = userList.value.filter(
          (user) => lifecycleInfo.value?.assistants?.includes(user.id) || false
        )
      }
    })
  }

  // 显示编辑对话框
  const showEditDialog = (row: LifecycleFollowUp) => {
    followUpDialogType.value = 'edit'
    Object.assign(followUpFormData, row)

    // 处理附件数据
    if (row.attachment_details && row.attachment_details.length > 0) {
      // 设置附件详情供组件显示
      followUpAttachmentDetails.value = row.attachment_details
      // followUpFormData.attachments 应该是 ID 数组
      followUpFormData.attachments = row.attachments || []
    } else {
      followUpAttachmentDetails.value = []
      followUpFormData.attachments = []
    }

    // 处理标签数据（统一使用 tag_ids）
    if (row.tag_ids && row.tag_ids.length > 0) {
      followUpFormData.tag_ids = [...row.tag_ids]
    } else if (row.tags && row.tags.length > 0) {
      // 兼容旧的 tags 字段
      followUpFormData.tag_ids = [...row.tags]
    } else {
      followUpFormData.tag_ids = []
    }

    followUpDialogVisible.value = true

    // 异步加载用户列表并过滤协助人员
    loadUserList().then(() => {
      if (
        lifecycleInfo.value &&
        lifecycleInfo.value.assistants &&
        lifecycleInfo.value.assistants.length > 0
      ) {
        // 从用户列表中获取协助人员的完整信息
        assistantsList.value = userList.value.filter(
          (user) => lifecycleInfo.value?.assistants?.includes(user.id) || false
        )
      }
    })
  }

  // 重置跟进表单
  const resetFollowUpForm = () => {
    Object.assign(followUpFormData, {
      lifecycle_id: 0,
      date: '',
      person_id: undefined,
      content: '',
      attachments: [],
      // 统一使用 tag_ids
      tag_ids: [] as number[]
    })
    followUpAttachmentDetails.value = []
  }

  // 提交跟进表单
  const handleFollowUpSubmit = async () => {
    if (!followUpFormRef.value) return
    await followUpFormRef.value.validate(async (valid) => {
      if (valid) {
        // 构建提交数据，使用新的格式
        const submitData: FollowUpFormData = {
          date: followUpFormData.date,
          person_id: followUpFormData.person_id!,
          content: followUpFormData.content,
          attachments: followUpFormData.attachments || [],
          tag_ids: followUpFormData.tag_ids || []
        }

        if (followUpDialogType.value === 'add') {
          await createFollowUp(props.lifecycleId, submitData)
          ElMessage.success('新增成功')
        } else {
          await updateFollowUp(props.lifecycleId, followUpFormData.id!, submitData)
          ElMessage.success('编辑成功')
        }
        followUpDialogVisible.value = false
        // 重新加载生命周期信息以获取最新的跟进列表
        await loadLifecycleInfo(props.lifecycleId)
        fetchData()
      }
    })
  }

  // 删除跟进
  const handleDelete = async (row: LifecycleFollowUp) => {
    try {
      await ElMessageBox.confirm('确定要删除该跟进信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await deleteFollowUp(row.id!, props.lifecycleId)
      ElMessage.success('删除成功')
      // 重新加载生命周期信息以获取最新的跟进列表
      await loadLifecycleInfo(props.lifecycleId)
      fetchData()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    emit('success')
  }
</script>

<style lang="scss" scoped>
  .follow-up-container {
    .toolbar {
      display: flex;
      gap: 10px;
    }
  }

  .upload-demo {
    width: 100%;
  }

  // 标签显示样式
  .tags-cell {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .el-tag {
      margin: 2px 0;
    }
  }
</style>
