<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tag>
 */
class TagFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $maintenanceTags = [
            '待巡查保养',
            '待维修',
            '维修中',
            '已维修',
            '已到场确认',
            '设备巡检',
            '设备维保',
            '设备质控',
            '设备盘点',
            '紧急维修',
            '常规保养',
            '年度质控',
            '正常运行',
            '故障停机',
            '备用待命',
            '预防性维护',
            '校准检测',
            '计量检定',
            '安全检查',
            '性能测试'
        ];

        $categories = ['状态', '任务', '优先级', '操作'];

        return [
            'name' => $this->faker->randomElement($maintenanceTags),
            'category' => $this->faker->randomElement($categories),
        ];
    }
}
