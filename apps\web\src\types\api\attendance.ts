/**
 * 考勤管理相关类型定义
 * 与后端API字段保持完全一致
 */

import type { UserListItem as User } from './user'
import type { AttachmentItem as Attachment } from './attachment'

// ========== 考勤配置相关 ==========

/**
 * 考勤配置实体
 */
export interface CheckinConfig {
  id: number
  attachable_type: string // 关联模块类型 (如: 'App\\Models\\Lifecycle')
  attachable_id: number // 关联业务ID
  checkin_time: number // 打卡时间（Unix时间戳）
  is_photo: number // 是否拍照：0-否 1-是
  location: string // 打卡地点名称
  longitude: number // 经度
  latitude: number // 纬度
  location_range: number // 打卡范围（米）
  created_at: number // 创建时间（Unix时间戳）
  updated_at: number // 更新时间（Unix时间戳）
  users?: User[] // 打卡人员列表（关联数据）
  attachable?: any // 关联业务对象（关联数据）
}

/**
 * 考勤配置表单数据
 */
export interface CheckinConfigForm {
  attachable_type: string // 固定为 'App\\Models\\Lifecycle'
  attachable_id: number // 生命周期项目ID
  checkin_time: number // 打卡时间（Unix时间戳）
  is_photo: number // 是否拍照：0-否 1-是
  location: string // 打卡地点名称
  longitude: number // 经度
  latitude: number // 纬度
  location_range: number // 打卡范围（米）
  user_ids: number[] // 打卡人员ID数组
}

/**
 * 考勤配置搜索参数
 */
export interface CheckinConfigSearchParams {
  attachable_type?: string // 所属模块
  status?: number // 状态：0-禁用 1-启用
  keyword?: string // 搜索关键词
  page?: number // 页码
  per_page?: number // 每页记录数
}

/**
 * 考勤配置切换状态参数
 */
export interface CheckinConfigSwitchParams {
  status: number // 状态：0-禁用 1-启用
}

// ========== 打卡记录相关 ==========

/**
 * 打卡记录实体
 */
export interface CheckinRecord {
  id: number
  checkin_config_id: number // 配置ID
  user_id: number // 用户ID
  checkin_time: number // 打卡时间戳（Unix时间戳）
  status: number // 状态：0-正常 1-异常
  location: string // 打卡地点
  longitude: number // 经度
  latitude: number // 纬度
  location_range: number // 距离范围（米）
  ip_address?: string // IP地址
  attachment_id?: number // 附件ID（打卡照片）
  content?: string // 打卡备注
  user?: User // 用户信息（关联数据）
  config?: CheckinConfig // 配置信息（关联数据）
  attachment?: Attachment // 附件信息（关联数据）
}

/**
 * 打卡记录表单数据
 */
export interface CheckinRecordForm {
  checkin_config_id: number // 打卡配置ID
  location: string // 打卡地点
  latitude: number // 纬度
  longitude: number // 经度
  location_range?: number // 打卡位置范围
  attachment_id?: number // 打卡照片附件ID
  content?: string // 打卡备注
  ip_address?: string // IP地址
}

/**
 * 打卡记录搜索参数
 */
export interface CheckinRecordSearchParams {
  config_id?: number // 打卡配置ID
  user_id?: number // 用户ID
  status?: number // 状态：0-正常 1-异常
  date_start?: string // 开始日期 YYYY-MM-DD
  date_end?: string // 结束日期 YYYY-MM-DD
  page?: number // 页码
  per_page?: number // 每页记录数
}

// ========== 辅助类型 ==========

/**
 * 生命周期选项（用于下拉选择）
 */
export interface LifecycleOption {
  id: number
  name: string
  project_name?: string
  entity_name?: string
}

/**
 * 用户选项（用于下拉选择）
 */
export interface UserOption {
  id: number
  nickname: string
  avatar?: string
}

/**
 * 考勤统计数据（可选，根据实际需求添加）
 */
export interface CheckinStatistics {
  total_configs: number // 总配置数
  active_configs: number // 活跃配置数
  total_users: number // 总参与人数
  today_checkin_count: number // 今日打卡次数
  normal_rate: number // 正常率（百分比）
  exception_count: number // 异常次数
  recent_records?: CheckinRecord[] // 最近打卡记录
}
