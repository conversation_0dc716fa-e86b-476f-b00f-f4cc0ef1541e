/**
 * 资产管理相关类型定义
 */

import type { Category } from './category'
import type { AttachmentItem } from './attachment'

/**
 * 资产信息
 */
export interface Asset {
  id: number
  name: string // 资产名称
  brand_id?: number // 品牌ID
  brand?: {
    id: number
    name: string
    entity_name: string
    display_name: string
  } // 品牌信息
  model?: string // 规格型号
  serial_number?: string // 序列号

  // 分类关联
  asset_category_ids?: number[] // 所有选中的分类ID数组
  categories?: Category[] // 分类信息数组

  // 字典字段
  asset_source?: string // 资产来源: produce/purchase/transfer/donate
  asset_status?: string // 资产状态: new_unstocked/in_use/pending_check/scrap_registered/under_repair
  asset_condition?: string // 成色: brand_new/second_hand/refurbished

  // 父子关系
  parent_id?: number // 主设备ID
  parent?: Asset // 主设备信息
  children?: Asset[] // 附属设备列表
  children_count?: number // 附属设备数量

  // 地址信息
  region_code?: string // 区县代码
  detailed_address?: string // 详细地址

  // 时间和数值字段
  start_date?: string // 启用日期
  warranty_period?: number // 合同质保期(月)
  warranty_alert?: number // 质保期预警(天)
  maintenance_cycle?: number // 维护周期(天)
  expected_years?: number // 预计使用年限(年)

  // JSON字段
  related_entities?: AssetRelatedEntity[] // 相关相关方信息

  // 其他字段
  remark?: string // 备注
  attachments?: AttachmentItem[] // 附件列表
  attachments_count?: number // 附件数量

  // 审计字段
  created_by?: number // 创建人ID
  updated_by?: number // 更新人ID
  created_at: string // 创建时间
  updated_at: string // 更新时间
}

/**
 * 资产查询参数
 */
export interface AssetQueryParams {
  name?: string // 资产名称搜索
  brand_id?: number // 品牌ID搜索
  serial_number?: string // 序列号搜索
  keyword?: string // 通用关键词搜索（名称、品牌、型号、序列号）
  asset_category_ids?: number[] // 分类ID数组，用于筛选
  asset_source?: string // 资产来源
  asset_status?: string // 资产状态
  asset_condition?: string // 成色
  is_accessory?: boolean // 是否附属设备（后端通过parent_id判断）
  parent_id?: number // 主设备ID
  page?: number
  per_page?: number
}

/**
 * 相关相关方信息
 */
export interface AssetRelatedEntity {
  entity_type: string | null // 相关方类型
  entity_id: number | null // 相关方ID
  entity_name?: string // 相关方名称（快照）
  contact_id?: string | null // 联系人ID（仅UI使用，不提交到后端）
  contact_name?: string // 联系人姓名
  contact_phone?: string // 联系电话
  position?: string // 职位
  department?: string // 所在部门
}

/**
 * 资产表单数据
 */
export interface AssetFormData {
  name: string
  brand_id?: number | null
  model?: string
  serial_number?: string

  // 分类关联
  asset_category_ids?: number[] // 所有选中的分类ID数组

  // 字典字段
  asset_source?: string | null
  asset_status?: string | null
  asset_condition?: string | null

  // 父子关系
  parent_id?: number | null

  // 地址信息
  region_code?: string
  detailed_address?: string

  // 时间和数值字段
  start_date?: string
  warranty_period?: number | null
  warranty_alert?: number | null
  maintenance_cycle?: number | null
  expected_years?: number | null

  // JSON字段
  related_entities?: AssetRelatedEntity[]

  // 其他
  attachments?: number[] // 附件ID列表
  remark?: string
}

/**
 * 主设备查询参数
 */
export interface MainAssetQueryParams {
  exclude_id?: number // 排除的资产ID
  keyword?: string // 搜索关键词
  page?: number
  per_page?: number
}

/**
 * 资产导入任务状态
 */
export interface AssetImportTask {
  id: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  original_filename: string
  total_rows: number
  success_rows: number
  failed_rows: number
  error_details: Array<{
    row: number
    error: string
    data: any[]
  }>
  summary?: string
  started_at?: string
  completed_at?: string
  created_at: string
}
