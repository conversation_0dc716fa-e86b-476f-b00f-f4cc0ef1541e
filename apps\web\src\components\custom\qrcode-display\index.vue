<template>
  <div class="qrcode-display">
    <div v-if="loading" class="qrcode-loading">
      <ElIcon :size="20" class="is-loading">
        <Loading />
      </ElIcon>
    </div>
    <ElImage
      v-else
      :src="qrcodeData"
      fit="contain"
      :style="{ width: `${displaySize}px`, height: `${displaySize}px` }"
    >
      <template #error>
        <div class="qrcode-error">
          <ElIcon :size="20">
            <Picture />
          </ElIcon>
        </div>
      </template>
    </ElImage>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Loading, Picture } from '@element-plus/icons-vue'
  import { generateQrCode } from '@/api/admin/qrcode'

  interface Props {
    /** 二维码内容 */
    content: string
    /** 显示大小（像素） */
    displaySize?: number
    /** 二维码生成大小（像素） */
    generateSize?: number
    /** 是否自动加载 */
    autoLoad?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    displaySize: 40,
    generateSize: 300,
    autoLoad: true
  })

  const loading = ref(false)
  const qrcodeData = ref<string>('')

  /** 加载二维码 */
  const loadQrCode = async () => {
    if (loading.value || !props.content) return

    loading.value = true
    try {
      const response = await generateQrCode({
        content: props.content,
        size: props.generateSize,
        margin: 1
      })
      qrcodeData.value = response.qrcode
    } catch (error) {
      console.error('生成二维码失败:', error)
      ElMessage.error('生成二维码失败')
    } finally {
      loading.value = false
    }
  }

  /** 监听内容变化 */
  watch(
    () => props.content,
    (newContent) => {
      if (newContent && props.autoLoad) {
        qrcodeData.value = ''
        loadQrCode()
      }
    }
  )

  onMounted(() => {
    if (props.content && props.autoLoad) {
      loadQrCode()
    }
  })
</script>

<style lang="scss" scoped>
  .qrcode-display {
    display: inline-block;

    .qrcode-loading,
    .qrcode-error {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }

    .qrcode-error {
      color: #909399;
    }

    :deep(.el-image) {
      padding: 2px;
      background-color: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }
  }

  @keyframes loading-rotate {
    to {
      transform: rotate(360deg);
    }
  }

  .is-loading {
    animation: loading-rotate 2s linear infinite;
  }
</style>
