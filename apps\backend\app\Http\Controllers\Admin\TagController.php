<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\TagResource;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @group 标签管理
 *
 * 管理系统标签库
 */
class TagController extends Controller
{
    /**
     * 获取标签列表
     *
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page integer 每页数量. Example: 20
     * @queryParam name string 标签名称搜索. Example: 重要
     * @queryParam category string 标签分类筛选. Example: 重要
     *
     * @apiResourceCollection App\Http\Resources\Admin\TagResource
     *
     * @apiResourceModel App\Models\Tag paginate=20
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 20);
        $filters = $request->only(['name', 'category']);

        $query = Tag::query();

        // 按名称搜索
        if (! empty($filters['name'])) {
            $query->where('name', 'like', '%'.$filters['name'].'%');
        }

        // 按分类筛选
        if (! empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        $tags = $query->latest()->paginate($perPage);

        return TagResource::collection($tags);
    }

    /**
     * 创建标签
     *
     * @bodyParam name string required 标签名称. Example: 重要
     * @bodyParam category string 标签分类. Example: 重要
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:100', 'unique:tags'],
            'category' => ['nullable', 'string', 'max:100'],
        ], [
            'name.required' => '标签名称不能为空',
            'name.max' => '标签名称长度不能超过100个字符',
        ]);

        $tag = Tag::create([
            'name' => $validated['name'],
            'category' => $validated['category'] ?? null,
        ]);

        return (new TagResource($tag))->response()->setStatusCode(201);
    }

    /**
     * 更新标签
     *
     * @urlParam id integer required 标签ID. Example: 1
     *
     * @bodyParam name string 标签名称. Example: 重要
     * @bodyParam category string 标签分类. Example: 重要
     */
    public function update(Request $request, int $id)
    {
        $tag = Tag::find($id);

        if (! $tag) {
            return $this->error('标签不存在', 404);
        }

        $validated = $request->validate([
            'name' => ['sometimes', 'string', 'max:100', Rule::unique('tags')->ignore($id)],
            'category' => ['nullable', 'string', 'max:100'],
        ], [
            'name.max' => '标签名称长度不能超过100个字符',
            'name.unique' => '标签名称已存在',
        ]);

        $tag->update([
            'name' => $validated['name'] ?? $tag->name,
            'category' => $validated['category'] ?? $tag->category,
        ]);

        return new TagResource($tag);
    }

    /**
     * 删除标签
     *
     * @urlParam id integer required 标签ID. Example: 1
     */
    public function destroy(int $id)
    {
        $tag = Tag::find($id);

        if (! $tag) {
            return $this->error('标签不存在', 404);
        }

        // 检查是否有关联的生命周期记录
        $lifecycleCount = $tag->lifecycles()->count();
        if ($lifecycleCount > 0) {
            return $this->error("标签正在被 {$lifecycleCount} 个生命周期记录使用，无法删除", 422);
        }

        $tag->delete();

        return response()->noContent();
    }

    /**
     * 获取所有标签（用于选择器）
     */
    public function all()
    {
        $tags = Tag::select(['id', 'name', 'category'])
            ->orderBy('name')
            ->get();

        return response()->json($tags);
    }
}
