<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 统一导入任务模型
 */
class ImportTask extends BaseModel
{
	use HasFactory;

	protected $table = 'import_tasks';

	protected $fillable = [
		'type',
		'file_path',
		'original_filename',
		'status',
		'total_rows',
		'success_rows',
		'failed_rows',
		'error_details',
		'summary',
		'created_by',
		'started_at',
		'completed_at',
	];

	protected $casts = [
		'total_rows' => 'integer',
		'success_rows' => 'integer',
		'failed_rows' => 'integer',
		'error_details' => 'array',
		'created_by' => 'integer',
		'started_at' => 'integer',
		'completed_at' => 'integer',
		'created_at' => 'integer',
		'updated_at' => 'integer',
	];

	const STATUS_PENDING = 'pending';
	const STATUS_PROCESSING = 'processing';
	const STATUS_COMPLETED = 'completed';
	const STATUS_FAILED = 'failed';

	public function creator(): BelongsTo
	{
		return $this->belongsTo(User::class, 'created_by');
	}

	public function markAsProcessing(): void
	{
		$this->update([
			'status' => self::STATUS_PROCESSING,
			'started_at' => time(),
		]);
	}

	public function markAsCompleted(array $result): void
	{
		$this->update([
			'status' => self::STATUS_COMPLETED,
			'total_rows' => $result['total_rows'] ?? 0,
			'success_rows' => $result['success_rows'] ?? 0,
			'failed_rows' => $result['failed_rows'] ?? 0,
			'error_details' => $result['errors'] ?? null,
			'summary' => $result['summary'] ?? null,
			'completed_at' => time(),
		]);
	}

	public function markAsFailed(string|array $error): void
	{
		$this->update([
			'status' => self::STATUS_FAILED,
			'error_details' => is_string($error) ? [['error' => $error]] : $error,
			'completed_at' => time(),
		]);
	}

	public function updateProgress(int $totalRows, int $successRows, int $failedRows): void
	{
		$this->update([
			'total_rows' => $totalRows,
			'success_rows' => $successRows,
			'failed_rows' => $failedRows,
		]);
	}
}
