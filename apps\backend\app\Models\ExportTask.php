<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 导出任务模型
 * 
 * @property int $id
 * @property string $type 导出类型 (asset, category, entity, user)
 * @property string $filename 文件名
 * @property string $status 状态 (pending, processing, completed, failed)
 * @property int $total_rows 总行数
 * @property array|null $filters 过滤条件
 * @property array|null $error_details 错误详情
 * @property int|null $file_size 文件大小(字节)
 * @property string|null $file_path 文件路径
 * @property int|null $started_at 开始时间戳
 * @property int|null $completed_at 完成时间戳
 * @property int $created_by 创建人ID
 * @property int $created_at 创建时间戳
 * @property int $updated_at 更新时间戳
 * @property-read User $creator 创建人
 */
class ExportTask extends Model
{
    use HasFactory;

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'type',
        'filename',
        'status',
        'total_rows',
        'filters',
        'error_details',
        'file_size',
        'file_path',
        'started_at',
        'completed_at',
        'created_by',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'filters' => 'array',
        'error_details' => 'array',
        'total_rows' => 'integer',
        'file_size' => 'integer',
        'started_at' => 'integer',
        'completed_at' => 'integer',
        'created_by' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 时间戳字段使用整数
     */
    public $timestamps = true;

    /**
     * 创建时间字段名
     */
    const CREATED_AT = 'created_at';

    /**
     * 更新时间字段名
     */
    const UPDATED_AT = 'updated_at';

    /**
     * 获取时间戳的新鲜值
     */
    public function freshTimestamp()
    {
        return time();
    }

    /**
     * 从时间戳获取日期时间
     */
    public function fromDateTime($value)
    {
        return is_numeric($value) ? $value : parent::fromDateTime($value);
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 标记任务为处理中
     */
    public function markAsProcessing(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'started_at' => time(),
        ]);
    }

    /**
     * 标记任务为完成
     */
    public function markAsCompleted(array $result): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'total_rows' => $result['total_rows'] ?? 0,
            'file_size' => $result['file_size'] ?? null,
            'file_path' => $result['file_path'] ?? null,
            'completed_at' => time(),
        ]);
    }

    /**
     * 标记任务为失败
     */
    public function markAsFailed($error): void
    {
        $errorDetails = is_array($error) ? $error : [['error' => (string) $error]];

        $this->update([
            'status' => self::STATUS_FAILED,
            'error_details' => $errorDetails,
            'completed_at' => time(),
        ]);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => '等待处理',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            default => '未知状态',
        };
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute(): string
    {
        return match ($this->type) {
            'asset' => '资产',
            'category' => '分类',
            'entity' => '相关方',
            'user' => '用户',
            default => '未知类型',
        };
    }

    /**
     * 获取文件大小文本
     */
    public function getFileSizeTextAttribute(): string
    {
        if (!$this->file_size) {
            return '';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }

    /**
     * 获取处理时长（秒）
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->started_at) {
            return null;
        }

        $endTime = $this->completed_at ?: time();
        return $endTime - $this->started_at;
    }

    /**
     * 获取处理时长文本
     */
    public function getDurationTextAttribute(): string
    {
        $duration = $this->duration;
        
        if (!$duration) {
            return '';
        }

        if ($duration < 60) {
            return $duration . '秒';
        }

        if ($duration < 3600) {
            return round($duration / 60, 1) . '分钟';
        }

        return round($duration / 3600, 1) . '小时';
    }

    /**
     * 是否可以下载
     */
    public function canDownload(): bool
    {
        return $this->status === self::STATUS_COMPLETED 
            && $this->file_path 
            && file_exists(storage_path('app/' . $this->file_path));
    }
}
