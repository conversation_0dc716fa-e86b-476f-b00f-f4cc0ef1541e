<template>
  <div class="region-selector">
    <ElCascader
      v-model="selectedRegion"
      :options="regionOptions"
      :props="cascaderProps"
      :placeholder="placeholder"
      :filterable="filterable"
      :clearable="clearable"
      :disabled="disabled"
      :size="size"
      :show-all-levels="showAllLevels"
      :collapse-tags="collapseTags"
      :separator="separator"
      :filter-method="filterMethod"
      :loading="loading"
      style="width: 100%"
      @change="handleChange"
      @expand-change="handleExpandChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
    >
      <!-- 自定义选项内容 -->
      <template #default="{ data }">
        <span>{{ data.label }}</span>
        <span v-if="showPinyin && data.pinyin_prefix" class="region-pinyin">
          ({{ data.pinyin_prefix }})
        </span>
      </template>

      <!-- 自定义空状态 -->
      <template #empty>
        <div class="region-empty">
          <span>暂无地区数据</span>
        </div>
      </template>
    </ElCascader>

    <!-- 显示选中的地区信息 -->
    <div v-if="showSelectedInfo && selectedRegionInfo" class="region-info">
      <p>选中地区：{{ selectedRegionInfo.fullName }}</p>
      <p>地区代码：{{ selectedRegionInfo.code }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue'
  import { ElCascader, ElMessage } from 'element-plus'
  import { useRegionStore } from '@/store/modules/region'
  import type { RegionTreeItem, RegionValue } from '@/types/api'

  defineOptions({ name: 'RegionSelector' })

  // 组件属性
  interface Props {
    modelValue?: RegionValue
    placeholder?: string
    filterable?: boolean
    clearable?: boolean
    disabled?: boolean
    size?: 'large' | 'default' | 'small'
    showAllLevels?: boolean
    collapseTags?: boolean
    separator?: string
    showPinyin?: boolean
    showSelectedInfo?: boolean
    emitPath?: boolean // 是否返回完整路径，默认只返回最后一级
    lazy?: boolean // 是否懒加载
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: undefined,
    placeholder: '请选择地区',
    filterable: true,
    clearable: true,
    disabled: false,
    size: 'default',
    showAllLevels: true,
    collapseTags: false,
    separator: ' / ',
    showPinyin: false,
    showSelectedInfo: false,
    emitPath: false,
    lazy: false
  })

  const emit = defineEmits<{
    'update:modelValue': [value: RegionValue]
    change: [value: RegionValue, selectedData?: any]
    'expand-change': [value: any]
    blur: [event: FocusEvent]
    focus: [event: FocusEvent]
    'visible-change': [value: boolean]
    'remove-tag': [value: any]
  }>()

  // 响应式数据
  const regionStore = useRegionStore()
  const regionOptions = ref<RegionTreeItem[]>([])
  const selectedRegion = ref<RegionValue>()
  const loading = ref(false)

  // 级联选择器配置
  const cascaderProps = computed(() => {
    const baseProps = {
      value: 'value',
      label: 'label',
      children: 'children',
      emitPath: true,
      checkStrictly: false
    }

    if (props.lazy) {
      return {
        ...baseProps,
        lazy: true,
        lazyLoad: async (node: any, resolve: any) => {
          const { level, value } = node

          // 根节点，加载省份
          if (level === 0) {
            try {
              const provinces = await regionStore.fetchProvinces()
              resolve(provinces)
            } catch {
              ElMessage.error('加载省份数据失败')
              resolve([])
            }
            return
          }

          // 加载子节点
          try {
            const children = await regionStore.fetchChildren(value)
            const childrenWithLeaf = children.map((item) => ({
              ...item,
              leaf: item.deep >= 2 // 区县级为叶子节点
            }))
            resolve(childrenWithLeaf)
          } catch {
            ElMessage.error('加载地区数据失败')
            resolve([])
          }
        }
      }
    }

    return baseProps
  })

  // 计算属性
  const selectedRegionInfo = computed(() => {
    if (!selectedRegion.value) return null

    const code = props.emitPath
      ? Array.isArray(selectedRegion.value)
        ? selectedRegion.value[selectedRegion.value.length - 1]
        : selectedRegion.value
      : (selectedRegion.value as string)

    // 这里可以通过store获取完整地区名称
    // 简化实现，直接使用代码
    return {
      code,
      fullName: code // 实际项目中可以调用store方法获取完整名称
    }
  })

  // 方法
  const loadRegionData = async () => {
    if (props.lazy) return // 懒加载模式不需要预加载

    loading.value = true
    try {
      const data = await regionStore.fetchRegionTree()
      regionOptions.value = data

      // 如果有默认值，设置选中项
      if (props.modelValue) {
        selectedRegion.value = props.modelValue
      }
    } catch (error) {
      console.error('加载地区数据失败：', error)
      ElMessage.error('加载地区数据失败')
    } finally {
      loading.value = false
    }
  }

  // 过滤方法（支持拼音搜索）
  const filterMethod = (node: any, keyword: string) => {
    const data = node.data
    const lowerKeyword = keyword.toLowerCase()

    return (
      data.label.includes(keyword) ||
      data.pinyin?.includes(lowerKeyword) ||
      data.pinyin_prefix?.toLowerCase().startsWith(lowerKeyword)
    )
  }

  // 事件处理
  const handleChange = (value: any) => {
    const emitValue = props.emitPath
      ? value
      : Array.isArray(value)
        ? value[value.length - 1]
        : value

    emit('update:modelValue', emitValue)
    emit('change', emitValue, selectedRegionInfo.value)
  }

  const handleExpandChange = (value: any) => {
    emit('expand-change', value)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleVisibleChange = (value: boolean) => {
    emit('visible-change', value)
  }

  const handleRemoveTag = (value: any) => {
    emit('remove-tag', value)
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    async (newValue) => {
      if (newValue && !props.lazy) {
        // 如果有值且不是懒加载模式，需要确保数据已加载
        if (regionOptions.value.length === 0) {
          await loadRegionData()
        }

        // 获取完整路径
        try {
          const pathResponse = await regionStore.fetchRegionPath(newValue as string)
          // pathResponse 包含 codes 数组，即地区代码路径
          if (pathResponse && pathResponse.codes && Array.isArray(pathResponse.codes)) {
            selectedRegion.value = pathResponse.codes
          } else {
            selectedRegion.value = newValue
          }
        } catch (error) {
          console.error('获取地区路径失败:', error)
          selectedRegion.value = newValue
        }
      } else {
        selectedRegion.value = newValue
      }
    },
    { immediate: true }
  )

  // 生命周期
  onMounted(() => {
    loadRegionData()
  })

  // 对外暴露方法
  defineExpose({
    loadData: loadRegionData,
    clearSelection: () => {
      selectedRegion.value = undefined
      emit('update:modelValue', '' as any)
    }
  })
</script>

<style lang="scss" scoped>
  .region-selector {
    .region-pinyin {
      margin-left: 8px;
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }

    .region-empty {
      padding: 10px;
      color: var(--el-text-color-placeholder);
      text-align: center;
    }

    .region-info {
      padding: 10px;
      margin-top: 10px;
      font-size: 14px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;

      p {
        margin: 5px 0;
        color: var(--el-text-color-regular);
      }
    }
  }
</style>
