<template>
  <ElDialog
    v-model="visible"
    title="新增联系人"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px" @submit.prevent>
      <ElFormItem label="姓名" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入联系人姓名" maxlength="50" clearable />
      </ElFormItem>

      <ElFormItem label="电话" prop="phone">
        <ElInput v-model="formData.phone" placeholder="请输入联系电话" maxlength="20" clearable />
      </ElFormItem>

      <ElFormItem label="职位" prop="position">
        <ElInput v-model="formData.position" placeholder="请输入职位" maxlength="50" clearable />
      </ElFormItem>

      <ElFormItem label="部门" prop="department">
        <ElInput v-model="formData.department" placeholder="请输入部门" maxlength="50" clearable />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit" :loading="loading"> 保存并填充 </ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'ContactQuickAddDialog' })

  import { ref, reactive, computed } from 'vue'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
  import { createContact } from '@/api/admin/entity'
  import type { ContactForm, Contact } from '@/types/api/entity'

  interface Props {
    modelValue: boolean
    entityId: number | null
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: [contact: Contact]
  }>()

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 表单相关
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = reactive<Omit<ContactForm, 'entity_id'>>({
    name: '',
    phone: '',
    position: '',
    department: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' },
      { max: 50, message: '姓名不能超过50个字符', trigger: 'blur' }
    ],
    phone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { max: 20, message: '电话不能超过20个字符', trigger: 'blur' },
      {
        pattern: /^[\d\-\+\(\)\s]+$/,
        message: '请输入有效的电话号码',
        trigger: 'blur'
      }
    ],
    position: [{ max: 50, message: '职位不能超过50个字符', trigger: 'blur' }],
    department: [{ max: 50, message: '部门不能超过50个字符', trigger: 'blur' }]
  })

  // 提交表单
  const handleSubmit = async () => {
    if (!props.entityId) {
      ElMessage.error('未选择相关方，无法创建联系人')
      return
    }

    try {
      await formRef.value?.validate()
      loading.value = true

      const contactData: ContactForm = {
        ...formData,
        entity_id: String(props.entityId)
      }

      const contact = await createContact(String(props.entityId), contactData)
      ElMessage.success('联系人创建成功')

      emit('success', contact)
      visible.value = false
    } catch (error) {
      console.error('创建联系人失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 取消
  const handleCancel = () => {
    visible.value = false
  }

  // 对话框关闭后重置表单
  const handleClosed = () => {
    formRef.value?.resetFields()
    Object.assign(formData, {
      name: '',
      phone: '',
      position: '',
      department: ''
    })
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding-top: 20px;
    padding-bottom: 10px;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
</style>
