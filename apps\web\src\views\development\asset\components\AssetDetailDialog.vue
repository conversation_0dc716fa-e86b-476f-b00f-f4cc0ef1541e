<template>
  <ElDialog
    :model-value="visible"
    :title="`资产详情 - ${assetDetail?.name || '加载中...'}`"
    :fullscreen="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="true"
    @update:model-value="handleVisibleChange"
    @close="handleClose"
  >
    <div class="asset-detail-dialog" v-loading="loading">
      <!-- 选项卡内容 -->
      <ElCard shadow="never" class="detail-card">
        <ElTabs v-model="activeTab" type="border-card">
          <!-- 详情与统计 -->
          <ElTabPane label="详情与统计" name="details">
            <div class="tab-content">
              <!-- 基本信息 -->
              <div class="section-block">
                <h3 class="section-title">基本信息</h3>
                <ElDescriptions :column="3" border>
                  <ElDescriptionsItem label="资产名称">
                    {{ assetDetail?.name || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="品牌">
                    {{ assetDetail?.brand?.name || assetDetail?.brand?.display_name || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="规格型号">
                    {{ assetDetail?.model || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="序列号">
                    {{ assetDetail?.serial_number || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="资产来源">
                    <ElTag v-if="assetDetail?.asset_source" type="primary">
                      {{ getAssetSourceLabel(assetDetail.asset_source) }}
                    </ElTag>
                    <span v-else>-</span>
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="资产状态">
                    <ElTag v-if="assetDetail?.asset_status" type="success">
                      {{ getAssetStatusLabel(assetDetail.asset_status) }}
                    </ElTag>
                    <span v-else>-</span>
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="成色">
                    <ElTag v-if="assetDetail?.asset_condition" type="info">
                      {{ getAssetConditionLabel(assetDetail.asset_condition) }}
                    </ElTag>
                    <span v-else>-</span>
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="主设备">
                    {{ assetDetail?.parent?.name || '无（主设备）' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="地址信息" :span="2">
                    {{ getFullAddress() }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="启用日期">
                    {{ formatDate(assetDetail?.start_date, 'YYYY-MM-DD') || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="质保期">
                    {{ assetDetail?.warranty_period ? `${assetDetail.warranty_period}个月` : '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="维护周期">
                    {{
                      assetDetail?.maintenance_cycle ? `${assetDetail.maintenance_cycle}天` : '-'
                    }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="预计使用年限">
                    {{ assetDetail?.expected_years ? `${assetDetail.expected_years}年` : '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="创建时间">
                    {{ formatDate(assetDetail?.created_at) || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="更新时间">
                    {{ formatDate(assetDetail?.updated_at) || '-' }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="备注" :span="3">
                    {{ assetDetail?.remark || '-' }}
                  </ElDescriptionsItem>
                </ElDescriptions>
              </div>

              <!-- 统计仪表板 -->
              <div class="section-block">
                <h3 class="section-title">统计概览</h3>

                <!-- 第一行：统计卡片 -->
                <ElRow :gutter="20" class="stats-row">
                  <ElCol :span="6" v-for="stat in statsData" :key="stat.title">
                    <ArtStatsCard
                      :icon="stat.icon"
                      :title="stat.title"
                      :count="stat.count"
                      :description="stat.title"
                      :icon-color="stat.iconColor"
                      :icon-bg-color="stat.iconBgColor"
                    />
                  </ElCol>
                </ElRow>

                <!-- 第二行：图表展示 -->
                <ElRow :gutter="20" class="charts-row">
                  <ElCol :span="12">
                    <ElCard class="chart-card">
                      <template #header>
                        <span class="card-header">事项比例</span>
                      </template>
                      <ArtRingChart
                        :data="pieData"
                        :height="'300px'"
                        :show-legend="true"
                        legend-position="right"
                      />
                    </ElCard>
                  </ElCol>
                  <ElCol :span="12">
                    <ElCard class="chart-card">
                      <template #header>
                        <span class="card-header">完成率指标</span>
                      </template>
                      <div class="progress-cards">
                        <ArtProgressCard
                          v-for="progress in progressData"
                          :key="progress.title"
                          :title="progress.title"
                          :percentage="progress.percentage"
                          :color="progress.color"
                          class="progress-item"
                        />
                      </div>
                    </ElCard>
                  </ElCol>
                </ElRow>

                <!-- 第三行：工作量和趋势 -->
                <ElRow :gutter="20" class="charts-row">
                  <ElCol :span="12">
                    <ElCard class="chart-card">
                      <template #header>
                        <span class="card-header">人员工作量</span>
                      </template>
                      <ArtBarChart
                        :x-axis-data="workloadData.xAxisData"
                        :series-data="[{ name: '处理次数', data: workloadData.data }]"
                        :height="'300px'"
                      />
                    </ElCard>
                  </ElCol>
                  <ElCol :span="12">
                    <ElCard class="chart-card">
                      <template #header>
                        <div class="card-header-with-filter">
                          <span class="card-header">工作趋势</span>
                          <ElSelect v-model="trendFilter" style="width: 120px" size="small">
                            <ElOption label="近6个月" value="6months" />
                            <ElOption label="近3个月" value="3months" />
                            <ElOption label="近1个月" value="1month" />
                          </ElSelect>
                        </div>
                      </template>
                      <ArtLineChart
                        :x-axis-data="trendData.xAxisData"
                        :series-data="[{ name: '事项数量', data: trendData.data }]"
                        :height="'300px'"
                      />
                    </ElCard>
                  </ElCol>
                </ElRow>
              </div>

              <!-- 从属资产 -->
              <div class="section-block">
                <h3 class="section-title">从属资产管理</h3>

                <!-- 如果当前是配件，显示主设备信息 -->
                <div v-if="assetDetail?.parent_id" class="parent-device-info">
                  <ElAlert
                    :title="`主设备：${assetDetail.parent?.name}`"
                    type="info"
                    show-icon
                    :closable="false"
                    style="margin-bottom: 16px"
                  >
                    <template #default>
                      <span
                        >品牌：{{
                          assetDetail.parent?.brand?.name ||
                          assetDetail.parent?.brand?.display_name ||
                          '-'
                        }}</span
                      >
                      <span style="margin-left: 20px"
                        >型号：{{ assetDetail.parent?.model || '-' }}</span
                      >
                      <span style="margin-left: 20px"
                        >序列号：{{ assetDetail.parent?.serial_number || '-' }}</span
                      >
                    </template>
                  </ElAlert>
                </div>

                <!-- 资产列表 -->
                <ArtTable
                  :data="relatedAssets"
                  :loading="false"
                  :columns="assetColumns"
                  :pagination="false"
                  :table-config="{ height: 'auto' }"
                />
              </div>
            </div>
          </ElTabPane>

          <!-- 生命周期管理 -->
          <ElTabPane label="生命周期管理" name="lifecycle">
            <div class="tab-content">
              <!-- 统计栏 -->
              <div class="stats-alert">
                <ElAlert
                  title="生命周期统计"
                  type="info"
                  :closable="false"
                  style="margin-bottom: 20px"
                >
                  <template #default>
                    <span
                      >总计：<strong>{{ lifecycleData.length }}</strong> 条</span
                    >
                    <span style="margin-left: 20px"
                      >已完成：<strong>{{ completedLifecycleCount }}</strong> 条</span
                    >
                    <span style="margin-left: 20px"
                      >进行中：<strong>{{ inProgressLifecycleCount }}</strong> 条</span
                    >
                  </template>
                </ElAlert>
              </div>

              <!-- 生命周期列表 -->
              <ArtTable
                :data="lifecycleData"
                :loading="false"
                :columns="lifecycleColumns"
                :pagination="false"
                :table-config="{ height: 'auto' }"
              />
            </div>
          </ElTabPane>

          <!-- 跟进记录 -->
          <ElTabPane label="跟进记录" name="followup">
            <div class="tab-content">
              <!-- 统计栏 -->
              <div class="stats-alert">
                <ElAlert
                  title="跟进统计"
                  type="success"
                  :closable="false"
                  style="margin-bottom: 20px"
                >
                  <template #default>
                    <span
                      >总跟进：<strong>{{ followUpData.length }}</strong> 次</span
                    >
                    <span style="margin-left: 20px"
                      >本月：<strong>{{ currentMonthFollowUpCount }}</strong> 次</span
                    >
                  </template>
                </ElAlert>
              </div>

              <!-- 跟进记录列表 -->
              <ArtTable
                :data="followUpData"
                :loading="false"
                :columns="followUpColumns"
                :pagination="false"
                :table-config="{ height: 'auto' }"
              />
            </div>
          </ElTabPane>
        </ElTabs>
      </ElCard>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AssetDetailDialog' })

  import { ref, reactive, computed, watch, h } from 'vue'
  import { ElTag, ElBadge } from 'element-plus'

  // 内部组件
  import ArtStatsCard from '@/components/core/cards/art-stats-card/index.vue'
  import ArtRingChart from '@/components/core/charts/art-ring-chart/index.vue'
  import ArtProgressCard from '@/components/core/cards/art-progress-card/index.vue'
  import ArtBarChart from '@/components/core/charts/art-bar-chart/index.vue'
  import ArtLineChart from '@/components/core/charts/art-line-chart/index.vue'
  import ArtTable from '@/components/core/tables/art-table/index.vue'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'

  import { formatDate } from '@/utils'
  import { useDictionaryStore } from '@/store/modules/dictionary'

  import type { Asset } from '@/types/api/asset'
  import type { ColumnOption } from '@/types/component'
  import type { DictionaryItem } from '@/types/api/dictionary'

  // Props 定义
  interface Props {
    visible: boolean
    assetId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    assetId: undefined
  })

  // Events 定义
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    close: []
  }>()

  const dictionaryStore = useDictionaryStore()

  // 页面状态
  const loading = ref(true)
  const activeTab = ref('details')
  const trendFilter = ref('6months')

  // 字典选项
  const assetSourceOptions = ref<DictionaryItem[]>([])
  const assetStatusOptions = ref<DictionaryItem[]>([])
  const assetConditionOptions = ref<DictionaryItem[]>([])

  // 资产详情数据（Mock数据）
  const assetDetail = ref<Asset>({
    id: 1,
    name: '戴尔PowerEdge R740服务器',
    brand: 'Dell',
    model: 'PowerEdge R740',
    serial_number: 'SN20231234567',
    asset_source: 'purchase',
    asset_status: 'in_use',
    asset_condition: 'second_hand',
    parent_id: null,
    parent: null,
    region_code: '110101',
    detailed_address: '北京市东城区建国门内大街XX号XX楼XX室',
    start_date: '2023-01-15',
    warranty_period: 36,
    warranty_alert: 30,
    maintenance_cycle: 30,
    expected_years: 5,
    remark: '核心业务服务器，重要性高',
    created_at: '2023-01-10 10:30:00',
    updated_at: '2023-06-15 14:20:00',
    children: [
      {
        id: 2,
        name: '内存条32GB',
        brand: 'Kingston',
        model: 'DDR4-3200',
        serial_number: 'MEM001',
        asset_status: 'in_use',
        created_at: '2023-01-10 10:30:00',
        updated_at: '2023-06-15 14:20:00'
      },
      {
        id: 3,
        name: 'SSD硬盘1TB',
        brand: 'Samsung',
        model: '980 PRO',
        serial_number: 'SSD001',
        asset_status: 'in_use',
        created_at: '2023-01-10 10:30:00',
        updated_at: '2023-06-15 14:20:00'
      }
    ]
  })

  // 统计数据
  const statsData = reactive([
    {
      title: '维护次数',
      count: 12,
      icon: '&#xe702;',
      iconColor: '#409EFF',
      iconBgColor: '#ECF5FF'
    },
    { title: '故障次数', count: 3, icon: '&#xe703;', iconColor: '#F56C6C', iconBgColor: '#FEF0F0' },
    { title: '巡检次数', count: 8, icon: '&#xe704;', iconColor: '#67C23A', iconBgColor: '#F0F9FF' },
    {
      title: '使用天数',
      count: 320,
      icon: '&#xe705;',
      iconColor: '#E6A23C',
      iconBgColor: '#FCEEF2'
    }
  ])

  // 饼图数据
  const pieData = reactive([
    { name: '维护', value: 40 },
    { name: '故障', value: 30 },
    { name: '巡检', value: 30 }
  ])

  // 进度数据
  const progressData = reactive([
    { title: '维护完成率', percentage: 85, color: '#409EFF' },
    { title: '故障解决率', percentage: 95, color: '#67C23A' },
    { title: '巡检达标率', percentage: 78, color: '#E6A23C' }
  ])

  // 工作量数据
  const workloadData = reactive({
    xAxisData: ['张三', '李四', '王五', '赵六'],
    data: [5, 3, 7, 4]
  })

  // 趋势数据
  const trendData = reactive({
    xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
    data: [5, 8, 12, 6, 9, 11]
  })

  // 生命周期数据
  const lifecycleData = reactive([
    {
      id: 1,
      type: '安装调试',
      date: '2023-01-15',
      initiator: '张三',
      content: '服务器初始化安装',
      status: '已完成'
    },
    {
      id: 2,
      type: '日常维护',
      date: '2023-02-15',
      initiator: '李四',
      content: '月度维护检查',
      status: '已完成'
    },
    {
      id: 3,
      type: '系统更新',
      date: '2023-03-10',
      initiator: '王五',
      content: '操作系统补丁更新',
      status: '已完成'
    },
    {
      id: 4,
      type: '性能监控',
      date: '2023-04-05',
      initiator: '赵六',
      content: '性能指标监控分析',
      status: '进行中'
    },
    {
      id: 5,
      type: '安全检查',
      date: '2023-05-20',
      initiator: '张三',
      content: '安全漏洞扫描',
      status: '已完成'
    },
    {
      id: 6,
      type: '备份验证',
      date: '2023-06-10',
      initiator: '李四',
      content: '数据备份完整性验证',
      status: '进行中'
    },
    {
      id: 7,
      type: '硬件检测',
      date: '2023-06-25',
      initiator: '王五',
      content: '硬件状态检测',
      status: '进行中'
    },
    {
      id: 8,
      type: '环境检查',
      date: '2023-07-01',
      initiator: '赵六',
      content: '机房环境检查',
      status: '已完成'
    }
  ])

  // 跟进记录数据
  const followUpData = reactive([
    {
      id: 1,
      date: '2023-06-15',
      person: '张三',
      content: '检查服务器运行状态，一切正常',
      attachments: 2
    },
    { id: 2, date: '2023-06-14', person: '李四', content: '更新系统补丁', attachments: 1 },
    {
      id: 3,
      date: '2023-06-13',
      person: '王五',
      content: '监控性能指标，CPU使用率正常',
      attachments: 0
    },
    { id: 4, date: '2023-06-12', person: '赵六', content: '备份数据完成', attachments: 3 },
    { id: 5, date: '2023-06-11', person: '张三', content: '清理机房灰尘', attachments: 1 },
    { id: 6, date: '2023-06-10', person: '李四', content: '检查网络连接', attachments: 0 },
    { id: 7, date: '2023-06-09', person: '王五', content: '更换UPS电池', attachments: 2 },
    { id: 8, date: '2023-06-08', person: '赵六', content: '安全扫描完成', attachments: 1 },
    { id: 9, date: '2023-06-07', person: '张三', content: '温度监控正常', attachments: 0 },
    { id: 10, date: '2023-06-06', person: '李四', content: '磁盘空间检查', attachments: 1 },
    { id: 11, date: '2023-06-05', person: '王五', content: '日志分析完成', attachments: 2 },
    { id: 12, date: '2023-06-04', person: '赵六', content: '权限审核', attachments: 0 },
    { id: 13, date: '2023-06-03', person: '张三', content: '配置文件备份', attachments: 1 },
    { id: 14, date: '2023-06-02', person: '李四', content: '服务监控告警处理', attachments: 3 },
    { id: 15, date: '2023-06-01', person: '王五', content: '月度巡检报告', attachments: 5 }
  ])

  // 计算属性
  const completedLifecycleCount = computed(
    () => lifecycleData.filter((item) => item.status === '已完成').length
  )

  const inProgressLifecycleCount = computed(
    () => lifecycleData.filter((item) => item.status === '进行中').length
  )

  const currentMonthFollowUpCount = computed(() => {
    const currentMonth = new Date().getMonth() + 1
    return followUpData.filter((item) => {
      const itemMonth = new Date(item.date).getMonth() + 1
      return itemMonth === currentMonth
    }).length
  })

  // 从属资产计算属性
  const relatedAssets = computed(() => {
    if (assetDetail.value?.parent_id) {
      // 如果当前是配件，显示其他配件
      return (
        assetDetail.value.parent?.children?.filter((child) => child.id !== assetDetail.value?.id) ||
        []
      )
    } else {
      // 如果当前是主设备，显示其配件
      return assetDetail.value?.children || []
    }
  })

  // 资产列表表格列定义
  const assetColumns = ref<ColumnOption[]>([
    {
      prop: 'name',
      label: '资产名称',
      minWidth: 180,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'brand',
      label: '品牌',
      width: 120,
      show: true,
      formatter: (row: Asset) => row.brand?.name || row.brand?.display_name || '-'
    },
    {
      prop: 'model',
      label: '规格型号',
      width: 150,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'serial_number',
      label: '序列号',
      width: 150,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'asset_status',
      label: '状态',
      width: 100,
      show: true,
      formatter: (row: Asset) => {
        const statusMap: Record<string, { label: string; type: string }> = {
          in_use: { label: '使用中', type: 'success' },
          new_unstocked: { label: '新建未入库', type: 'info' },
          pending_check: { label: '待检', type: 'warning' },
          under_repair: { label: '维修中', type: 'danger' },
          scrap_registered: { label: '报废登记', type: 'info' }
        }
        const status = statusMap[row.asset_status || ''] || {
          label: row.asset_status || '-',
          type: 'info'
        }
        return h(ElTag, { type: status.type }, status.label)
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 120,
      fixed: 'right' as const,
      show: true,
      formatter: (row: Asset) => {
        return h(ArtButtonTable, {
          type: 'view',
          onClick: () => handleViewAsset(row)
        })
      }
    }
  ])

  // 生命周期列表表格列定义
  const lifecycleColumns = ref<ColumnOption[]>([
    {
      prop: 'type',
      label: '事项类型',
      width: 120,
      show: true
    },
    {
      prop: 'date',
      label: '日期',
      width: 120,
      show: true
    },
    {
      prop: 'initiator',
      label: '发起人',
      width: 100,
      show: true
    },
    {
      prop: 'content',
      label: '内容',
      minWidth: 200,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      show: true,
      formatter: (row: any) => {
        const statusType = row.status === '已完成' ? 'success' : 'processing'
        return h(ElTag, { type: statusType }, row.status)
      }
    }
  ])

  // 跟进记录列表表格列定义
  const followUpColumns = ref<ColumnOption[]>([
    {
      prop: 'date',
      label: '跟进日期',
      width: 120,
      show: true
    },
    {
      prop: 'person',
      label: '跟进人',
      width: 100,
      show: true
    },
    {
      prop: 'content',
      label: '跟进内容',
      minWidth: 300,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'attachments',
      label: '附件',
      width: 80,
      show: true,
      formatter: (row: any) => {
        if (row.attachments > 0) {
          return h(ElBadge, { value: row.attachments }, () => h('span', '📎'))
        }
        return '-'
      }
    }
  ])

  // 获取字典标签
  const getAssetSourceLabel = (code: string) => {
    const item = assetSourceOptions.value.find((opt) => opt.code === code)
    return item?.value || code
  }

  const getAssetStatusLabel = (code: string) => {
    const item = assetStatusOptions.value.find((opt) => opt.code === code)
    return item?.value || code
  }

  const getAssetConditionLabel = (code: string) => {
    const item = assetConditionOptions.value.find((opt) => opt.code === code)
    return item?.value || code
  }

  // 获取完整地址
  const getFullAddress = () => {
    if (!assetDetail.value?.region_code && !assetDetail.value?.detailed_address) {
      return '-'
    }

    let address = ''
    if (assetDetail.value?.region_code) {
      // TODO: 通过regionStore获取地区名称
      address = assetDetail.value.region_code
    }

    if (assetDetail.value?.detailed_address) {
      address = address
        ? `${address} ${assetDetail.value.detailed_address}`
        : assetDetail.value.detailed_address
    }

    return address
  }

  // 事件处理
  const handleVisibleChange = (visible: boolean) => {
    emit('update:visible', visible)
  }

  const handleClose = () => {
    emit('close')
  }

  const handleViewAsset = (row: Asset) => {
    // 在弹窗内查看其他资产，可以更新当前弹窗内容
    console.log('查看资产:', row.id)
    // TODO: 这里可以实现在当前弹窗内切换到其他资产的详情
  }

  // 加载字典数据
  const loadDictionaries = async () => {
    try {
      const [sourceData, statusData, conditionData] = await Promise.all([
        dictionaryStore.fetchItemsByCode('asset_source'),
        dictionaryStore.fetchItemsByCode('asset_status'),
        dictionaryStore.fetchItemsByCode('asset_condition')
      ])

      assetSourceOptions.value = sourceData || []
      assetStatusOptions.value = statusData || []
      assetConditionOptions.value = conditionData || []
    } catch (error) {
      console.error('加载字典数据失败:', error)
    }
  }

  // 模拟加载资产详情
  const loadAssetDetail = async () => {
    if (!props.assetId) return

    loading.value = true
    try {
      // TODO: 实际项目中这里应该调用API获取资产详情
      // const detail = await getAssetDetail(props.assetId)
      // assetDetail.value = detail

      // 模拟加载延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 根据传入的 assetId 更新mock数据的id
      if (assetDetail.value) {
        assetDetail.value.id = props.assetId
      }
    } catch (error) {
      console.error('加载资产详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 监听弹窗显示状态和资产ID变化
  watch(
    () => [props.visible, props.assetId],
    ([visible, assetId]) => {
      if (visible && assetId) {
        loadDictionaries()
        loadAssetDetail()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped>
  .asset-detail-dialog {
    height: 100%;
    padding: 20px;
    overflow: auto;

    .detail-card {
      height: calc(100vh - 120px);

      :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0;
      }

      :deep(.el-tabs--border-card) {
        display: flex;
        flex-direction: column;
        height: 100%;
        border: none;
        box-shadow: none;

        .el-tabs__header {
          flex-shrink: 0;
          margin: 0;
        }

        .el-tabs__content {
          flex: 1;
          padding: 24px;
          overflow: auto;
        }
      }
    }

    .tab-content {
      .section-block {
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          padding-left: 12px;
          margin: 0 0 20px;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          border-left: 4px solid var(--el-color-primary);
        }
      }

      .stats-row {
        margin-bottom: 20px;
      }

      .charts-row {
        margin-bottom: 20px;

        .chart-card {
          height: 380px;

          .card-header {
            font-weight: 600;
          }

          .card-header-with-filter {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .card-header {
              font-weight: 600;
            }
          }

          :deep(.el-card__body) {
            display: flex;
            flex-direction: column;
            height: calc(100% - 60px);

            .progress-cards {
              display: flex;
              flex-direction: column;
              gap: 16px;
              height: 100%;

              .progress-item {
                flex: 1;
                height: auto !important;
                min-height: 80px;
              }
            }
          }
        }
      }

      .stats-alert {
        margin-bottom: 20px;
      }

      .parent-device-info {
        margin-bottom: 16px;
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        width: 120px;
        font-weight: 500;
      }
    }
  }

  // 全屏弹窗样式优化
  :deep(.el-dialog__header) {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  :deep(.el-dialog__body) {
    height: calc(100vh - 60px);
    padding: 0;
    overflow: hidden;
  }
</style>
