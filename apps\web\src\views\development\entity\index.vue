<template>
  <div class="entity-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
      :showExpand="true"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="showDialog('add')" v-ripple v-if="hasAuth('add')">
            <ElIcon><Plus /></ElIcon>
            新增相关方
          </ElButton>

          <!-- 批量操作按钮组 -->
          <ElButton type="info" @click="handleBatchImport" v-ripple>
            <ElIcon><Upload /></ElIcon>
            批量导入
          </ElButton>

          <ElButton type="info" @click="handleBatchExport" v-ripple>
            <ElIcon><Download /></ElIcon>
            批量导出
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
        @row:selection-change="handleSelectionChange"
      >
        <!-- 相关方类型列 -->
        <template #entity_type="{ row }">
          <ElTag :color="getEntityTypeColor(row.entity_type)" style="color: #fff">
            {{ getEntityTypeName(row.entity_type) }}
          </ElTag>
        </template>

        <!-- 联系人数列 -->
        <template #contacts="{ row }">
          <span>{{ row.contacts_count || 0 }} 个</span>
        </template>

        <!-- 品牌数列 -->
        <template #brands="{ row }">
          <span>{{ row.brands_count || 0 }} 个</span>
        </template>

        <!-- 附件数列 -->
        <template #attachments="{ row }">
          <span>{{ row.attachments_count || 0 }} 个</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="primary" :icon="User" @click="handleContacts(row)" />
            <ArtButtonTable type="warning" :icon="Shop" @click="handleBrands(row)" />
            <ArtButtonTable type="edit" @click="handleEdit(row)" v-if="hasAuth('edit')" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" v-if="hasAuth('delete')" />
          </div>
        </template>
      </ArtTable>

      <!-- 新增/编辑相关方对话框 -->
      <EntityFormDialog
        v-model:visible="dialogVisible"
        v-model:entity="selectedEntity"
        :entity-id="currentEntityId"
        @success="handleDialogSuccess"
      />

      <!-- 联系人管理对话框 -->
      <ContactDialog
        v-model:visible="contactDialogVisible"
        :entityId="currentEntityId"
        :entityName="currentEntityName"
        @refresh="refreshAll"
      />

      <!-- 品牌管理抽屉 -->
      <BrandDrawer
        v-model:visible="brandDrawerVisible"
        :entityId="currentEntityId"
        :entityName="currentEntityName"
        @refresh="refreshAll"
      />

      <!-- 批量导入弹窗 -->
      <BatchImportDialog
        v-model:visible="importDialogVisible"
        type="entity"
        @import-success="handleImportSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  // Vue 核心
  import { ref, onMounted } from 'vue'

  // UI 框架
  import { ElTag, ElMessage, ElMessageBox, ElButton, ElIcon } from 'element-plus'
  import { User, Upload, Download, Plus, Shop } from '@element-plus/icons-vue'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import ContactDialog from './components/ContactDialog.vue'
  import BrandDrawer from './components/BrandDrawer.vue'
  import EntityFormDialog from './components/EntityFormDialog.vue'

  // API
  import { getEntityList, deleteEntity } from '@/api/admin/entity'
  import { exportData, downloadBlob, generateFilename } from '@/api/admin/import-export'
  import { useDictionaryStore } from '@/store/modules/dictionary'

  // 组件
  import { BatchImportDialog } from '@/components/custom/batch-import-dialog'

  // 类型定义
  import type { SearchFormItem } from '@/types'
  import type { Entity, EntitySearchParams } from '@/types/api'

  defineOptions({ name: 'Entity' }) // 定义组件名称，用于 KeepAlive 缓存控制

  // 权限控制
  const { hasAuth } = useAuth()

  // 对话框状态
  const dialogVisible = ref(false)
  const selectedEntity = ref<Entity | null>(null)
  const currentEntityId = ref('')
  const contactDialogVisible = ref(false)
  const brandDrawerVisible = ref(false)
  const currentEntityName = ref('')

  // 选中行数据
  const selectedRows = ref<Entity[]>([])

  // 导入弹窗状态
  const importDialogVisible = ref(false)

  // 选中行状态 - 留作未来使用
  // const hasSelectedRows = computed(() => selectedRows.value.length > 0)
  // const selectedCount = computed(() => selectedRows.value.length)

  // 相关方类型数据
  const entityTypes = ref<any[]>([])

  // 搜索表单状态
  const searchFormState = ref({
    name: '',
    entity_type: null,
    tax_number: '',
    keywords: ''
  })

  // 表格配置
  const tableConfig = {
    rowKey: 'id',
    height: undefined,
    maxHeight: '600px'
  }

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      prop: 'name',
      type: 'input',
      label: '相关方名称',
      placeholder: '请输入相关方名称'
    },
    {
      prop: 'tax_number',
      type: 'input',
      label: '税号',
      placeholder: '请输入税号'
    },
    {
      prop: 'entity_type',
      type: 'select',
      label: '相关方类型',
      config: {
        clearable: true,
        placeholder: '请选择类型'
      },
      options: () =>
        entityTypes.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'keywords',
      type: 'input',
      label: '特征词',
      placeholder: '请输入特征词'
    }
  ]

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<Entity>({
    core: {
      apiFn: async (params: EntitySearchParams) => {
        const response = await getEntityList(params)
        // 直接使用后端返回的数据，不再转换字段名
        const data = response.data
        return {
          records: data,
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10,
        name: '',
        tax_number: '',
        keywords: '',
        entity_type: ''
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 50,
          show: true
        },
        { prop: 'id', label: 'ID', width: 80 },
        { prop: 'name', label: '相关方名称', minWidth: 200 },
        { prop: 'tax_number', label: '税号', width: 180 },
        {
          prop: 'entity_type',
          label: '相关方类型',
          width: 120,
          useSlot: true
        },
        { prop: 'address', label: '单位地址', minWidth: 250 },
        { prop: 'phone', label: '电话号码', width: 150 },
        { prop: 'keywords', label: '特征词', minWidth: 200 },
        {
          prop: 'contacts',
          label: '联系人',
          width: 100,
          useSlot: true
        },
        {
          prop: 'brands',
          label: '品牌数',
          width: 100,
          useSlot: true
        },
        {
          prop: 'attachments',
          label: '附件数',
          width: 100,
          useSlot: true
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 180,
          formatter: (row: any) => {
            return row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : '-'
          }
        },
        {
          prop: 'updated_at',
          label: '更新时间',
          width: 180,
          formatter: (row: any) => {
            return row.updated_at ? formatDate(row.updated_at, 'YYYY-MM-DD HH:mm:ss') : '-'
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 240,
          fixed: 'right',
          useSlot: true
        }
      ]
    }
  })

  // 获取相关方类型名称
  const getEntityTypeName = (type: string) => {
    const entityType = entityTypes.value.find((item) => item.code === type)
    return entityType?.value || type
  }

  // 获取相关方类型颜色
  const getEntityTypeColor = (type: string) => {
    const entityType = entityTypes.value.find((item) => item.code === type)
    return entityType?.color || '#909399'
  }

  // 加载相关方类型
  const loadEntityTypes = async () => {
    const dictionaryStore = useDictionaryStore()
    const items = await dictionaryStore.fetchItemsByCode('entity_type')
    entityTypes.value = items.map((item) => ({
      code: item.code,
      value: item.value,
      color: item.color || '#909399'
    }))
  }

  // 处理搜索
  const handleSearch = () => {
    // 直接使用搜索表单的值
    Object.assign(searchState, searchFormState.value)
    searchData()
  }

  // 处理重置
  const handleReset = () => {
    searchFormState.value = {
      name: '',
      entity_type: null,
      tax_number: '',
      keywords: ''
    }
    resetSearch()
  }

  // 显示对话框
  const showDialog = (type: 'add' | 'edit', row?: Entity) => {
    if (type === 'edit' && row) {
      selectedEntity.value = row
      currentEntityId.value = String(row.id)
    } else {
      selectedEntity.value = null
      currentEntityId.value = ''
    }
    dialogVisible.value = true
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    const isAdd = !selectedEntity.value?.id
    if (isAdd) {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 编辑行
  const handleEdit = (row: Entity) => {
    showDialog('edit', row)
  }

  // 删除行
  const handleDelete = async (row: Entity) => {
    try {
      const contactCount = row.contacts_count || 0
      const attachmentCount = row.attachments_count || 0

      let confirmMessage = `确定要删除相关方"${row.name}"吗？`
      if (contactCount > 0 || attachmentCount > 0) {
        confirmMessage += '\n\n此操作将同时删除：'
        if (contactCount > 0) {
          confirmMessage += `\n• ${contactCount} 个联系人`
        }
        if (attachmentCount > 0) {
          confirmMessage += `\n• ${attachmentCount} 个附件关联`
        }
      }

      await ElMessageBox.confirm(confirmMessage, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        distinguishCancelAndClose: true
      })

      await deleteEntity(String(row.id))
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 管理联系人
  const handleContacts = (row: Entity) => {
    currentEntityId.value = String(row.id) // 确保转换为字符串
    currentEntityName.value = row.name
    contactDialogVisible.value = true
  }

  // 管理品牌
  const handleBrands = (row: Entity) => {
    currentEntityId.value = String(row.id) // 确保转换为字符串
    currentEntityName.value = row.name
    brandDrawerVisible.value = true
  }

  // 选中行变化事件
  const handleSelectionChange = (selection: Entity[]) => {
    selectedRows.value = selection
  }

  // 批量导入
  const handleBatchImport = () => {
    importDialogVisible.value = true
  }

  // 批量导出
  const handleBatchExport = async () => {
    try {
      ElMessage.info('正在导出数据，请稍候...')
      const blob = await exportData('entity', searchFormState.value)
      const filename = generateFilename('entity', '数据')
      downloadBlob(blob, filename)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请重试')
    }
  }

  // 导入成功处理
  const handleImportSuccess = () => {
    refreshAll() // 刷新列表
  }

  // 初始化
  onMounted(() => {
    loadEntityTypes()
  })
</script>

<style lang="scss" scoped>
  .entity-page {
    height: 100%;
    padding: 20px;

    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  // 批量操作按钮组样式
  :deep(.art-table-header) {
    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .upload-demo {
    width: 100%;
  }
</style>
