import request from '@/utils/http'
import type { RegionTreeItem } from '@/types/api'

// ========== 地区管理 ==========

/**
 * 获取完整地区树（省市区三级）
 */
export const getRegionTree = (): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: '/api/admin/regions/tree'
  })
}

/**
 * 获取省份列表
 */
export const getProvinces = (): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: '/api/admin/regions/provinces'
  })
}

/**
 * 获取指定省份下的城市列表
 */
export const getCities = (provinceCode: string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/api/admin/regions/${provinceCode}/cities`
  })
}

/**
 * 获取指定城市下的区县列表
 */
export const getDistricts = (cityCode: string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/api/admin/regions/${cityCode}/districts`
  })
}

/**
 * 根据地区编码获取地区信息
 */
export const getRegionInfo = (code: string): Promise<RegionTreeItem> => {
  return request.get<RegionTreeItem>({
    url: `/api/admin/regions/${code}`
  })
}

/**
 * 根据地区编码获取完整地区路径（省/市/区）
 */
export const getRegionPath = (code: string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/api/admin/regions/path/${code}`
  })
}

/**
 * 获取指定地区的子级地区
 */
export const getRegionChildren = (code: string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/api/admin/regions/${code}/children`
  })
}

/**
 * 搜索地区
 */
export const searchRegions = (keyword: string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: '/api/admin/regions/search',
    params: { keyword }
  })
}
