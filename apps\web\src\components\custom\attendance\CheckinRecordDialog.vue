<template>
  <ElDialog
    v-model="visible"
    :title="`${config?.attachable?.name || '考勤配置'} - 打卡记录`"
    width="90%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <!-- 搜索筛选 -->
    <div class="search-section">
      <ElForm :model="searchForm" :inline="true" label-width="80px" class="search-form">
        <ElFormItem label="打卡日期">
          <ElDatePicker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="handleDateRangeChange"
          />
        </ElFormItem>
        <ElFormItem label="参与人员">
          <ElSelect
            v-model="searchForm.user_id"
            placeholder="请选择人员"
            clearable
            style="width: 150px"
          >
            <ElOption
              v-for="user in config?.users || []"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="打卡状态">
          <ElSelect
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <ElOption label="正常" :value="0" />
            <ElOption label="异常" :value="2" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <!-- 记录表格 -->
    <ElTable :data="tableData" :loading="loading" stripe height="500" style="width: 100%">
      <ElTableColumn prop="id" label="ID" width="80" />

      <ElTableColumn label="打卡人员" width="150">
        <template #default="{ row }">
          <div class="user-info">
            <ElAvatar :src="row.user_avatar" :size="30" :icon="UserFilled" class="user-avatar" />
            <span class="user-name">{{ row.user_name }}</span>
          </div>
        </template>
      </ElTableColumn>

      <ElTableColumn label="打卡时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.checkin_time, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </ElTableColumn>

      <ElTableColumn label="打卡状态" width="100">
        <template #default="{ row }">
          <ElTag :type="getStatusType(row.status)">
            {{ row.status_label }}
          </ElTag>
        </template>
      </ElTableColumn>

      <ElTableColumn label="打卡地点" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.location_address }}
        </template>
      </ElTableColumn>

      <ElTableColumn label="距离" width="100">
        <template #default="{ row }">
          <span v-if="row.distance !== undefined"> {{ row.distance }}米 </span>
          <span v-else>-</span>
        </template>
      </ElTableColumn>

      <ElTableColumn label="打卡附件" width="150">
        <template #default="{ row }">
          <div v-if="row.attachments && row.attachments.length > 0" class="attachment-thumbnails">
            <!-- 显示最多3个缩略图 -->
            <div class="thumbnail-list">
              <ElImage
                v-for="(attachment, index) in getVisibleAttachments(row.attachments)"
                :key="attachment.id || index"
                :src="getAttachmentThumbnail(attachment)"
                :preview-src-list="getImageUrls(row.attachments)"
                fit="cover"
                class="attachment-thumbnail"
                lazy
              />
              <!-- 如果附件数量超过3个，显示 +N -->
              <div v-if="row.attachments.length > 3" class="more-indicator">
                +{{ row.attachments.length - 3 }}
              </div>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <ElPagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">关闭</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'CheckinRecordDialog' })

  // Vue 核心
  import { ref, reactive, watch } from 'vue'

  // UI 框架
  import {
    ElButton,
    ElMessage,
    ElTag,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElAvatar,
    ElImage
  } from 'element-plus'
  import { UserFilled } from '@element-plus/icons-vue'
  import type { TagProps } from 'element-plus'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // API
  import { getCheckinRecords } from '@/api/admin/attendance'

  // 类型定义
  import type {
    CheckinConfig,
    CheckinRecord,
    CheckinRecordSearchParams
  } from '@/types/api/attendance'

  // Props
  const props = defineProps<{
    config?: CheckinConfig | null
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 数据状态
  const loading = ref(false)
  const tableData = ref<CheckinRecord[]>([])

  // 搜索表单
  const searchForm = reactive({
    user_id: undefined as number | undefined,
    status: undefined as number | undefined,
    date_start: undefined as string | undefined,
    date_end: undefined as string | undefined
  })

  // 日期范围
  const dateRange = ref<[string, string]>()

  // 分页
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  })

  // 监听弹窗显示状态
  watch(visible, (newVal) => {
    if (newVal && props.config) {
      loadRecords()
    }
  })

  // 监听配置变化
  watch(
    () => props.config,
    (newConfig) => {
      if (newConfig && visible.value) {
        resetSearch()
        loadRecords()
      }
    }
  )

  // 加载打卡记录
  const loadRecords = async () => {
    if (!props.config) return

    loading.value = true
    try {
      const params: CheckinRecordSearchParams = {
        config_id: props.config.id,
        page: pagination.current,
        per_page: pagination.size
      }

      // 添加搜索条件
      if (searchForm.user_id) {
        params.user_id = searchForm.user_id
      }
      if (searchForm.status !== undefined) {
        params.status = searchForm.status as any
      }
      if (searchForm.date_start) {
        params.date_start = searchForm.date_start
      }
      if (searchForm.date_end) {
        params.date_end = searchForm.date_end
      }

      const response = await getCheckinRecords(params)
      tableData.value = response.data || []
      pagination.total = response.meta?.total || 0
    } catch (error) {
      console.error('加载打卡记录失败:', error)
      ElMessage.error('加载打卡记录失败')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dates: [string, string] | null) => {
    if (dates && Array.isArray(dates)) {
      searchForm.date_start = dates[0]
      searchForm.date_end = dates[1]
    } else {
      searchForm.date_start = undefined
      searchForm.date_end = undefined
    }
  }

  // 处理搜索
  const handleSearch = () => {
    pagination.current = 1
    loadRecords()
  }

  // 处理重置
  const handleReset = () => {
    searchForm.user_id = undefined
    searchForm.status = undefined
    searchForm.date_start = undefined
    searchForm.date_end = undefined
    dateRange.value = undefined
    pagination.current = 1
    loadRecords()
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.user_id = undefined
    searchForm.status = undefined
    searchForm.date_start = undefined
    searchForm.date_end = undefined
    dateRange.value = undefined
    pagination.current = 1
    pagination.size = 20
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.size = size
    pagination.current = 1
    loadRecords()
  }

  const handleCurrentChange = (current: number) => {
    pagination.current = current
    loadRecords()
  }

  // 获取状态标签类型
  const getStatusType = (status: number): TagProps['type'] => {
    switch (status) {
      case 0:
        return 'success'
      case 2:
        return 'danger'
      default:
        return 'info'
    }
  }

  // 获取可见的附件（最多显示3个）
  const getVisibleAttachments = (attachments: any[]) => {
    return attachments?.slice(0, 3) || []
  }

  // 获取附件缩略图
  const getAttachmentThumbnail = (attachment: any) => {
    // 如果是图片类型，返回图片URL
    if (attachment.file_type?.startsWith('image/')) {
      return attachment.file_url || attachment.url
    }
    // 非图片返回空字符串
    return ''
  }

  // 获取所有图片URL（用于预览）
  const getImageUrls = (attachments: any[]) => {
    return (
      attachments
        ?.filter((item) => item.file_type?.startsWith('image/'))
        ?.map((item) => item.file_url || item.url)
        ?.filter(Boolean) || []
    )
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    resetSearch()
    tableData.value = []
  }
</script>

<style lang="scss" scoped>
  .search-section {
    padding: 16px;
    margin-bottom: 20px;
    background-color: var(--el-bg-color-page);
    border-radius: 6px;

    .search-form {
      margin: 0;

      :deep(.el-form-item) {
        margin-right: 20px;
        margin-bottom: 0;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      margin-right: 8px;
    }

    .user-name {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  .attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .attachment-item {
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;

      .attachment-preview {
        position: relative;

        .file-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 150px;
          color: var(--el-text-color-placeholder);
          background-color: var(--el-fill-color-light);
        }
      }

      .attachment-info {
        padding: 12px;

        .file-name {
          margin: 0 0 8px;
          overflow: hidden;
          font-size: 14px;
          color: var(--el-text-color-primary);
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-actions {
          text-align: right;
        }
      }
    }
  }

  .attachment-thumbnails {
    .thumbnail-list {
      display: flex;
      gap: 4px;
      align-items: center;

      .attachment-thumbnail {
        width: 32px;
        height: 32px;
        cursor: pointer;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 4px;
      }

      .more-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        font-size: 11px;
        color: var(--el-text-color-secondary);
        background-color: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 4px;
      }
    }
  }
</style>
