<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\OperationLog;

echo "验证最新的文件上传操作日志:\n";
$latestLog = OperationLog::latest()->first();

if ($latestLog) {
    echo "ID: {$latestLog->id}\n";
    echo "操作类型: {$latestLog->operation_type}\n";
    echo "操作描述: {$latestLog->operation_description}\n";
    echo "目标类型: {$latestLog->target_type}\n";
    echo "目标名称: {$latestLog->target_name}\n";
    echo "路径: {$latestLog->path}\n";
    echo "用户: {$latestLog->user_name}\n";
    echo "时间: {$latestLog->created_at}\n";

    // 检查是否符合预期
    if ($latestLog->operation_type === 'upload' &&
        $latestLog->operation_description === '上传文件' &&
        str_contains($latestLog->path, 'attachments/upload')) {
        echo "\n✅ 操作日志记录正确！\n";
        echo "✅ 操作类型: upload\n";
        echo "✅ 操作描述: 上传文件\n";
        echo "✅ 路径包含: attachments/upload\n";
    } else {
        echo "\n❌ 操作日志记录不正确\n";
        echo "期望: operation_type = 'upload', operation_description = '上传文件'\n";
        echo "实际: operation_type = '{$latestLog->operation_type}', operation_description = '{$latestLog->operation_description}'\n";
    }
} else {
    echo "❌ 没有找到操作日志记录\n";
}
