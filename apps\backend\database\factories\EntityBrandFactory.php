<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EntityBrand>
 */
class EntityBrandFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $medicalBrands = [
            'GE Healthcare',
            'Siemens Healthineers', 
            'Philips Healthcare',
            '迈瑞医疗',
            '联影医疗',
            '东软医疗',
            '奥林巴斯',
            '日立医疗',
            '强生医疗',
            '美敦力',
            '雅培',
            '罗氏诊断',
            '贝克曼库尔特',
            '索尼医疗',
            '理邦仪器'
        ];

        return [
            'entity_id' => \App\Models\Entity::factory(),
            'name' => $this->faker->randomElement($medicalBrands),
            'description' => $this->faker->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 999),
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
}
