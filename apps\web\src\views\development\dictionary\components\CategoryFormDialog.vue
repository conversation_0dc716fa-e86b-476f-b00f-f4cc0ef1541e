<template>
  <ElDialog
    v-model="visible"
    :title="isEdit ? '编辑字典分类' : '新增字典分类'"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <ElFormItem label="分类编码" prop="code">
        <ElInput
          v-model="formData.code"
          placeholder="请输入分类编码，如：yes_or_no"
          :disabled="isEdit"
        />
      </ElFormItem>
      <ElFormItem label="分类名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入分类名称" />
      </ElFormItem>
      <ElFormItem label="排序" prop="sort">
        <ElInputNumber v-model="formData.sort" :min="0" :max="9999" />
      </ElFormItem>
      <ElFormItem label="状态" prop="enabled">
        <ElSwitch v-model="formData.enabled" />
      </ElFormItem>
      <ElFormItem label="描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" @click="handleConfirm" :loading="loading">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createDictionaryCategory, updateDictionaryCategory } from '@/api/admin/dictionary'
  import type { DictionaryCategory, DictionaryCategoryForm } from '@/types/api'

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>('visible', { default: false })
  const category = defineModel<DictionaryCategory | null>('category', { default: null })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 计算属性判断是否编辑模式
  const isEdit = computed(() => !!category.value?.id)

  // 表单实例
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = ref<DictionaryCategoryForm>({
    id: '',
    code: '',
    name: '',
    description: '',
    enabled: true,
    sort: 0
  })

  // 表单验证规则
  const formRules: FormRules = {
    code: [
      { required: true, message: '请输入分类编码', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
        message: '编码只能包含字母、数字和下划线，且不能以数字开头',
        trigger: 'blur'
      }
    ],
    name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
  }

  // 监听 category 变化，更新表单数据
  watch(
    () => category.value,
    (newVal) => {
      if (newVal) {
        // 编辑模式，填充数据
        formData.value = {
          id: newVal.id,
          code: newVal.code,
          name: newVal.name,
          description: newVal.description || '',
          enabled: newVal.enabled,
          sort: newVal.sort || 0
        }
      } else {
        // 新增模式，重置数据
        formData.value = {
          id: '',
          code: '',
          name: '',
          description: '',
          enabled: true,
          sort: 0
        }
      }
    },
    { immediate: true }
  )

  // 处理取消
  const handleCancel = () => {
    visible.value = false
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    await formRef.value.validate()

    loading.value = true
    try {
      if (isEdit.value) {
        await updateDictionaryCategory(formData.value.id!, formData.value)
        ElMessage.success('修改成功')
      } else {
        await createDictionaryCategory(formData.value)
        ElMessage.success('新增成功')
      }

      visible.value = false
      emit('success')
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭后的处理
  const handleClosed = () => {
    // 重置表单
    formRef.value?.resetFields()
    // 清空编辑数据
    category.value = null
  }
</script>
