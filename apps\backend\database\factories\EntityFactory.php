<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Entity>
 */
class EntityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $entityTypes = ['manufacturer', 'supplier', 'service_provider', 'end_customer'];
        $medicalCompanies = [
            '北京康泰医疗设备有限公司',
            '上海华健医疗科技股份有限公司',
            '深圳迈瑞生物医疗电子股份有限公司',
            '江苏鱼跃医疗设备股份有限公司',
            '广东安健科技股份有限公司',
            '山东威高集团医疗器械股份有限公司',
            '北京万东医疗科技股份有限公司',
            '上海联影医疗科技股份有限公司',
            '苏州奥普拓激光科技有限公司',
            '北京理邦精密仪器股份有限公司'
        ];
        
        $hospitalNames = [
            '北京协和医院',
            '上海交通大学医学院附属瑞金医院',
            '四川大学华西医院',
            '复旦大学附属中山医院',
            '中南大学湘雅医院',
            '山东大学齐鲁医院',
            '华中科技大学同济医学院附属同济医院',
            '中国医学科学院肿瘤医院',
            '中山大学附属第一医院',
            '西安交通大学第一附属医院'
        ];

        return [
            'name' => $this->faker->randomElement(array_merge($medicalCompanies, $hospitalNames)),
            'tax_number' => $this->faker->numerify('91##########0####X'),
            'entity_type' => $this->faker->randomElement($entityTypes),
            'address' => $this->faker->address(),
            'phone' => $this->faker->phoneNumber(),
            'keywords' => $this->faker->words(3, true),
            'remark' => $this->faker->sentence(),
            'created_by' => 1,
            'updated_by' => 1,
        ];
    }
}
