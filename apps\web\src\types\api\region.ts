// 地区管理相关类型定义

// 地区实体
export interface Region {
  id: number
  pid: number
  deep: number
  name: string
  pinyin_prefix: string
  pinyin: string
  ext_id: string
  ext_name: string
  created_at: string
  updated_at: string
}

// 树形结构地区项
export interface RegionTreeItem {
  value: string // ext_id 地区代码
  label: string // ext_name 地区名称
  id: number
  pid: number
  deep: number
  children?: RegionTreeItem[]
  isLeaf?: boolean
}

// 地区搜索结果
export interface RegionSearchItem {
  value: string // ext_id 地区代码
  label: string // ext_name 地区名称
  path: RegionPathItem[]
  full_name: string // 完整路径名称
  deep: number
  pinyin: string
}

// 地区路径项
export interface RegionPathItem {
  code: string // ext_id
  name: string // ext_name
}

// 地区路径响应
export interface RegionPathResponse {
  path: RegionPathItem[]
  codes: string[]
  names: string[]
  full_name: string
}

// 地区选择器的值类型
export type RegionValue = string | string[]

// 级联选择器选项
export interface CascaderOption {
  value: string
  label: string
  children?: CascaderOption[]
  isLeaf?: boolean
}
