<?php

namespace App\Services\Attachment\Drivers;

use App\Services\Attachment\Contracts\StorageDriver;
use Illuminate\Http\UploadedFile;

class AliOssDriver implements StorageDriver
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * 本地上传文件（OSS驱动不支持服务端上传）
     */
    public function store(UploadedFile $file, string $path): string
    {
        throw new \BadMethodCallException('AliOSS driver does not support server-side upload. Use STS credentials instead.');
    }

    /**
     * 直接上传文件到OSS
     *
     * @param  string|resource|\Illuminate\Http\UploadedFile  $file  要上传的文件
     * @param  string  $path  OSS中的存储路径
     */
    public function uploadFile($file, string $path): string
    {
        $ossClient = new \OSS\OssClient(
            $this->config['access_key_id'],
            $this->config['access_key_secret'],
            $this->config['endpoint'],
            $this->config['is_cname'] ?? false
        );

        if ($file instanceof UploadedFile) {
            $filePath = $file->getRealPath();
        } elseif (is_resource($file)) {
            $content = stream_get_contents($file);
            $ossClient->putObject($this->config['bucket'], $path, $content);

            return $path;
        } else {
            $filePath = $file;
        }

        // 对于大文件使用分片上传
        $fileSize = filesize($filePath);
        if ($fileSize > 5 * 1024 * 1024) { // 大于5MB使用分片上传
            return $this->multipartUpload($ossClient, $filePath, $path);
        }

        // 小文件直接上传
        $ossClient->uploadFile($this->config['bucket'], $path, $filePath);

        return $path;
    }

    /**
     * 分片上传大文件
     */
    protected function multipartUpload(\OSS\OssClient $ossClient, string $filePath, string $path): string
    {
        // 初始化分片上传
        $uploadId = $ossClient->initiateMultipartUpload($this->config['bucket'], $path);

        // 分片大小，建议不小于100KB
        $partSize = 1 * 1024 * 1024; // 1MB
        $uploadFileSize = filesize($filePath);

        $uploadParts = [];
        $uploadPosition = 0;
        $partNumber = 1;

        while ($uploadPosition < $uploadFileSize) {
            $partContent = file_get_contents(
                $filePath,
                false,
                null,
                $uploadPosition,
                min($partSize, $uploadFileSize - $uploadPosition)
            );

            $responseUploadPart = $ossClient->uploadPart(
                $this->config['bucket'],
                $path,
                $uploadId,
                $partContent,
                $partNumber
            );

            $uploadParts[] = [
                'PartNumber' => $partNumber,
                'ETag' => $responseUploadPart['ETag'],
            ];

            $uploadPosition += $partSize;
            $partNumber++;
        }

        // 完成分片上传
        $ossClient->completeMultipartUpload(
            $this->config['bucket'],
            $path,
            $uploadId,
            $uploadParts
        );

        return $path;
    }

    /**
     * 验证回调签名
     */
    public function validateCallback(array $data): bool
    {
        // 获取回调头部信息
        $authorizationBase64 = request()->header('authorization', '');
        $pubKeyUrlBase64 = request()->header('x-oss-pub-key-url', '');

        if (! $authorizationBase64 || ! $pubKeyUrlBase64) {
            return false;
        }

        // 获取公钥
        $pubKey = base64_decode(urldecode($pubKeyUrlBase64));
        $pubKey = file_get_contents($pubKey);
        if (! $pubKey) {
            return false;
        }

        // 获取回调内容
        $body = file_get_contents('php://input');
        $path = request()->getRequestUri();
        $queryString = request()->getQueryString();
        if ($queryString) {
            $path .= '?'.$queryString;
        }

        $authStr = '';
        $authStr .= 'POST\n';
        $authStr .= 'application/json\n';
        $authStr .= request()->header('content-md5', '')."\n";
        $authStr .= request()->header('content-type', '')."\n";
        $authStr .= request()->header('date', '')."\n";
        $authStr .= $path;

        // 验证签名
        $authorization = base64_decode($authorizationBase64);
        $verify = openssl_verify($authStr, $authorization, $pubKey, OPENSSL_ALGO_MD5);

        return $verify === 1;
    }

    /**
     * 获取存储类型
     */
    public function getType(): string
    {
        return 'alioss';
    }

    /**
     * 检查文件是否存在
     */
    public function exists(string $path): bool
    {
        $ossClient = new \OSS\OssClient(
            $this->config['access_key_id'],
            $this->config['access_key_secret'],
            $this->config['endpoint'],
            $this->config['is_cname'] ?? false
        );

        return $ossClient->doesObjectExist($this->config['bucket'], $path);
    }

    /**
     * 删除文件
     */
    public function delete(string $path): bool
    {
        $ossClient = new \OSS\OssClient(
            $this->config['access_key_id'],
            $this->config['access_key_secret'],
            $this->config['endpoint'],
            $this->config['is_cname'] ?? false
        );

        try {
            $ossClient->deleteObject($this->config['bucket'], $path);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取文件访问URL
     */
    public function url(string $path): string
    {
        if (isset($this->config['cdn_domain']) && $this->config['cdn_domain']) {
            $scheme = $this->config['use_ssl'] ? 'https' : 'http';

            return $scheme.'://'.$this->config['cdn_domain'].'/'.$path;
        }

        $scheme = $this->config['use_ssl'] ? 'https' : 'http';
        $endpoint = $this->config['endpoint'];
        $bucket = $this->config['bucket'];

        if (isset($this->config['is_cname']) && $this->config['is_cname']) {
            return $scheme.'://'.$endpoint.'/'.$path;
        }

        return $scheme.'://'.$bucket.'.'.$endpoint.'/'.$path;
    }

    /**
     * 生成带签名的私有文件访问URL
     */
    public function getSignedUrl(string $path, int $expires = 3600): string
    {
        $resource = '/'.$this->config['bucket'].'/'.ltrim($path, '/');
        $expires = time() + $expires;

        $stringToSign = "GET\n\n\n{$expires}\n{$resource}";
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->config['access_key_secret'], true));

        $url = $this->url($path);
        $params = [
            'OSSAccessKeyId' => $this->config['access_key_id'],
            'Expires' => $expires,
            'Signature' => $signature,
        ];

        return $url.'?'.http_build_query($params);
    }

    /**
     * 获取上传凭证（AliOSS使用STS凭证）
     *
     * @param  array  $params  上传参数
     *
     * @throws \RuntimeException
     */
    public function getUploadCredentials(array $params = []): array
    {
        // AliOSS驱动使用STS凭证，需要通过 AliStsDriver 处理
        throw new \RuntimeException('AliOSS驱动请使用STS凭证上传，请调用 AliStsDriver::getSTSCredentials 方法');
    }
}
