<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CheckinRecordRequest;
use App\Models\CheckinRecord;
use App\Services\CheckinService;
use Illuminate\Http\Request;
use App\Http\Resources\Admin\CheckinRecordResource;

/**
 * @group 考勤记录
 */
class CheckinRecordController extends Controller
{
    public function __construct(private CheckinService $checkinService)
    {
    }

    /**
     * 获取打卡记录列表
     *
     * @queryParam config_id int 打卡配置ID Example: 1
     * @queryParam user_id int 用户ID Example: 1
     * @queryParam status int 状态(0-正常,1-异常) Example: 0
     * @queryParam date_start string 开始日期 Example: 2025-08-01
     * @queryParam date_end string 结束日期 Example: 2025-08-31
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页记录数 Example: 15
     */
    public function index(Request $request)
    {
        $records = $this->checkinService->getRecords($request->all());
        return CheckinRecordResource::collection($records);
    }

    /**
     * 打卡操作
     *
     * @bodyParam checkin_config_id int required 打卡配置ID Example: 2
     * @bodyParam location string 打卡地点 Example: 公司大门
     * @bodyParam latitude decimal 打卡位置经度 Example: 39.9042
     * @bodyParam longitude decimal 打卡位置纬度 Example: 116.4074
     * @bodyParam location_range int 打卡位置范围 Example: 50
     * @bodyParam attachment_id int 打卡照片附件ID Example: 1
     * @bodyParam content string 打卡备注 Example: 正常打卡
     * @bodyParam ip_address string IP地址 Example: 127.0.0.1
     */
    public function store(CheckinRecordRequest $request)
    {
        $record = $this->checkinService->checkin($request->validated());
        return new CheckinRecordResource($record);
    }

    /**
     * 获取打卡记录详情
     *
     * @urlParam id int required 记录ID Example: 1
     */
    public function show($id)
    {
        $record = CheckinRecord::with(['user:id,nickname', 'config:id', 'attachment'])
            ->findOrFail($id);
        return new CheckinRecordResource($record);
    }
}
